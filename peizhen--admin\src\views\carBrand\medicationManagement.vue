<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<!-- 用药记录管理 -->
			<el-tab-pane label="用药记录管理" name="medication">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchMedication" placeholder="请输入患者姓名"
							v-model="medicationSearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>药物类型：</span>
						<el-select style="width: 200px;" v-model="medicationSearch.medicationType" placeholder="请选择药物类型">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in medicationTypeOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>用药状态：</span>
						<el-select style="width: 200px;" v-model="medicationSearch.medicationStatus" placeholder="请选择用药状态">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in medicationStatusOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchMedication">
						查询
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetMedication">
						重置
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addMedication()">
						添加用药记录
					</el-button>
				</div>
				
				<el-table v-loading="medicationLoading" :data="medicationData.records" border>
					<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
					<el-table-column prop="medicationName" label="药物名称" width="150"></el-table-column>
					<el-table-column prop="brandName" label="商品名" width="120"></el-table-column>
					<el-table-column prop="medicationType" label="药物类型" width="100">
						<template slot-scope="scope">
							<span>{{ getMedicationTypeText(scope.row.medicationType) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="dosage" label="剂量" width="100"></el-table-column>
					<el-table-column prop="frequency" label="频次" width="100"></el-table-column>
					<el-table-column prop="medicationStatus" label="用药状态" width="100">
						<template slot-scope="scope">
							<el-tag :type="getMedicationStatusType(scope.row.medicationStatus)">
								{{ getMedicationStatusText(scope.row.medicationStatus) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
					<el-table-column prop="endDate" label="结束日期" width="120"></el-table-column>
					<el-table-column label="操作" width="250">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editMedication(scope.row)">编辑</el-button>
							<el-button size="mini" type="warning" @click="discontinueMedication(scope.row)" 
								v-if="scope.row.medicationStatus == '1'">停药</el-button>
							<el-button size="mini" type="danger" @click="deleteMedication(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleMedicationSizeChange" @current-change="handleMedicationCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="medicationPage.limit" :current-page="medicationPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="medicationData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
			
			<!-- 生命体征管理 -->
			<el-tab-pane label="生命体征管理" name="vitalsigns">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchVitalSigns" placeholder="请输入患者姓名"
							v-model="vitalSignsSearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>测量日期：</span>
						<el-date-picker v-model="vitalSignsSearch.dateRange" type="daterange" 
							range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
							style="width: 240px;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
						</el-date-picker>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchVitalSigns">
						查询
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetVitalSigns">
						重置
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addVitalSigns()">
						添加生命体征
					</el-button>
				</div>
				
				<el-table v-loading="vitalSignsLoading" :data="vitalSignsData.records" border>
					<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
					<el-table-column prop="measurementDate" label="测量日期" width="120"></el-table-column>
					<el-table-column prop="systolicPressure" label="收缩压" width="80"></el-table-column>
					<el-table-column prop="diastolicPressure" label="舒张压" width="80"></el-table-column>
					<el-table-column prop="heartRate" label="心率" width="80"></el-table-column>
					<el-table-column prop="temperature" label="体温" width="80"></el-table-column>
					<el-table-column prop="respiratoryRate" label="呼吸频率" width="100"></el-table-column>
					<el-table-column prop="oxygenSaturation" label="血氧饱和度" width="120"></el-table-column>
					<el-table-column prop="abnormalIndicators" label="异常指标" width="150">
						<template slot-scope="scope">
							<el-tag type="danger" v-if="scope.row.abnormalIndicators">
								{{ scope.row.abnormalIndicators }}
							</el-tag>
							<span v-else>正常</span>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="200">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editVitalSigns(scope.row)">编辑</el-button>
							<el-button size="mini" type="danger" @click="deleteVitalSigns(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleVitalSignsSizeChange" @current-change="handleVitalSignsCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="vitalSignsPage.limit" :current-page="vitalSignsPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="vitalSignsData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
			
			<!-- 检查结果管理 -->
			<el-tab-pane label="检查结果管理" name="testresult">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchTestResult" placeholder="请输入患者姓名"
							v-model="testResultSearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>检查类型：</span>
						<el-select style="width: 200px;" v-model="testResultSearch.testType" placeholder="请选择检查类型">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in testTypeOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>结果状态：</span>
						<el-select style="width: 200px;" v-model="testResultSearch.resultStatus" placeholder="请选择结果状态">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in testResultStatusOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchTestResult">
						查询
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetTestResult">
						重置
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addTestResult()">
						添加检查结果
					</el-button>
				</div>
				
				<el-table v-loading="testResultLoading" :data="testResultData.records" border>
					<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
					<el-table-column prop="testName" label="检查项目" width="150"></el-table-column>
					<el-table-column prop="testType" label="检查类型" width="100">
						<template slot-scope="scope">
							<span>{{ getTestTypeText(scope.row.testType) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="testDate" label="检查日期" width="120"></el-table-column>
					<el-table-column prop="testResult" label="检查结果" width="150"></el-table-column>
					<el-table-column prop="resultStatus" label="结果状态" width="100">
						<template slot-scope="scope">
							<el-tag :type="getTestResultStatusType(scope.row.resultStatus)">
								{{ getTestResultStatusText(scope.row.resultStatus) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="testHospital" label="检查医院" width="150"></el-table-column>
					<el-table-column prop="isUrgent" label="是否紧急" width="80">
						<template slot-scope="scope">
							<el-tag :type="scope.row.isUrgent == 1 ? 'danger' : 'success'">
								{{ scope.row.isUrgent == 1 ? '是' : '否' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="200">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editTestResult(scope.row)">编辑</el-button>
							<el-button size="mini" type="danger" @click="deleteTestResult(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleTestResultSizeChange" @current-change="handleTestResultCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="testResultPage.limit" :current-page="testResultPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="testResultData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
export default {
	data() {
		return {
			activeName: 'medication',
			
			// 数据字典选项
			medicationTypeOptions: [],
			medicationStatusOptions: [],
			testTypeOptions: [],
			testResultStatusOptions: [],
			
			// 用药记录
			medicationSearch: {
				patientName: '',
				medicationType: '',
				medicationStatus: ''
			},
			medicationPage: {
				page: 1,
				limit: 10
			},
			medicationData: {},
			medicationLoading: false,
			
			// 生命体征
			vitalSignsSearch: {
				patientName: '',
				dateRange: []
			},
			vitalSignsPage: {
				page: 1,
				limit: 10
			},
			vitalSignsData: {},
			vitalSignsLoading: false,
			
			// 检查结果
			testResultSearch: {
				patientName: '',
				testType: '',
				resultStatus: ''
			},
			testResultPage: {
				page: 1,
				limit: 10
			},
			testResultData: {},
			testResultLoading: false
		}
	},
	methods: {
		// 标签页切换
		handleClick(tab) {
			switch(tab.name) {
				case 'medication':
					this.loadMedicationData()
					break
				case 'vitalsigns':
					this.loadVitalSignsData()
					break
				case 'testresult':
					this.loadTestResultData()
					break
			}
		},
		
		// 加载数据字典
		loadDictData() {
			const dictTypes = ['medication_type', 'medication_status', 'test_type', 'test_result_status']

			dictTypes.forEach(type => {
				this.$http({
					url: this.$http.adornUrl('sys/dict/list'),
					method: 'get',
					params: this.$http.adornParams({ type: type })
				}).then(({data}) => {
					if (data && data.code === 0) {
						// 转换数据格式以适配现有的 sys_dict 表结构
						const options = data.data.map(item => ({
							dictValue: item.code,
							dictLabel: item.value || item.name
						}))

						switch(type) {
							case 'medication_type':
								this.medicationTypeOptions = options
								break
							case 'medication_status':
								this.medicationStatusOptions = options
								break
							case 'test_type':
								this.testTypeOptions = options
								break
							case 'test_result_status':
								this.testResultStatusOptions = options
								break
						}
					}
				})
			})
		},
		
		// 工具方法
		getDictText(options, value) {
			const item = options.find(opt => opt.dictValue == value)
			return item ? item.dictLabel : value
		},
		
		getMedicationTypeText(type) {
			return this.getDictText(this.medicationTypeOptions, type)
		},
		
		getMedicationStatusText(status) {
			return this.getDictText(this.medicationStatusOptions, status)
		},
		
		getMedicationStatusType(status) {
			switch(status) {
				case '1': return 'success'
				case '2': return 'danger'
				case '3': return 'warning'
				default: return 'info'
			}
		},
		
		getTestTypeText(type) {
			return this.getDictText(this.testTypeOptions, type)
		},
		
		getTestResultStatusText(status) {
			return this.getDictText(this.testResultStatusOptions, status)
		},
		
		getTestResultStatusType(status) {
			switch(status) {
				case '1': return 'success'
				case '2': return 'warning'
				case '3': return 'info'
				case '4': return 'warning'
				case '5': return 'danger'
				default: return 'info'
			}
		},

		// ==================== 用药记录管理 ====================
		// 加载用药记录数据
		loadMedicationData() {
			this.medicationLoading = true
			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getMedicationList'),
				method: 'get',
				params: this.$http.adornParams({
					page: this.medicationPage.page,
					limit: this.medicationPage.limit,
					patientName: this.medicationSearch.patientName,
					medicationType: this.medicationSearch.medicationType,
					medicationStatus: this.medicationSearch.medicationStatus
				})
			}).then(({data}) => {
				this.medicationLoading = false
				if (data && data.code === 0) {
					this.medicationData = data.data
				}
			}).catch(() => {
				this.medicationLoading = false
			})
		},

		// 搜索用药记录
		searchMedication() {
			this.medicationPage.page = 1
			this.loadMedicationData()
		},

		// 重置用药记录搜索
		resetMedication() {
			this.medicationSearch = {
				patientName: '',
				medicationType: '',
				medicationStatus: ''
			}
			this.medicationPage.page = 1
			this.loadMedicationData()
		},

		// 用药记录分页
		handleMedicationSizeChange(val) {
			this.medicationPage.limit = val
			this.loadMedicationData()
		},

		handleMedicationCurrentChange(val) {
			this.medicationPage.page = val
			this.loadMedicationData()
		},

		// 添加用药记录
		addMedication() {
			this.$router.push({
				path: '/medicationForm',
				query: { action: 'add' }
			})
		},

		// 编辑用药记录
		editMedication(row) {
			this.$router.push({
				path: '/medicationForm',
				query: {
					action: 'edit',
					medicationId: row.medicationId
				}
			})
		},

		// 停药
		discontinueMedication(row) {
			this.$prompt('请输入停药原因', '停药确认', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputPattern: /.+/,
				inputErrorMessage: '停药原因不能为空'
			}).then(({ value }) => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/discontinueMedication'),
					method: 'post',
					data: this.$http.adornData({
						medicationId: row.medicationId,
						reason: value
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('停药成功')
						this.loadMedicationData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// 删除用药记录
		deleteMedication(row) {
			this.$confirm(`确定删除该用药记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/deleteMedication'),
					method: 'post',
					data: this.$http.adornData({
						medicationId: row.medicationId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadMedicationData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// ==================== 生命体征管理 ====================
		// 加载生命体征数据
		loadVitalSignsData() {
			this.vitalSignsLoading = true
			let params = {
				page: this.vitalSignsPage.page,
				limit: this.vitalSignsPage.limit,
				patientName: this.vitalSignsSearch.patientName
			}

			if (this.vitalSignsSearch.dateRange && this.vitalSignsSearch.dateRange.length === 2) {
				params.startDate = this.vitalSignsSearch.dateRange[0]
				params.endDate = this.vitalSignsSearch.dateRange[1]
			}

			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getVitalSignsList'),
				method: 'get',
				params: this.$http.adornParams(params)
			}).then(({data}) => {
				this.vitalSignsLoading = false
				if (data && data.code === 0) {
					this.vitalSignsData = data.data
				}
			}).catch(() => {
				this.vitalSignsLoading = false
			})
		},

		// 搜索生命体征
		searchVitalSigns() {
			this.vitalSignsPage.page = 1
			this.loadVitalSignsData()
		},

		// 重置生命体征搜索
		resetVitalSigns() {
			this.vitalSignsSearch = {
				patientName: '',
				dateRange: []
			}
			this.vitalSignsPage.page = 1
			this.loadVitalSignsData()
		},

		// 生命体征分页
		handleVitalSignsSizeChange(val) {
			this.vitalSignsPage.limit = val
			this.loadVitalSignsData()
		},

		handleVitalSignsCurrentChange(val) {
			this.vitalSignsPage.page = val
			this.loadVitalSignsData()
		},

		// 添加生命体征
		addVitalSigns() {
			this.$router.push({
				path: '/vitalSignsForm',
				query: { action: 'add' }
			})
		},

		// 编辑生命体征
		editVitalSigns(row) {
			this.$router.push({
				path: '/vitalSignsForm',
				query: {
					action: 'edit',
					vitalId: row.vitalId
				}
			})
		},

		// 删除生命体征
		deleteVitalSigns(row) {
			this.$confirm(`确定删除该生命体征记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/deleteVitalSigns'),
					method: 'post',
					data: this.$http.adornData({
						vitalId: row.vitalId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadVitalSignsData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// ==================== 检查结果管理 ====================
		// 加载检查结果数据
		loadTestResultData() {
			this.testResultLoading = true
			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getTestResultList'),
				method: 'get',
				params: this.$http.adornParams({
					page: this.testResultPage.page,
					limit: this.testResultPage.limit,
					patientName: this.testResultSearch.patientName,
					testType: this.testResultSearch.testType,
					resultStatus: this.testResultSearch.resultStatus
				})
			}).then(({data}) => {
				this.testResultLoading = false
				if (data && data.code === 0) {
					this.testResultData = data.data
				}
			}).catch(() => {
				this.testResultLoading = false
			})
		},

		// 搜索检查结果
		searchTestResult() {
			this.testResultPage.page = 1
			this.loadTestResultData()
		},

		// 重置检查结果搜索
		resetTestResult() {
			this.testResultSearch = {
				patientName: '',
				testType: '',
				resultStatus: ''
			}
			this.testResultPage.page = 1
			this.loadTestResultData()
		},

		// 检查结果分页
		handleTestResultSizeChange(val) {
			this.testResultPage.limit = val
			this.loadTestResultData()
		},

		handleTestResultCurrentChange(val) {
			this.testResultPage.page = val
			this.loadTestResultData()
		},

		// 添加检查结果
		addTestResult() {
			this.$router.push({
				path: '/testResultForm',
				query: { action: 'add' }
			})
		},

		// 编辑检查结果
		editTestResult(row) {
			this.$router.push({
				path: '/testResultForm',
				query: {
					action: 'edit',
					testId: row.testId
				}
			})
		},

		// 删除检查结果
		deleteTestResult(row) {
			this.$confirm(`确定删除该检查结果记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/deleteTestResult'),
					method: 'post',
					data: this.$http.adornData({
						testId: row.testId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadTestResultData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		}
	},
	mounted() {
		this.loadDictData()
		this.loadMedicationData()
	}
}
</script>

<style scoped>
.el-table {
	margin-top: 20px;
}
</style>
