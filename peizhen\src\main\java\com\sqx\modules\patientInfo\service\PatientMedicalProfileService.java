package com.sqx.modules.patientInfo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.common.utils.Result;
import com.sqx.modules.patientInfo.entity.*;

import java.util.List;

/**
 * <p>
 * 患者医疗档案综合服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface PatientMedicalProfileService {

    // ==================== 综合医疗档案管理 ====================
    
    /**
     * 获取患者完整医疗档案
     */
    Result getCompleteMedicalProfile(Long patientId);

    /**
     * 更新患者医疗档案完整度
     */
    Result updateProfileCompleteness(Long patientId);

    /**
     * 医疗档案风险评估
     */
    Result assessMedicalRisk(Long patientId);

    // ==================== 健康状态管理 ====================
    
    /**
     * 保存或更新健康状态
     */
    Result saveHealthStatus(PatientHealthStatus healthStatus);

    /**
     * 获取患者健康状态
     */
    PatientHealthStatus getHealthStatusByPatient(Long patientId);

    // ==================== 病史管理 ====================
    
    /**
     * 保存病史记录
     */
    Result saveMedicalHistory(PatientMedicalHistory medicalHistory);

    /**
     * 分页查询病史记录
     */
    IPage<PatientMedicalHistory> getMedicalHistoryList(Integer page, Integer limit, Long patientId, Integer historyType);

    /**
     * 获取活跃病史
     */
    List<PatientMedicalHistory> getActiveMedicalHistory(Long patientId);

    /**
     * 删除病史记录
     */
    Result deleteMedicalHistory(Long historyId);

    // ==================== 过敏管理 ====================
    
    /**
     * 保存过敏记录
     */
    Result saveAllergy(PatientAllergy allergy);

    /**
     * 分页查询过敏记录
     */
    IPage<PatientAllergy> getAllergyList(Integer page, Integer limit, Long patientId, Integer allergyType);

    /**
     * 获取活跃过敏记录
     */
    List<PatientAllergy> getActiveAllergies(Long patientId);

    /**
     * 检查过敏冲突
     */
    Result checkAllergyConflict(Long patientId, String allergenName, Integer allergyType);

    /**
     * 删除过敏记录
     */
    Result deleteAllergy(Long allergyId);

    // ==================== 用药管理 ====================
    
    /**
     * 保存用药记录
     */
    Result saveMedication(PatientMedication medication);

    /**
     * 分页查询用药记录
     */
    IPage<PatientMedication> getMedicationList(Integer page, Integer limit, Long patientId, Integer medicationStatus);

    /**
     * 获取当前用药列表
     */
    List<PatientMedication> getCurrentMedications(Long patientId);

    /**
     * 检查用药冲突
     */
    Result checkMedicationConflict(Long patientId, String medicationName);

    /**
     * 停用药物
     */
    Result discontinueMedication(Long medicationId, String reason);

    /**
     * 删除用药记录
     */
    Result deleteMedication(Long medicationId);

    // ==================== 生命体征管理 ====================
    
    /**
     * 保存生命体征记录
     */
    Result saveVitalSigns(PatientVitalSigns vitalSigns);

    /**
     * 分页查询生命体征记录
     */
    IPage<PatientVitalSigns> getVitalSignsList(Integer page, Integer limit, Long patientId, String startDate, String endDate);

    /**
     * 获取最新生命体征
     */
    PatientVitalSigns getLatestVitalSigns(Long patientId);

    /**
     * 删除生命体征记录
     */
    Result deleteVitalSigns(Long vitalId);

    // ==================== 检查结果管理 ====================
    
    /**
     * 保存检查结果
     */
    Result saveTestResult(PatientTestResult testResult);

    /**
     * 分页查询检查结果
     */
    IPage<PatientTestResult> getTestResultList(Integer page, Integer limit, Long patientId, Integer testType, String startDate, String endDate);

    /**
     * 获取最近检查结果
     */
    List<PatientTestResult> getRecentTestResults(Long patientId, Integer limit);

    /**
     * 删除检查结果
     */
    Result deleteTestResult(Long testId);

    // ==================== 疫苗接种管理 ====================
    
    /**
     * 保存疫苗接种记录
     */
    Result saveVaccination(PatientVaccination vaccination);

    /**
     * 分页查询疫苗接种记录
     */
    IPage<PatientVaccination> getVaccinationList(Integer page, Integer limit, Long patientId, Integer vaccineType);

    /**
     * 获取疫苗接种历史
     */
    List<PatientVaccination> getVaccinationHistory(Long patientId);

    /**
     * 删除疫苗接种记录
     */
    Result deleteVaccination(Long vaccinationId);

    // ==================== 治疗方案管理 ====================
    
    /**
     * 保存治疗方案
     */
    Result saveTreatmentPlan(PatientTreatmentPlan treatmentPlan);

    /**
     * 分页查询治疗方案
     */
    IPage<PatientTreatmentPlan> getTreatmentPlanList(Integer page, Integer limit, Long patientId, Integer planStatus);

    /**
     * 获取当前治疗方案
     */
    List<PatientTreatmentPlan> getCurrentTreatmentPlans(Long patientId);

    /**
     * 更新治疗方案状态
     */
    Result updateTreatmentPlanStatus(Long planId, Integer status);

    /**
     * 删除治疗方案
     */
    Result deleteTreatmentPlan(Long planId);

    // ==================== 医疗数据统计 ====================
    
    /**
     * 获取患者医疗数据统计
     */
    Result getMedicalDataStatistics(Long patientId);

    /**
     * 生成医疗报告
     */
    Result generateMedicalReport(Long patientId, String reportType);
}
