package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.patientInfo.entity.PatientMedicalHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 患者病史记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Mapper
public interface PatientMedicalHistoryDao extends BaseMapper<PatientMedicalHistory> {

    /**
     * 分页查询患者病史记录
     */
    IPage<PatientMedicalHistory> getHistoryList(@Param("pages") Page<PatientMedicalHistory> pages, 
                                               @Param("patientId") Long patientId, 
                                               @Param("historyType") Integer historyType);

    /**
     * 获取患者活跃病史记录
     */
    List<PatientMedicalHistory> getActiveHistoryByPatient(@Param("patientId") Long patientId);

    /**
     * 获取患者家族病史
     */
    List<PatientMedicalHistory> getFamilyHistoryByPatient(@Param("patientId") Long patientId);

    /**
     * 根据疾病名称搜索病史
     */
    List<PatientMedicalHistory> searchByDiseaseName(@Param("patientId") Long patientId, 
                                                   @Param("diseaseName") String diseaseName);
}
