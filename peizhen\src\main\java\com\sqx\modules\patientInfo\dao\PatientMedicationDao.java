package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.patientInfo.entity.PatientMedication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 患者用药记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Mapper
public interface PatientMedicationDao extends BaseMapper<PatientMedication> {

    /**
     * 分页查询患者用药记录
     */
    IPage<PatientMedication> getMedicationList(@Param("pages") Page<PatientMedication> pages, 
                                              @Param("patientId") Long patientId, 
                                              @Param("medicationStatus") Integer medicationStatus);

    /**
     * 获取患者当前用药列表
     */
    List<PatientMedication> getCurrentMedicationsByPatient(@Param("patientId") Long patientId);

    /**
     * 根据药物名称搜索用药记录
     */
    List<PatientMedication> searchByMedicationName(@Param("patientId") Long patientId, 
                                                  @Param("medicationName") String medicationName);

    /**
     * 获取患者用药历史
     */
    List<PatientMedication> getMedicationHistoryByPatient(@Param("patientId") Long patientId);

    /**
     * 检查药物冲突
     */
    List<PatientMedication> checkMedicationConflicts(@Param("patientId") Long patientId, 
                                                    @Param("medicationName") String medicationName);
}
