# Java数据字典初始化使用说明

## 🎯 概述

由于SQL脚本在您的数据库环境中可能存在兼容性问题，我创建了一个Java服务来初始化医疗档案相关的数据字典。这个方案更加灵活，可以适配您现有的系统架构。

## 📁 文件结构

```
peizhen/src/main/java/com/sqx/modules/sys/
├── service/impl/
│   └── MedicalDictInitService.java          # 数据字典初始化服务
├── controller/
│   └── MedicalDictInitController.java       # 初始化接口控制器
└── test/
    └── MedicalDictInitTest.java             # 测试类
```

## 🚀 使用方法

### 方法一：通过API接口初始化

1. **启动您的Spring Boot应用**

2. **调用初始化接口**
   ```bash
   POST http://localhost:8080/sys/medical-dict/init
   ```

3. **使用Postman或curl测试**
   ```bash
   curl -X POST http://localhost:8080/sys/medical-dict/init
   ```

4. **预期响应**
   ```json
   {
     "code": 0,
     "msg": "医疗档案数据字典初始化成功！共初始化15个字典类型，75个字典项。"
   }
   ```

### 方法二：通过单元测试初始化

1. **运行测试类**
   ```bash
   # 在IDE中右键运行
   MedicalDictInitTest.testInitMedicalDict()
   
   # 或使用Maven命令
   mvn test -Dtest=MedicalDictInitTest#testInitMedicalDict
   ```

2. **查看控制台输出**
   ```
   医疗档案数据字典初始化成功！
   ```

### 方法三：在代码中直接调用

```java
@Autowired
private MedicalDictInitService medicalDictInitService;

public void initDict() {
    medicalDictInitService.initMedicalDict();
}
```

## 📊 初始化的数据字典

### 数据结构说明

每个字典类型都会创建：
1. **父级记录** - 字典类型名称（如"患者风险等级"）
2. **子级记录** - 具体的字典项（如"低风险"、"中风险"、"高风险"）

### 初始化的15种字典类型

| 序号 | 字典类型 | type值 | 字典项数量 | 说明 |
|------|---------|--------|------------|------|
| 1 | 患者风险等级 | patient_risk_level | 3 | 低风险、中风险、高风险 |
| 2 | 健康状态评级 | health_status_level | 5 | 优秀、良好、一般、较差、差 |
| 3 | 病史类型 | medical_history_type | 5 | 既往史、现病史、家族史、过敏史、手术史 |
| 4 | 过敏类型 | allergy_type | 5 | 药物过敏、食物过敏、环境过敏、接触过敏、其他过敏 |
| 5 | 过敏严重程度 | allergy_severity | 4 | 轻微、中度、严重、危及生命 |
| 6 | 药物分类 | medication_type | 5 | 处方药、非处方药、中药、生物制剂、疫苗 |
| 7 | 用药状态 | medication_status | 3 | 正在使用、已停用、暂停使用 |
| 8 | 检查类型 | test_type | 5 | 血液检查、影像检查、心电检查、内镜检查、病理检查 |
| 9 | 检查结果状态 | test_result_status | 5 | 正常、异常偏高、异常偏低、临界值、严重异常 |
| 10 | 疫苗类型 | vaccine_type | 5 | 新冠疫苗、流感疫苗、乙肝疫苗、HPV疫苗、其他疫苗 |
| 11 | 接种反应 | vaccination_reaction | 4 | 无反应、轻微反应、中度反应、严重反应 |
| 12 | 治疗方案类型 | treatment_plan_type | 5 | 药物治疗、手术治疗、物理治疗、心理治疗、综合治疗 |
| 13 | 治疗方案状态 | treatment_plan_status | 5 | 计划中、进行中、已完成、暂停、终止 |
| 14 | 婚姻状况 | marital_status | 4 | 未婚、已婚、离异、丧偶 |
| 15 | 医保类型 | insurance_type | 5 | 城镇职工、城镇居民、新农合、商业保险、自费 |

**总计：15个字典类型 + 60个字典项 = 75条记录**

## 🔧 技术特性

### 1. 安全性
- **事务支持** - 使用 `@Transactional` 确保数据一致性
- **清理机制** - 初始化前会清理已存在的医疗相关字典
- **异常处理** - 完善的异常捕获和处理

### 2. 灵活性
- **层级结构** - 支持父子级字典结构
- **可扩展** - 易于添加新的字典类型
- **可配置** - 字典项可以通过修改代码轻松调整

### 3. 兼容性
- **适配现有结构** - 完全兼容您的 `sys_dict` 表结构
- **使用现有服务** - 基于您现有的 `SysDictService`
- **无侵入性** - 不影响现有功能

## 📋 验证初始化结果

### 1. 数据库查询验证

```sql
-- 查看所有医疗相关的字典类型
SELECT DISTINCT type FROM sys_dict 
WHERE type IN (
    'patient_risk_level', 'health_status_level', 'medical_history_type',
    'allergy_type', 'allergy_severity', 'medication_type', 'medication_status',
    'test_type', 'test_result_status', 'vaccine_type', 'vaccination_reaction',
    'treatment_plan_type', 'treatment_plan_status', 'marital_status', 'insurance_type'
);

-- 查看患者风险等级的层级结构
SELECT id, name, type, code, value, order_num, parent_id 
FROM sys_dict 
WHERE type = 'patient_risk_level' 
ORDER BY parent_id, order_num;

-- 统计各类型的字典项数量
SELECT type, COUNT(*) as count 
FROM sys_dict 
WHERE type LIKE '%patient%' OR type LIKE '%medical%' OR type LIKE '%health%'
GROUP BY type;
```

### 2. 前端页面验证

1. 访问您的前端页面
2. 检查下拉选项是否正确加载
3. 验证标签显示是否正常

## 🔄 自定义和扩展

### 添加新的字典类型

1. **在 `MedicalDictInitService` 中添加新方法**
   ```java
   private void initNewDictType() {
       Map<String, String> items = new HashMap<>();
       items.put("1", "选项1");
       items.put("2", "选项2");
       
       createDictTypeWithItems("新字典类型", "new_dict_type", items, "新字典类型说明");
   }
   ```

2. **在 `initMedicalDict()` 方法中调用**
   ```java
   public void initMedicalDict() {
       // ... 其他初始化
       initNewDictType();
   }
   ```

3. **更新清理方法**
   ```java
   private void cleanExistingMedicalDict() {
       String[] medicalTypes = {
           // ... 现有类型
           "new_dict_type"
       };
       // ...
   }
   ```

### 修改现有字典项

直接修改对应的 `init*()` 方法中的 `items` Map即可。

## 🚨 注意事项

1. **备份数据** - 初始化前请备份您的 `sys_dict` 表数据
2. **权限检查** - 确保运行用户有数据库写权限
3. **重复执行** - 可以重复执行，会先清理再创建
4. **依赖检查** - 确保 `SysDictService` 正常工作

## 🎯 故障排除

### 常见问题

1. **服务注入失败**
   - 检查 `@Service` 注解
   - 确认包扫描路径正确

2. **数据库连接问题**
   - 检查数据库配置
   - 确认数据库服务正常

3. **权限不足**
   - 检查数据库用户权限
   - 确认表操作权限

### 调试方法

1. **开启SQL日志**
   ```yaml
   logging:
     level:
       com.sqx.modules.sys.dao: DEBUG
   ```

2. **添加调试日志**
   ```java
   @Slf4j
   public class MedicalDictInitService {
       // 在方法中添加
       log.info("正在初始化字典类型: {}", type);
   }
   ```

通过这个Java初始化方案，您可以更灵活地管理医疗档案数据字典，并且完全适配您现有的系统架构！
