<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientMedicationDao">

    <!-- 分页查询患者用药记录 -->
    <select id="getMedicationList" resultType="com.sqx.modules.patientInfo.entity.PatientMedication">
        SELECT * FROM patient_medication 
        WHERE patient_id = #{patientId} AND is_delete = 0
        <if test="medicationStatus != null">
            AND medication_status = #{medicationStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 获取患者当前用药列表 -->
    <select id="getCurrentMedicationsByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientMedication">
        SELECT * FROM patient_medication 
        WHERE patient_id = #{patientId} AND medication_status = 1 AND is_delete = 0
        ORDER BY start_date DESC
    </select>

    <!-- 根据药物名称搜索用药记录 -->
    <select id="searchByMedicationName" resultType="com.sqx.modules.patientInfo.entity.PatientMedication">
        SELECT * FROM patient_medication 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND (medication_name LIKE CONCAT('%', #{medicationName}, '%') 
             OR brand_name LIKE CONCAT('%', #{medicationName}, '%'))
        ORDER BY create_time DESC
    </select>

    <!-- 获取患者用药历史 -->
    <select id="getMedicationHistoryByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientMedication">
        SELECT * FROM patient_medication 
        WHERE patient_id = #{patientId} AND is_delete = 0
        ORDER BY start_date DESC, create_time DESC
    </select>

    <!-- 检查药物冲突 -->
    <select id="checkMedicationConflicts" resultType="com.sqx.modules.patientInfo.entity.PatientMedication">
        SELECT * FROM patient_medication 
        WHERE patient_id = #{patientId} AND medication_status = 1 AND is_delete = 0
        AND (medication_name = #{medicationName} OR brand_name = #{medicationName})
    </select>

</mapper>
