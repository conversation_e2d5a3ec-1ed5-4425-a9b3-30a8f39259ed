<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientVitalSignsDao">

    <!-- 分页查询患者生命体征记录 -->
    <select id="getVitalSignsList" resultType="com.sqx.modules.patientInfo.entity.PatientVitalSigns">
        SELECT * FROM patient_vital_signs 
        WHERE patient_id = #{patientId} AND is_delete = 0
        <if test="startDate != null and startDate != ''">
            AND measurement_date >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND measurement_date &lt;= #{endDate}
        </if>
        ORDER BY measurement_date DESC, create_time DESC
    </select>

    <!-- 获取患者最新生命体征 -->
    <select id="getLatestByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientVitalSigns">
        SELECT * FROM patient_vital_signs 
        WHERE patient_id = #{patientId} AND is_delete = 0
        ORDER BY measurement_date DESC, create_time DESC
        LIMIT 1
    </select>

    <!-- 获取患者生命体征历史记录 -->
    <select id="getHistoryByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientVitalSigns">
        SELECT * FROM patient_vital_signs 
        WHERE patient_id = #{patientId} AND is_delete = 0
        ORDER BY measurement_date DESC, create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取异常生命体征记录 -->
    <select id="getAbnormalVitalSigns" resultType="com.sqx.modules.patientInfo.entity.PatientVitalSigns">
        SELECT * FROM patient_vital_signs 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND (abnormal_indicators IS NOT NULL AND abnormal_indicators != '')
        ORDER BY measurement_date DESC, create_time DESC
    </select>

</mapper>
