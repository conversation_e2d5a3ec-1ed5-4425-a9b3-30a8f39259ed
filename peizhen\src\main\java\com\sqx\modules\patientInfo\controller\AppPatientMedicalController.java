package com.sqx.modules.patientInfo.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.patientInfo.entity.*;
import com.sqx.modules.patientInfo.service.PatientMedicalProfileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 患者医疗档案管理-用户端
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@RestController
@Api(value = "患者医疗档案管理-用户端", tags = {"患者医疗档案管理-用户端"})
@RequestMapping("/app/patientMedical/")
public class AppPatientMedicalController {

    @Autowired
    private PatientMedicalProfileService medicalProfileService;

    // ==================== 综合医疗档案管理 ====================
    
    @Login
    @ApiOperation("获取患者完整医疗档案")
    @GetMapping("getCompleteMedicalProfile")
    public Result getCompleteMedicalProfile(Long patientId) {
        return medicalProfileService.getCompleteMedicalProfile(patientId);
    }

    @Login
    @ApiOperation("更新患者医疗档案完整度")
    @PostMapping("updateProfileCompleteness")
    public Result updateProfileCompleteness(Long patientId) {
        return medicalProfileService.updateProfileCompleteness(patientId);
    }

    @Login
    @ApiOperation("医疗档案风险评估")
    @PostMapping("assessMedicalRisk")
    public Result assessMedicalRisk(Long patientId) {
        return medicalProfileService.assessMedicalRisk(patientId);
    }

    // ==================== 健康状态管理 ====================
    
    @Login
    @ApiOperation("保存或更新健康状态")
    @PostMapping("saveHealthStatus")
    public Result saveHealthStatus(@RequestBody PatientHealthStatus healthStatus) {
        return medicalProfileService.saveHealthStatus(healthStatus);
    }

    @Login
    @ApiOperation("获取患者健康状态")
    @GetMapping("getHealthStatus")
    public Result getHealthStatus(Long patientId) {
        PatientHealthStatus healthStatus = medicalProfileService.getHealthStatusByPatient(patientId);
        return Result.success().put("data", healthStatus);
    }

    // ==================== 病史管理 ====================
    
    @Login
    @ApiOperation("保存病史记录")
    @PostMapping("saveMedicalHistory")
    public Result saveMedicalHistory(@RequestBody PatientMedicalHistory medicalHistory) {
        return medicalProfileService.saveMedicalHistory(medicalHistory);
    }

    @Login
    @ApiOperation("分页查询病史记录")
    @GetMapping("getMedicalHistoryList")
    public Result getMedicalHistoryList(Integer page, Integer limit, Long patientId, Integer historyType) {
        return Result.success().put("data", medicalProfileService.getMedicalHistoryList(page, limit, patientId, historyType));
    }

    @Login
    @ApiOperation("获取活跃病史")
    @GetMapping("getActiveMedicalHistory")
    public Result getActiveMedicalHistory(Long patientId) {
        return Result.success().put("data", medicalProfileService.getActiveMedicalHistory(patientId));
    }

    @Login
    @ApiOperation("删除病史记录")
    @PostMapping("deleteMedicalHistory")
    public Result deleteMedicalHistory(Long historyId) {
        return medicalProfileService.deleteMedicalHistory(historyId);
    }

    // ==================== 过敏管理 ====================
    
    @Login
    @ApiOperation("保存过敏记录")
    @PostMapping("saveAllergy")
    public Result saveAllergy(@RequestBody PatientAllergy allergy) {
        return medicalProfileService.saveAllergy(allergy);
    }

    @Login
    @ApiOperation("分页查询过敏记录")
    @GetMapping("getAllergyList")
    public Result getAllergyList(Integer page, Integer limit, Long patientId, Integer allergyType) {
        return Result.success().put("data", medicalProfileService.getAllergyList(page, limit, patientId, allergyType));
    }

    @Login
    @ApiOperation("获取活跃过敏记录")
    @GetMapping("getActiveAllergies")
    public Result getActiveAllergies(Long patientId) {
        return Result.success().put("data", medicalProfileService.getActiveAllergies(patientId));
    }

    @Login
    @ApiOperation("检查过敏冲突")
    @PostMapping("checkAllergyConflict")
    public Result checkAllergyConflict(Long patientId, String allergenName, Integer allergyType) {
        return medicalProfileService.checkAllergyConflict(patientId, allergenName, allergyType);
    }

    @Login
    @ApiOperation("删除过敏记录")
    @PostMapping("deleteAllergy")
    public Result deleteAllergy(Long allergyId) {
        return medicalProfileService.deleteAllergy(allergyId);
    }

    // ==================== 用药管理 ====================
    
    @Login
    @ApiOperation("保存用药记录")
    @PostMapping("saveMedication")
    public Result saveMedication(@RequestBody PatientMedication medication) {
        return medicalProfileService.saveMedication(medication);
    }

    @Login
    @ApiOperation("分页查询用药记录")
    @GetMapping("getMedicationList")
    public Result getMedicationList(Integer page, Integer limit, Long patientId, Integer medicationStatus) {
        return Result.success().put("data", medicalProfileService.getMedicationList(page, limit, patientId, medicationStatus));
    }

    @Login
    @ApiOperation("获取当前用药列表")
    @GetMapping("getCurrentMedications")
    public Result getCurrentMedications(Long patientId) {
        return Result.success().put("data", medicalProfileService.getCurrentMedications(patientId));
    }

    @Login
    @ApiOperation("检查用药冲突")
    @PostMapping("checkMedicationConflict")
    public Result checkMedicationConflict(Long patientId, String medicationName) {
        return medicalProfileService.checkMedicationConflict(patientId, medicationName);
    }

    @Login
    @ApiOperation("停用药物")
    @PostMapping("discontinueMedication")
    public Result discontinueMedication(Long medicationId, String reason) {
        return medicalProfileService.discontinueMedication(medicationId, reason);
    }

    @Login
    @ApiOperation("删除用药记录")
    @PostMapping("deleteMedication")
    public Result deleteMedication(Long medicationId) {
        return medicalProfileService.deleteMedication(medicationId);
    }

    // ==================== 生命体征管理 ====================
    
    @Login
    @ApiOperation("保存生命体征记录")
    @PostMapping("saveVitalSigns")
    public Result saveVitalSigns(@RequestBody PatientVitalSigns vitalSigns) {
        return medicalProfileService.saveVitalSigns(vitalSigns);
    }

    @Login
    @ApiOperation("分页查询生命体征记录")
    @GetMapping("getVitalSignsList")
    public Result getVitalSignsList(Integer page, Integer limit, Long patientId, String startDate, String endDate) {
        return Result.success().put("data", medicalProfileService.getVitalSignsList(page, limit, patientId, startDate, endDate));
    }

    @Login
    @ApiOperation("获取最新生命体征")
    @GetMapping("getLatestVitalSigns")
    public Result getLatestVitalSigns(Long patientId) {
        return Result.success().put("data", medicalProfileService.getLatestVitalSigns(patientId));
    }

    @Login
    @ApiOperation("删除生命体征记录")
    @PostMapping("deleteVitalSigns")
    public Result deleteVitalSigns(Long vitalId) {
        return medicalProfileService.deleteVitalSigns(vitalId);
    }

    // ==================== 检查结果管理 ====================
    
    @Login
    @ApiOperation("保存检查结果")
    @PostMapping("saveTestResult")
    public Result saveTestResult(@RequestBody PatientTestResult testResult) {
        return medicalProfileService.saveTestResult(testResult);
    }

    @Login
    @ApiOperation("分页查询检查结果")
    @GetMapping("getTestResultList")
    public Result getTestResultList(Integer page, Integer limit, Long patientId, Integer testType, String startDate, String endDate) {
        return Result.success().put("data", medicalProfileService.getTestResultList(page, limit, patientId, testType, startDate, endDate));
    }

    @Login
    @ApiOperation("获取最近检查结果")
    @GetMapping("getRecentTestResults")
    public Result getRecentTestResults(Long patientId, Integer limit) {
        return Result.success().put("data", medicalProfileService.getRecentTestResults(patientId, limit));
    }

    @Login
    @ApiOperation("删除检查结果")
    @PostMapping("deleteTestResult")
    public Result deleteTestResult(Long testId) {
        return medicalProfileService.deleteTestResult(testId);
    }

    // ==================== 疫苗接种管理 ====================
    
    @Login
    @ApiOperation("保存疫苗接种记录")
    @PostMapping("saveVaccination")
    public Result saveVaccination(@RequestBody PatientVaccination vaccination) {
        return medicalProfileService.saveVaccination(vaccination);
    }

    @Login
    @ApiOperation("分页查询疫苗接种记录")
    @GetMapping("getVaccinationList")
    public Result getVaccinationList(Integer page, Integer limit, Long patientId, Integer vaccineType) {
        return Result.success().put("data", medicalProfileService.getVaccinationList(page, limit, patientId, vaccineType));
    }

    @Login
    @ApiOperation("获取疫苗接种历史")
    @GetMapping("getVaccinationHistory")
    public Result getVaccinationHistory(Long patientId) {
        return Result.success().put("data", medicalProfileService.getVaccinationHistory(patientId));
    }

    @Login
    @ApiOperation("删除疫苗接种记录")
    @PostMapping("deleteVaccination")
    public Result deleteVaccination(Long vaccinationId) {
        return medicalProfileService.deleteVaccination(vaccinationId);
    }

    // ==================== 治疗方案管理 ====================
    
    @Login
    @ApiOperation("保存治疗方案")
    @PostMapping("saveTreatmentPlan")
    public Result saveTreatmentPlan(@RequestBody PatientTreatmentPlan treatmentPlan) {
        return medicalProfileService.saveTreatmentPlan(treatmentPlan);
    }

    @Login
    @ApiOperation("分页查询治疗方案")
    @GetMapping("getTreatmentPlanList")
    public Result getTreatmentPlanList(Integer page, Integer limit, Long patientId, Integer planStatus) {
        return Result.success().put("data", medicalProfileService.getTreatmentPlanList(page, limit, patientId, planStatus));
    }

    @Login
    @ApiOperation("获取当前治疗方案")
    @GetMapping("getCurrentTreatmentPlans")
    public Result getCurrentTreatmentPlans(Long patientId) {
        return Result.success().put("data", medicalProfileService.getCurrentTreatmentPlans(patientId));
    }

    @Login
    @ApiOperation("更新治疗方案状态")
    @PostMapping("updateTreatmentPlanStatus")
    public Result updateTreatmentPlanStatus(Long planId, Integer status) {
        return medicalProfileService.updateTreatmentPlanStatus(planId, status);
    }

    @Login
    @ApiOperation("删除治疗方案")
    @PostMapping("deleteTreatmentPlan")
    public Result deleteTreatmentPlan(Long planId) {
        return medicalProfileService.deleteTreatmentPlan(planId);
    }

    // ==================== 医疗数据统计 ====================
    
    @Login
    @ApiOperation("获取患者医疗数据统计")
    @GetMapping("getMedicalDataStatistics")
    public Result getMedicalDataStatistics(Long patientId) {
        return medicalProfileService.getMedicalDataStatistics(patientId);
    }

    @Login
    @ApiOperation("生成医疗报告")
    @PostMapping("generateMedicalReport")
    public Result generateMedicalReport(Long patientId, String reportType) {
        return medicalProfileService.generateMedicalReport(patientId, reportType);
    }
}
