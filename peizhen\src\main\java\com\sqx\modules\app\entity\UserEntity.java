package com.sqx.modules.app.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 用户
 */
@Data
@ApiModel("用户")
@TableName("tb_user")
public class UserEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Excel(name = "编号",orderNum = "1")
    @ApiModelProperty("用户id")
    @TableId(type = IdType.AUTO, value = "user_id")
    private Long userId;
    /**
     * 用户名
     */
    @Excel(name = "昵称",orderNum = "2")
    @ApiModelProperty("用户名")
    @TableField("user_name")
    private String userName;

    /**
     * 手机号
     */
    @Excel(name = "手机号",orderNum = "5")
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 头像
     */
    @Excel(name = "头像",orderNum = "4")
    @ApiModelProperty("头像")
    private String avatar;

    /**
     * 性别 1男 2女
     */
    @Excel(name = "性别",replace = {"男_1","女_2","未设置_null","未设置_0" +
            ""},orderNum = "3")
    @ApiModelProperty("性别 1男 2女")
    private Integer sex;
    /**
     * 年龄
     */
    @ApiModelProperty("年龄")
    private Integer age;

    /**
     * 微信小程序openid
     */
    @ApiModelProperty("微信小程序openid")
    @TableField("open_id")
    private String openId;

    /**
     * 微信app openid
     */
    @ApiModelProperty("微信公众号openid")
    @TableField("wx_open_id")
    private String wxOpenId;

    /**
     * 密码
     */
    private String password;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间",orderNum = "16",width = 20)
    @TableField("create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private String updateTime;

    /**
     * 苹果id
     */
    @TableField("apple_id")
    private String appleId;

    /**
     * 手机类型 1安卓 2ios
     */
    @TableField("sys_phone")
    private Integer sysPhone;

    /**
     * 状态 1正常 2禁用
     */
    private Integer status;

    /**
     * 来源 app 小程序 公众号
     */
    private String platform;

    /**
     * 积分
     */
    private Integer jifen;
    /**
     * 是否是新人 1是 0不是
     */
    private Integer IsNewPeople;

    /**
     * 邀请码
     */
    @Excel(name = "邀请码",orderNum = "7")
    @TableField("invitation_code")
    private String invitationCode;

    /**
     * 邀请人邀请码
     */
    @Excel(name = "邀请人邀请码",orderNum = "8")
    @TableField("inviter_code")
    private String inviterCode;

    private String clientid;
    @Excel(name = "支付宝账号",orderNum = "13")
    private String zhiFuBao;
    @Excel(name = "支付宝名称",orderNum = "11")
    private String zhiFuBaoName;

    /**
     * 是否认证 状态值 1用户认证  2企业认证  3用户企业都认证
     */
    @Excel(name = "是否已实名",replace = {"是_1","否_null"},orderNum = "9")
    private Integer isAuthentication;

    /**
     * 商家小程序openId
     */
    private String shopOpenId;

    @TableField(exist = false)
    private Integer type;

    /**
     * 介绍
     */
    private String details;

    /**
     * 轮播图
     */
    private String detailsImg;

    /**
     * 资质
     */
    private String certificationImg;
    @Excel(name = "收款微信二维码",orderNum = "10")
    private String wxImg;
    private String address;

    private String shopPhone;

    private String shopImg;

    private String shopName;

    private String addressImg;

    private String startTime;

    private String endTime;

    private String shopType;

    private String longitude;

    private String latitude;

    /**
     * 是否接受消息推送 1是 2否
     */
    private Integer isSendMsg;

    @Excel(name = "佣金比例",orderNum = "12")
    private BigDecimal rate;

    /**
     * 直属佣金比例
     */
    @Excel(name = "推广佣金比例",orderNum = "14")
    private BigDecimal zhiRate;

    /**
     * 非直属佣金比例
     */
    @Excel(name = "代理商佣金比例",orderNum = "15")
    private BigDecimal feiRate;

    /**
     * 是否缴纳保证金  1缴纳 其他未缴纳
     */
    private Integer isSafetyMoney;

    /**
     * 是否是推广员 1是
     */
    private Integer isPromotion;

    /**
     * 是否是代理商
     */
    private Integer isAgent;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 评分
     */
    private String finalScore;
    /**
     * 信用分
     */
    @Excel(name = "信用分",orderNum = "6")
    private Integer creditScore;
    /**
     * 等级id
     */
    private Long rewardId;

    /**
     * 等级图标
     */
    private String iconImg;
    /**
     * 等级名称
     */
    private String levelName;
  /**
     * 接单状态:0下线 1上线
     */
    private Integer shopState;


    /**
     * 工作状态 (0空闲中 1忙碌中)
     */
    @TableField(exist = false)
    private Integer workStatus;
    /**
     * 护龄时长
     */
    @TableField(exist = false)
    private Integer workAge;
    /**
     * 真实姓名
     */
    @TableField(exist = false)
    private String realName;
    /**
     * 完成订单数
     */
    @TableField(exist = false)
    private Integer orderCount;

    /**
     * 实名认证信息
     */
    @TableField(exist = false)
    private UserCertification userCertification;


    /**
     * 服务项目名称
     */
    @TableField(exist = false)
    private String serviceName;
}
