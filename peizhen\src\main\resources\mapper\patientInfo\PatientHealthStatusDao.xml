<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientHealthStatusDao">

    <!-- 根据患者ID获取健康状态 -->
    <select id="getByPatientId" resultType="com.sqx.modules.patientInfo.entity.PatientHealthStatus">
        SELECT * FROM patient_health_status 
        WHERE patient_id = #{patientId} AND is_delete = 0
    </select>

    <!-- 更新或插入健康状态 -->
    <insert id="saveOrUpdate" parameterType="com.sqx.modules.patientInfo.entity.PatientHealthStatus">
        INSERT INTO patient_health_status (
            patient_id, overall_health, height, weight, bmi, blood_type, rh_type,
            smoking_status, drinking_status, exercise_frequency, sleep_quality,
            mental_status, mobility_status, chronic_diseases, risk_level,
            special_notes, create_time, update_time, create_by, update_by
        ) VALUES (
            #{patientId}, #{overallHealth}, #{height}, #{weight}, #{bmi}, #{bloodType}, #{rhType},
            #{smokingStatus}, #{drinkingStatus}, #{exerciseFrequency}, #{sleepQuality},
            #{mentalStatus}, #{mobilityStatus}, #{chronicDiseases}, #{riskLevel},
            #{specialNotes}, NOW(), NOW(), #{createBy}, #{updateBy}
        ) ON DUPLICATE KEY UPDATE
            overall_health = VALUES(overall_health),
            height = VALUES(height),
            weight = VALUES(weight),
            bmi = VALUES(bmi),
            blood_type = VALUES(blood_type),
            rh_type = VALUES(rh_type),
            smoking_status = VALUES(smoking_status),
            drinking_status = VALUES(drinking_status),
            exercise_frequency = VALUES(exercise_frequency),
            sleep_quality = VALUES(sleep_quality),
            mental_status = VALUES(mental_status),
            mobility_status = VALUES(mobility_status),
            chronic_diseases = VALUES(chronic_diseases),
            risk_level = VALUES(risk_level),
            special_notes = VALUES(special_notes),
            update_time = NOW(),
            update_by = VALUES(update_by)
    </insert>

</mapper>
