<template>
  <div class="allergy-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="search-item">
          <span>患者姓名：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入患者姓名"
            v-model="searchForm.patientName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>过敏类型：</span>
          <el-select style="width: 200px;" v-model="searchForm.allergyType" placeholder="请选择过敏类型" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in allergyTypeOptions" :key="item.code" :label="item.value" :value="item.code">
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span>过敏原：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入过敏原"
            v-model="searchForm.allergenName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>严重程度：</span>
          <el-select style="width: 200px;" v-model="searchForm.severity" placeholder="请选择严重程度" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in severityOptions" :key="item.code" :label="item.value" :value="item.code">
            </el-option>
          </el-select>
        </div>
      </div>
      
      <div class="action-buttons">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="handleSearch">
          查询
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd">
          添加过敏记录
        </el-button>
        <el-button size="mini" type="success" icon="el-icon-download" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData.records" border stripe>
      <el-table-column prop="patientName" label="患者姓名" width="120">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            style="color: #409EFF;background: #fff;border: none;padding: 0;" 
            type="primary"
            @click="handleViewPatient(scope.row)">
            {{ scope.row.patientName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="allergyType" label="过敏类型" width="100">
        <template slot-scope="scope">
          <el-tag :type="getAllergyTypeColor(scope.row.allergyType)">
            {{ getAllergyTypeText(scope.row.allergyType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="allergenName" label="过敏原" width="150">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.allergenName" placement="top">
            <span class="text-ellipsis">{{ scope.row.allergenName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="allergenCode" label="过敏原编码" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.allergenCode || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="severity" label="严重程度" width="100">
        <template slot-scope="scope">
          <el-tag :type="getSeverityColor(scope.row.severity)">
            {{ getSeverityText(scope.row.severity) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="symptoms" label="过敏症状" width="200">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.symptoms" placement="top">
            <span class="text-ellipsis">{{ scope.row.symptoms || '-' }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="firstDiscoveryDate" label="首次发现" width="120"></el-table-column>
      <el-table-column prop="lastReactionDate" label="最后反应" width="120"></el-table-column>
      <el-table-column prop="isConfirmed" label="确认状态" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isConfirmed == 1 ? 'success' : 'warning'">
            {{ scope.row.isConfirmed == 1 ? '确认' : '疑似' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="requiresEmergencyTreatment" label="紧急处理" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.requiresEmergencyTreatment == 1 ? 'danger' : 'success'">
            {{ scope.row.requiresEmergencyTreatment == 1 ? '需要' : '不需要' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isActive" label="是否活跃" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isActive == 1 ? 'success' : 'info'">
            {{ scope.row.isActive == 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="info" @click="handleViewDetail(scope.row)">详情</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination 
        @size-change="handleSizeChange" 
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 30, 40]" 
        :page-size="pagination.limit" 
        :current-page="pagination.page"
        layout="total,sizes, prev, pager, next,jumper" 
        :total="tableData.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AllergyManagement',
  props: {
    allergyTypeOptions: {
      type: Array,
      default: () => []
    },
    severityOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        patientName: '',
        allergyType: '',
        allergenName: '',
        severity: ''
      },
      pagination: {
        page: 1,
        limit: 10
      },
      tableData: {
        records: [],
        total: 0
      }
    }
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const response = await this.$http({
          url: this.$http.adornUrl('admin/patientMedical/getAllergyList'),
          method: 'get',
          params: this.$http.adornParams({
            page: this.pagination.page,
            limit: this.pagination.limit,
            ...this.searchForm
          })
        })
        
        if (response.data && response.data.code === 0) {
          this.tableData = response.data.data
        }
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        patientName: '',
        allergyType: '',
        allergenName: '',
        severity: ''
      }
      this.pagination.page = 1
      this.loadData()
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.limit = val
      this.loadData()
    },

    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },

    // 操作处理
    handleAdd() {
      this.$emit('add-allergy')
    },

    handleEdit(row) {
      this.$emit('edit-allergy', row)
    },

    handleDelete(row) {
      this.$emit('delete-allergy', row)
    },

    handleViewDetail(row) {
      this.$emit('view-allergy-detail', row)
    },

    handleViewPatient(row) {
      this.$emit('view-patient', row)
    },

    handleExport() {
      this.$emit('export-allergy-data', this.searchForm)
    },

    // 工具方法
    getAllergyTypeText(type) {
      if (!type) return '-'
      const item = this.allergyTypeOptions.find(opt => opt.code == type)
      return item ? item.value : '-'
    },

    getAllergyTypeColor(type) {
      switch(type) {
        case 1: return 'danger'   // 药物过敏
        case 2: return 'warning'  // 食物过敏
        case 3: return 'info'     // 环境过敏
        case 4: return 'primary'  // 接触性过敏
        case 5: return 'success'  // 其他过敏
        default: return 'info'
      }
    },

    getSeverityText(severity) {
      if (!severity) return '-'
      const item = this.severityOptions.find(opt => opt.code == severity)
      return item ? item.value : '-'
    },

    getSeverityColor(severity) {
      switch(severity) {
        case 1: return 'success'  // 轻微
        case 2: return 'warning'  // 中度
        case 3: return 'danger'   // 严重
        case 4: return 'danger'   // 危及生命
        default: return 'info'
      }
    }
  },

  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.allergy-management {
  padding: 20px;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-item span {
  white-space: nowrap;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.pagination-section {
  text-align: center;
  margin-top: 20px;
}

.text-ellipsis {
  display: inline-block;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
