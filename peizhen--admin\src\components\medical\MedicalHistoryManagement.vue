<template>
  <div class="medical-history-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="search-item">
          <span>患者姓名：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入患者姓名"
            v-model="searchForm.patientName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>病史类型：</span>
          <el-select style="width: 200px;" v-model="searchForm.historyType" placeholder="请选择病史类型" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in historyTypeOptions" :key="item.code" :label="item.value" :value="item.code">
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span>疾病名称：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入疾病名称"
            v-model="searchForm.diseaseName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>是否活跃：</span>
          <el-select style="width: 200px;" v-model="searchForm.isActive" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="活跃" value="1"></el-option>
            <el-option label="非活跃" value="0"></el-option>
          </el-select>
        </div>
      </div>
      
      <div class="action-buttons">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="handleSearch">
          查询
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd">
          添加病史记录
        </el-button>
        <el-button size="mini" type="success" icon="el-icon-download" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData.records" border stripe>
      <el-table-column prop="patientName" label="患者姓名" width="120">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            style="color: #409EFF;background: #fff;border: none;padding: 0;" 
            type="primary"
            @click="handleViewPatient(scope.row)">
            {{ scope.row.patientName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="historyType" label="病史类型" width="100">
        <template slot-scope="scope">
          <el-tag :type="getHistoryTypeColor(scope.row.historyType)">
            {{ getHistoryTypeText(scope.row.historyType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="diseaseName" label="疾病/手术名称" width="180">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.diseaseName" placement="top">
            <span class="text-ellipsis">{{ scope.row.diseaseName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="diseaseCode" label="疾病编码" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.diseaseCode || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="diagnosisDate" label="诊断时间" width="120"></el-table-column>
      <el-table-column prop="hospitalName" label="治疗医院" width="150">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.hospitalName" placement="top">
            <span class="text-ellipsis">{{ scope.row.hospitalName || '-' }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="doctorName" label="主治医生" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.doctorName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="treatmentResult" label="治疗结果" width="100">
        <template slot-scope="scope">
          <el-tag :type="getTreatmentResultType(scope.row.treatmentResult)">
            {{ getTreatmentResultText(scope.row.treatmentResult) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="severity" label="严重程度" width="100">
        <template slot-scope="scope">
          <el-tag :type="getSeverityType(scope.row.severity)">
            {{ getSeverityText(scope.row.severity) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isHereditary" label="遗传性" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isHereditary == 1 ? 'warning' : 'success'">
            {{ scope.row.isHereditary == 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isActive" label="是否活跃" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isActive == 1 ? 'success' : 'info'">
            {{ scope.row.isActive == 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="info" @click="handleViewDetail(scope.row)">详情</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination 
        @size-change="handleSizeChange" 
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 30, 40]" 
        :page-size="pagination.limit" 
        :current-page="pagination.page"
        layout="total,sizes, prev, pager, next,jumper" 
        :total="tableData.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MedicalHistoryManagement',
  props: {
    historyTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        patientName: '',
        historyType: '',
        diseaseName: '',
        isActive: ''
      },
      pagination: {
        page: 1,
        limit: 10
      },
      tableData: {
        records: [],
        total: 0
      }
    }
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const response = await this.$http({
          url: this.$http.adornUrl('admin/patientMedical/getMedicalHistoryList'),
          method: 'get',
          params: this.$http.adornParams({
            page: this.pagination.page,
            limit: this.pagination.limit,
            ...this.searchForm
          })
        })
        
        if (response.data && response.data.code === 0) {
          this.tableData = response.data.data
        }
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        patientName: '',
        historyType: '',
        diseaseName: '',
        isActive: ''
      }
      this.pagination.page = 1
      this.loadData()
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.limit = val
      this.loadData()
    },

    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },

    // 操作处理
    handleAdd() {
      this.$emit('add-medical-history')
    },

    handleEdit(row) {
      this.$emit('edit-medical-history', row)
    },

    handleDelete(row) {
      this.$emit('delete-medical-history', row)
    },

    handleViewDetail(row) {
      this.$emit('view-history-detail', row)
    },

    handleViewPatient(row) {
      this.$emit('view-patient', row)
    },

    handleExport() {
      this.$emit('export-history-data', this.searchForm)
    },

    // 工具方法
    getHistoryTypeText(type) {
      if (!type) return '-'
      const item = this.historyTypeOptions.find(opt => opt.code == type)
      return item ? item.value : '-'
    },

    getHistoryTypeColor(type) {
      switch(type) {
        case 1: return 'primary'  // 既往病史
        case 2: return 'warning'  // 手术史
        case 3: return 'danger'   // 外伤史
        case 4: return 'info'     // 输血史
        case 5: return 'success'  // 家族史
        default: return 'info'
      }
    },

    getTreatmentResultText(result) {
      const resultMap = {
        1: '治愈',
        2: '好转',
        3: '未愈',
        4: '死亡',
        5: '未知'
      }
      return resultMap[result] || '-'
    },

    getTreatmentResultType(result) {
      switch(result) {
        case 1: return 'success'
        case 2: return 'warning'
        case 3: return 'danger'
        case 4: return 'danger'
        case 5: return 'info'
        default: return 'info'
      }
    },

    getSeverityText(severity) {
      const severityMap = {
        1: '轻度',
        2: '中度',
        3: '重度'
      }
      return severityMap[severity] || '-'
    },

    getSeverityType(severity) {
      switch(severity) {
        case 1: return 'success'
        case 2: return 'warning'
        case 3: return 'danger'
        default: return 'info'
      }
    }
  },

  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.medical-history-management {
  padding: 20px;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-item span {
  white-space: nowrap;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.pagination-section {
  text-align: center;
  margin-top: 20px;
}

.text-ellipsis {
  display: inline-block;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
