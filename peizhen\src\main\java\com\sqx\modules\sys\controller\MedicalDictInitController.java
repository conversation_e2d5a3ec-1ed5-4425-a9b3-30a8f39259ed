package com.sqx.modules.sys.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.sys.service.impl.MedicalDictInitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 医疗档案数据字典初始化控制器
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/sys/medical-dict")
public class MedicalDictInitController {

    @Autowired
    private MedicalDictInitService medicalDictInitService;

    /**
     * 初始化医疗档案数据字典
     */
    @GetMapping("/init")
    public Result initMedicalDict() {
        try {
            medicalDictInitService.initMedicalDict();
            return Result.success("医疗档案数据字典初始化成功！共初始化15个字典类型，75个字典项。");
        } catch (Exception e) {
            return Result.error("初始化失败：" + e.getMessage());
        }
    }
}
