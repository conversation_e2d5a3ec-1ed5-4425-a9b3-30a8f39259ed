-- ========================================
-- 患者医疗档案增强功能 - 部署指南
-- 创建时间: 2024-08-05
-- 描述: 完整的数据库部署步骤和验证脚本
-- ========================================

-- ==========================================
-- 部署步骤说明
-- ==========================================
/*
1. 部署前准备
   - 备份现有数据库
   - 确认MySQL版本 >= 5.7
   - 确认有足够的磁盘空间
   - 确认数据库用户权限

2. 执行顺序
   a) medical_enhancement_migration.sql        -- 基础表结构
   b) medical_enhancement_migration_part2.sql  -- 扩展表结构
   c) medical_enhancement_indexes.sql          -- 索引优化
   d) medical_enhancement_initial_data.sql     -- 初始数据
   e) medical_enhancement_validation.sql       -- 验证和维护
   f) 本文件最后的验证脚本                      -- 部署验证

3. 部署后验证
   - 检查表结构完整性
   - 验证索引创建
   - 测试数据插入
   - 验证外键约束
*/

-- ==========================================
-- 部署前检查
-- ==========================================

-- 检查MySQL版本
SELECT VERSION() as mysql_version;

-- 检查当前数据库
SELECT DATABASE() as current_database;

-- 检查现有patient_info表结构
DESCRIBE patient_info;

-- 检查磁盘空间
SELECT 
    table_schema as database_name,
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as database_size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE()
GROUP BY table_schema;

-- ==========================================
-- 部署验证脚本
-- ==========================================

-- 1. 验证所有表是否创建成功
SELECT 
    table_name,
    table_type,
    engine,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN (
    'patient_info',
    'patient_health_status',
    'patient_medical_history',
    'patient_allergy',
    'patient_adverse_reaction',
    'patient_medication',
    'patient_vital_signs',
    'patient_test_result',
    'patient_vaccination',
    'patient_treatment_plan'
)
ORDER BY table_name;

-- 2. 验证索引创建
SELECT 
    table_name,
    index_name,
    column_name,
    seq_in_index,
    index_type
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name LIKE 'patient_%'
ORDER BY table_name, index_name, seq_in_index;

-- 3. 验证外键约束
SELECT 
    constraint_name,
    table_name,
    column_name,
    referenced_table_name,
    referenced_column_name
FROM information_schema.key_column_usage 
WHERE table_schema = DATABASE() 
AND referenced_table_name IS NOT NULL
AND table_name LIKE 'patient_%'
ORDER BY table_name;

-- 4. 验证触发器创建
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing
FROM information_schema.triggers 
WHERE trigger_schema = DATABASE()
AND trigger_name LIKE '%profile_completeness%'
ORDER BY trigger_name;

-- 5. 验证存储过程创建
SELECT 
    routine_name,
    routine_type,
    data_type,
    created,
    last_altered
FROM information_schema.routines 
WHERE routine_schema = DATABASE()
AND routine_name LIKE 'sp_%'
ORDER BY routine_name;

-- 6. 验证视图创建
SELECT 
    table_name,
    table_type,
    is_updatable
FROM information_schema.views 
WHERE table_schema = DATABASE()
AND table_name LIKE 'v_%'
ORDER BY table_name;

-- 7. 验证数据字典数据
SELECT 
    dt.dict_type,
    dt.dict_name,
    COUNT(dd.dict_data_id) as data_count
FROM sys_dict_type dt
LEFT JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type IN (
    'patient_risk_level',
    'health_status_level',
    'medical_history_type',
    'allergy_type',
    'allergy_severity',
    'medication_type',
    'medication_status',
    'test_type',
    'test_result_status',
    'vaccine_type',
    'vaccination_reaction',
    'treatment_plan_type',
    'treatment_plan_status',
    'marital_status',
    'insurance_type'
)
GROUP BY dt.dict_type, dt.dict_name
ORDER BY dt.dict_type;

-- ==========================================
-- 功能测试脚本
-- ==========================================

-- 测试插入患者基础信息
INSERT INTO patient_info (
    real_name, phone, id_number, gender, is_under_age,
    birth_date, ethnicity, marital_status, occupation,
    current_address, insurance_type, insurance_number,
    primary_contact_name, primary_contact_relation, primary_contact_phone,
    profile_completeness, risk_level, create_time, update_time
) VALUES (
    '测试患者', '13800138000', '110101199001011234', 1, 0,
    '1990-01-01', '汉族', 2, '软件工程师',
    '北京市朝阳区测试街道123号', 1, 'BJ123456789',
    '测试联系人', '配偶', '13900139000',
    0, 1, NOW(), NOW()
);

SET @test_patient_id = LAST_INSERT_ID();

-- 测试插入健康状态
INSERT INTO patient_health_status (
    patient_id, overall_health, height, weight, bmi,
    blood_type, rh_type, smoking_status, drinking_status,
    exercise_frequency, sleep_quality, mental_status, mobility_status,
    risk_level, create_time, update_time
) VALUES (
    @test_patient_id, 2, 170.00, 65.00, 22.49,
    'A', 1, 0, 1,
    3, 2, 2, 1,
    1, NOW(), NOW()
);

-- 测试插入病史记录
INSERT INTO patient_medical_history (
    patient_id, history_type, disease_name, disease_code,
    diagnosis_date, hospital_name, doctor_name,
    treatment_result, severity, is_hereditary, is_active,
    create_time, update_time
) VALUES (
    @test_patient_id, 1, '高血压', 'I10', 
    '2023-01-01', '测试医院', '张医生',
    2, 1, 0, 1,
    NOW(), NOW()
);

-- 测试插入过敏记录
INSERT INTO patient_allergy (
    patient_id, allergy_type, allergen_name, severity,
    symptoms, first_discovery_date, is_confirmed,
    confirmation_method, is_active, create_time, update_time
) VALUES (
    @test_patient_id, 1, '青霉素', 3,
    '皮疹、呼吸困难', '2020-05-01', 1,
    2, 1, NOW(), NOW()
);

-- 验证测试数据插入
SELECT 
    pi.patient_id,
    pi.real_name,
    pi.profile_completeness,
    pi.risk_level,
    phs.overall_health,
    pmh.disease_name,
    pa.allergen_name
FROM patient_info pi
LEFT JOIN patient_health_status phs ON pi.patient_id = phs.patient_id
LEFT JOIN patient_medical_history pmh ON pi.patient_id = pmh.patient_id
LEFT JOIN patient_allergy pa ON pi.patient_id = pa.patient_id
WHERE pi.patient_id = @test_patient_id;

-- 测试存储过程
CALL sp_assess_patient_risk(@test_patient_id);

-- 清理测试数据
DELETE FROM patient_allergy WHERE patient_id = @test_patient_id;
DELETE FROM patient_medical_history WHERE patient_id = @test_patient_id;
DELETE FROM patient_health_status WHERE patient_id = @test_patient_id;
DELETE FROM patient_info WHERE patient_id = @test_patient_id;

-- ==========================================
-- 部署完成检查
-- ==========================================

-- 最终验证 - 统计所有医疗档案相关表的记录数
SELECT 
    'patient_info' as table_name, COUNT(*) as record_count FROM patient_info WHERE is_delete = 0
UNION ALL
SELECT 
    'patient_health_status', COUNT(*) FROM patient_health_status WHERE is_delete = 0
UNION ALL
SELECT 
    'patient_medical_history', COUNT(*) FROM patient_medical_history WHERE is_delete = 0
UNION ALL
SELECT 
    'patient_allergy', COUNT(*) FROM patient_allergy WHERE is_delete = 0
UNION ALL
SELECT 
    'patient_medication', COUNT(*) FROM patient_medication WHERE is_delete = 0
UNION ALL
SELECT 
    'patient_vital_signs', COUNT(*) FROM patient_vital_signs WHERE is_delete = 0
UNION ALL
SELECT 
    'patient_test_result', COUNT(*) FROM patient_test_result WHERE is_delete = 0
UNION ALL
SELECT 
    'patient_vaccination', COUNT(*) FROM patient_vaccination WHERE is_delete = 0
UNION ALL
SELECT 
    'patient_treatment_plan', COUNT(*) FROM patient_treatment_plan WHERE is_delete = 0;

-- 部署完成提示
SELECT 
    '医疗档案增强功能数据库部署完成！' as deployment_status,
    NOW() as completion_time,
    VERSION() as mysql_version,
    DATABASE() as database_name;
