<template>
  <div class="health-status-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="search-item">
          <span>患者姓名：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入患者姓名"
            v-model="searchForm.patientName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>健康等级：</span>
          <el-select style="width: 200px;" v-model="searchForm.overallHealth" placeholder="请选择健康等级" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in healthLevelOptions" :key="item.code" :label="item.value" :value="item.code">
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span>血型：</span>
          <el-select style="width: 200px;" v-model="searchForm.bloodType" placeholder="请选择血型" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="A型" value="A"></el-option>
            <el-option label="B型" value="B"></el-option>
            <el-option label="AB型" value="AB"></el-option>
            <el-option label="O型" value="O"></el-option>
          </el-select>
        </div>
      </div>
      
      <div class="action-buttons">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="handleSearch">
          查询
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd">
          添加健康记录
        </el-button>
        <el-button size="mini" type="success" icon="el-icon-download" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData.records" border stripe>
      <el-table-column prop="patientName" label="患者姓名" width="120">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            style="color: #409EFF;background: #fff;border: none;padding: 0;" 
            type="primary"
            @click="handleViewPatient(scope.row)">
            {{ scope.row.patientName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="overallHealth" label="整体健康状况" width="120">
        <template slot-scope="scope">
          <el-tag :type="getHealthLevelType(scope.row.overallHealth)">
            {{ getHealthLevelText(scope.row.overallHealth) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="height" label="身高(cm)" width="100"></el-table-column>
      <el-table-column prop="weight" label="体重(kg)" width="100"></el-table-column>
      <el-table-column prop="bmi" label="BMI" width="80">
        <template slot-scope="scope">
          <span :class="getBMIClass(scope.row.bmi)">{{ scope.row.bmi || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="bloodType" label="血型" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.bloodType || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="rhType" label="RH血型" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.rhType == 1 ? '阳性' : scope.row.rhType == 0 ? '阴性' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="smokingStatus" label="吸烟状况" width="100">
        <template slot-scope="scope">
          <el-tag :type="getSmokingStatusType(scope.row.smokingStatus)">
            {{ getSmokingStatusText(scope.row.smokingStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="drinkingStatus" label="饮酒状况" width="100">
        <template slot-scope="scope">
          <el-tag :type="getDrinkingStatusType(scope.row.drinkingStatus)">
            {{ getDrinkingStatusText(scope.row.drinkingStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="exerciseFrequency" label="运动频率" width="100">
        <template slot-scope="scope">
          <span>{{ getExerciseFrequencyText(scope.row.exerciseFrequency) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="lastUpdateTime" label="最后更新" width="150">
        <template slot-scope="scope">
          <span>{{ formatDate(scope.row.lastUpdateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="info" @click="handleViewDetail(scope.row)">详情</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination 
        @size-change="handleSizeChange" 
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 30, 40]" 
        :page-size="pagination.limit" 
        :current-page="pagination.page"
        layout="total,sizes, prev, pager, next,jumper" 
        :total="tableData.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HealthStatusManagement',
  props: {
    healthLevelOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        patientName: '',
        overallHealth: '',
        bloodType: ''
      },
      pagination: {
        page: 1,
        limit: 10
      },
      tableData: {
        records: [],
        total: 0
      }
    }
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const response = await this.$http({
          url: this.$http.adornUrl('admin/patientMedical/getHealthStatusList'),
          method: 'get',
          params: this.$http.adornParams({
            page: this.pagination.page,
            limit: this.pagination.limit,
            ...this.searchForm
          })
        })
        
        if (response.data && response.data.code === 0) {
          this.tableData = response.data.data
        }
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        patientName: '',
        overallHealth: '',
        bloodType: ''
      }
      this.pagination.page = 1
      this.loadData()
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.limit = val
      this.loadData()
    },

    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },

    // 操作处理
    handleAdd() {
      this.$emit('add-health-status')
    },

    handleEdit(row) {
      this.$emit('edit-health-status', row)
    },

    handleDelete(row) {
      this.$emit('delete-health-status', row)
    },

    handleViewDetail(row) {
      this.$emit('view-health-detail', row)
    },

    handleViewPatient(row) {
      this.$emit('view-patient', row)
    },

    handleExport() {
      this.$emit('export-health-data', this.searchForm)
    },

    // 工具方法
    getHealthLevelText(level) {
      if (!level) return '未评估'
      const item = this.healthLevelOptions.find(opt => opt.code == level)
      return item ? item.value : '未评估'
    },

    getHealthLevelType(level) {
      switch(level) {
        case 1: return 'success'
        case 2: return 'success'
        case 3: return 'warning'
        case 4: return 'danger'
        case 5: return 'danger'
        default: return 'info'
      }
    },

    getBMIClass(bmi) {
      if (!bmi) return ''
      if (bmi < 18.5) return 'bmi-underweight'
      if (bmi < 24) return 'bmi-normal'
      if (bmi < 28) return 'bmi-overweight'
      return 'bmi-obese'
    },

    getSmokingStatusText(status) {
      const statusMap = {
        0: '从不吸烟',
        1: '已戒烟',
        2: '偶尔吸烟',
        3: '经常吸烟'
      }
      return statusMap[status] || '-'
    },

    getSmokingStatusType(status) {
      switch(status) {
        case 0: return 'success'
        case 1: return 'warning'
        case 2: return 'warning'
        case 3: return 'danger'
        default: return 'info'
      }
    },

    getDrinkingStatusText(status) {
      const statusMap = {
        0: '从不饮酒',
        1: '已戒酒',
        2: '偶尔饮酒',
        3: '经常饮酒'
      }
      return statusMap[status] || '-'
    },

    getDrinkingStatusType(status) {
      switch(status) {
        case 0: return 'success'
        case 1: return 'warning'
        case 2: return 'warning'
        case 3: return 'danger'
        default: return 'info'
      }
    },

    getExerciseFrequencyText(frequency) {
      const frequencyMap = {
        0: '从不运动',
        1: '偶尔运动',
        2: '每周1-2次',
        3: '每周3-4次',
        4: '每天运动'
      }
      return frequencyMap[frequency] || '-'
    },

    formatDate(dateStr) {
      if (!dateStr) return '-'
      return new Date(dateStr).toLocaleString('zh-CN')
    }
  },

  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.health-status-management {
  padding: 20px;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-item span {
  white-space: nowrap;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.pagination-section {
  text-align: center;
  margin-top: 20px;
}

.bmi-underweight {
  color: #409EFF;
}

.bmi-normal {
  color: #67C23A;
}

.bmi-overweight {
  color: #E6A23C;
}

.bmi-obese {
  color: #F56C6C;
}
</style>
