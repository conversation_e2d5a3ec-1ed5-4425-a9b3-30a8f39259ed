<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<!-- 患者基础信息 -->
			<el-tab-pane label="患者基础信息" name="basic">
				<div style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 20px;">
					<div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>患者姓名：</span>
							<el-input style="width: 200px;" @keydown.enter.native="searchBasic" placeholder="请输入患者姓名"
								v-model="basicSearch.realName"></el-input>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>联系电话：</span>
							<el-input style="width: 200px;" @keydown.enter.native="searchBasic" placeholder="请输入联系电话"
								v-model="basicSearch.phone"></el-input>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>风险等级：</span>
							<el-select style="width: 200px;" v-model="basicSearch.riskLevel" placeholder="请选择风险等级">
								<el-option label="全部" value=""></el-option>
								<el-option v-for="item in riskLevelOptions" :key="item.dictValue" 
									:label="item.dictLabel" :value="item.dictValue">
								</el-option>
							</el-select>&nbsp;&nbsp;
						</div>
						<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchBasic">
							查询
						</el-button>
						<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetBasic">
							重置
						</el-button>
						<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addBasicInfo()">
							添加患者
						</el-button>
					</div>
				</div>
				
				<el-table v-loading="basicLoading" :data="basicData.records" border>
					<el-table-column prop="patientId" label="患者ID" width="80"></el-table-column>
					<el-table-column prop="realName" label="患者姓名" width="120">
						<template slot-scope="scope">
							<el-button size="mini" style="color: #409EFF;background: #fff;border: none;padding: 0;" 
								type="primary" @click="viewDetail(scope.row)">
								{{ scope.row.realName }}
							</el-button>
						</template>
					</el-table-column>
					<el-table-column prop="sex" label="性别" width="60">
						<template slot-scope="scope">
							<span>{{ scope.row.sex == 1 ? '男' : '女' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="birthDate" label="出生日期" width="100"></el-table-column>
					<el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
					<el-table-column prop="profileCompleteness" label="档案完整度" width="120">
						<template slot-scope="scope">
							<el-progress :percentage="scope.row.profileCompleteness || 0" 
								:color="getCompletenessColor(scope.row.profileCompleteness)">
							</el-progress>
						</template>
					</el-table-column>
					<el-table-column prop="riskLevel" label="风险等级" width="100">
						<template slot-scope="scope">
							<el-tag :type="getRiskLevelType(scope.row.riskLevel)">
								{{ getRiskLevelText(scope.row.riskLevel) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="300" fixed="right">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editBasicInfo(scope.row)">编辑</el-button>
							<el-button size="mini" type="success" @click="viewMedicalProfile(scope.row)">医疗档案</el-button>
							<el-button size="mini" type="warning" @click="assessRisk(scope.row)">风险评估</el-button>
							<el-button size="mini" type="danger" @click="deleteBasicInfo(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleBasicSizeChange" @current-change="handleBasicCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="basicPage.limit" :current-page="basicPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="basicData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
			
			<!-- 健康状态管理 -->
			<el-tab-pane label="健康状态管理" name="health">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchHealth" placeholder="请输入患者姓名"
							v-model="healthSearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>健康状态：</span>
						<el-select style="width: 200px;" v-model="healthSearch.healthLevel" placeholder="请选择健康状态">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in healthStatusOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchHealth">
						查询
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetHealth">
						重置
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addHealthStatus()">
						添加健康状态
					</el-button>
				</div>
				
				<el-table v-loading="healthLoading" :data="healthData.records" border>
					<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
					<el-table-column prop="overallHealth" label="总体健康状况" width="120">
						<template slot-scope="scope">
							<span>{{ getHealthStatusText(scope.row.overallHealth) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="height" label="身高(cm)" width="100"></el-table-column>
					<el-table-column prop="weight" label="体重(kg)" width="100"></el-table-column>
					<el-table-column prop="bmi" label="BMI" width="80"></el-table-column>
					<el-table-column prop="bloodType" label="血型" width="80"></el-table-column>
					<el-table-column prop="smokingStatus" label="吸烟状况" width="100">
						<template slot-scope="scope">
							<span>{{ scope.row.smokingStatus == 1 ? '吸烟' : '不吸烟' }}</span>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="200">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editHealthStatus(scope.row)">编辑</el-button>
							<el-button size="mini" type="danger" @click="deleteHealthStatus(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleHealthSizeChange" @current-change="handleHealthCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="healthPage.limit" :current-page="healthPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="healthData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
			
			<!-- 病史记录管理 -->
			<el-tab-pane label="病史记录管理" name="history">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchHistory" placeholder="请输入患者姓名"
							v-model="historySearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>病史类型：</span>
						<el-select style="width: 200px;" v-model="historySearch.historyType" placeholder="请选择病史类型">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in historyTypeOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchHistory">
						查询
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetHistory">
						重置
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addMedicalHistory()">
						添加病史记录
					</el-button>
				</div>
				
				<el-table v-loading="historyLoading" :data="historyData.records" border>
					<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
					<el-table-column prop="historyType" label="病史类型" width="100">
						<template slot-scope="scope">
							<span>{{ getHistoryTypeText(scope.row.historyType) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="diseaseName" label="疾病名称" width="150"></el-table-column>
					<el-table-column prop="diagnosisDate" label="诊断日期" width="120"></el-table-column>
					<el-table-column prop="hospitalName" label="医院名称" width="150"></el-table-column>
					<el-table-column prop="isActive" label="是否活跃" width="80">
						<template slot-scope="scope">
							<el-tag :type="scope.row.isActive == 1 ? 'success' : 'info'">
								{{ scope.row.isActive == 1 ? '是' : '否' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="200">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editMedicalHistory(scope.row)">编辑</el-button>
							<el-button size="mini" type="danger" @click="deleteMedicalHistory(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleHistorySizeChange" @current-change="handleHistoryCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="historyPage.limit" :current-page="historyPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="historyData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
			
			<!-- 过敏信息管理 -->
			<el-tab-pane label="过敏信息管理" name="allergy">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchAllergy" placeholder="请输入患者姓名"
							v-model="allergySearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>过敏类型：</span>
						<el-select style="width: 200px;" v-model="allergySearch.allergyType" placeholder="请选择过敏类型">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in allergyTypeOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchAllergy">
						查询
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetAllergy">
						重置
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addAllergy()">
						添加过敏信息
					</el-button>
				</div>
				
				<el-table v-loading="allergyLoading" :data="allergyData.records" border>
					<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
					<el-table-column prop="allergyType" label="过敏类型" width="100">
						<template slot-scope="scope">
							<span>{{ getAllergyTypeText(scope.row.allergyType) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="allergenName" label="过敏原" width="150"></el-table-column>
					<el-table-column prop="severity" label="严重程度" width="100">
						<template slot-scope="scope">
							<el-tag :type="getAllergySeverityType(scope.row.severity)">
								{{ getAllergySeverityText(scope.row.severity) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="symptoms" label="症状" width="200"></el-table-column>
					<el-table-column prop="isActive" label="是否活跃" width="80">
						<template slot-scope="scope">
							<el-tag :type="scope.row.isActive == 1 ? 'success' : 'info'">
								{{ scope.row.isActive == 1 ? '是' : '否' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="200">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editAllergy(scope.row)">编辑</el-button>
							<el-button size="mini" type="danger" @click="deleteAllergy(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleAllergySizeChange" @current-change="handleAllergyCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="allergyPage.limit" :current-page="allergyPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="allergyData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
export default {
	data() {
		return {
			activeName: 'basic',
			
			// 数据字典选项
			riskLevelOptions: [],
			healthStatusOptions: [],
			historyTypeOptions: [],
			allergyTypeOptions: [],
			allergySeverityOptions: [],
			medicationTypeOptions: [],
			medicationStatusOptions: [],
			testTypeOptions: [],
			testResultStatusOptions: [],
			vaccineTypeOptions: [],
			vaccinationReactionOptions: [],
			treatmentPlanTypeOptions: [],
			treatmentPlanStatusOptions: [],
			maritalStatusOptions: [],
			insuranceTypeOptions: [],
			
			// 基础信息
			basicSearch: {
				realName: '',
				phone: '',
				riskLevel: ''
			},
			basicPage: {
				page: 1,
				limit: 10
			},
			basicData: {},
			basicLoading: false,
			
			// 健康状态
			healthSearch: {
				patientName: '',
				healthLevel: ''
			},
			healthPage: {
				page: 1,
				limit: 10
			},
			healthData: {},
			healthLoading: false,
			
			// 病史记录
			historySearch: {
				patientName: '',
				historyType: ''
			},
			historyPage: {
				page: 1,
				limit: 10
			},
			historyData: {},
			historyLoading: false,
			
			// 过敏信息
			allergySearch: {
				patientName: '',
				allergyType: ''
			},
			allergyPage: {
				page: 1,
				limit: 10
			},
			allergyData: {},
			allergyLoading: false
		}
	},
	methods: {
		// 标签页切换
		handleClick(tab) {
			switch(tab.name) {
				case 'basic':
					this.loadBasicData()
					break
				case 'health':
					this.loadHealthData()
					break
				case 'history':
					this.loadHistoryData()
					break
				case 'allergy':
					this.loadAllergyData()
					break
			}
		},
		
		// 加载数据字典
		loadDictData() {
			const dictTypes = [
				'patient_risk_level',
				'health_status_level',
				'medical_history_type',
				'allergy_type',
				'allergy_severity',
				'medication_type',
				'medication_status',
				'test_type',
				'test_result_status',
				'vaccine_type',
				'vaccination_reaction',
				'treatment_plan_type',
				'treatment_plan_status',
				'marital_status',
				'insurance_type'
			]

			dictTypes.forEach(type => {
				this.$http({
					url: this.$http.adornUrl('sys/dict/list'),
					method: 'get',
					params: this.$http.adornParams({
						type: type
					})
				}).then(({data}) => {
					if (data && data.code === 0) {
						// 转换数据格式以适配现有的 sys_dict 表结构
						const options = data.data.map(item => ({
							dictValue: item.code,
							dictLabel: item.value || item.name
						}))

						switch(type) {
							case 'patient_risk_level':
								this.riskLevelOptions = options
								break
							case 'health_status_level':
								this.healthStatusOptions = options
								break
							case 'medical_history_type':
								this.historyTypeOptions = options
								break
							case 'allergy_type':
								this.allergyTypeOptions = options
								break
							case 'allergy_severity':
								this.allergySeverityOptions = options
								break
							case 'medication_type':
								this.medicationTypeOptions = options
								break
							case 'medication_status':
								this.medicationStatusOptions = options
								break
							case 'test_type':
								this.testTypeOptions = options
								break
							case 'test_result_status':
								this.testResultStatusOptions = options
								break
							case 'vaccine_type':
								this.vaccineTypeOptions = options
								break
							case 'vaccination_reaction':
								this.vaccinationReactionOptions = options
								break
							case 'treatment_plan_type':
								this.treatmentPlanTypeOptions = options
								break
							case 'treatment_plan_status':
								this.treatmentPlanStatusOptions = options
								break
							case 'marital_status':
								this.maritalStatusOptions = options
								break
							case 'insurance_type':
								this.insuranceTypeOptions = options
								break
						}
					}
				})
			})
		},

		// 工具方法 - 根据字典值获取文本
		getDictText(options, value) {
			const item = options.find(opt => opt.dictValue == value)
			return item ? item.dictLabel : value
		},

		// 获取风险等级文本
		getRiskLevelText(level) {
			return this.getDictText(this.riskLevelOptions, level)
		},

		// 获取风险等级类型
		getRiskLevelType(level) {
			switch(level) {
				case '1': return 'success'
				case '2': return 'warning'
				case '3': return 'danger'
				default: return 'info'
			}
		},

		// 获取健康状态文本
		getHealthStatusText(level) {
			return this.getDictText(this.healthStatusOptions, level)
		},

		// 获取病史类型文本
		getHistoryTypeText(type) {
			return this.getDictText(this.historyTypeOptions, type)
		},

		// 获取过敏类型文本
		getAllergyTypeText(type) {
			return this.getDictText(this.allergyTypeOptions, type)
		},

		// 获取过敏严重程度文本
		getAllergySeverityText(severity) {
			return this.getDictText(this.allergySeverityOptions, severity)
		},

		// 获取过敏严重程度类型
		getAllergySeverityType(severity) {
			switch(severity) {
				case '1': return 'success'
				case '2': return 'warning'
				case '3': return 'danger'
				case '4': return 'danger'
				default: return 'info'
			}
		},

		// 获取完整度颜色
		getCompletenessColor(percentage) {
			if (percentage >= 80) return '#67c23a'
			if (percentage >= 60) return '#e6a23c'
			return '#f56c6c'
		},

		// ==================== 基础信息管理 ====================
		// 加载基础信息数据
		loadBasicData() {
			this.basicLoading = true
			this.$http({
				url: this.$http.adornUrl('admin/patientInfo/getPatientList'),
				method: 'get',
				params: this.$http.adornParams({
					page: this.basicPage.page,
					limit: this.basicPage.limit,
					realName: this.basicSearch.realName,
					phone: this.basicSearch.phone,
					riskLevel: this.basicSearch.riskLevel
				})
			}).then(({data}) => {
				this.basicLoading = false
				if (data && data.code === 0) {
					this.basicData = data.data
				}
			}).catch(() => {
				this.basicLoading = false
			})
		},

		// 搜索基础信息
		searchBasic() {
			this.basicPage.page = 1
			this.loadBasicData()
		},

		// 重置基础信息搜索
		resetBasic() {
			this.basicSearch = {
				realName: '',
				phone: '',
				riskLevel: ''
			}
			this.basicPage.page = 1
			this.loadBasicData()
		},

		// 基础信息分页
		handleBasicSizeChange(val) {
			this.basicPage.limit = val
			this.loadBasicData()
		},

		handleBasicCurrentChange(val) {
			this.basicPage.page = val
			this.loadBasicData()
		},

		// 添加基础信息
		addBasicInfo() {
			this.$router.push({
				path: '/patientBasicForm',
				query: { action: 'add' }
			})
		},

		// 编辑基础信息
		editBasicInfo(row) {
			this.$router.push({
				path: '/patientBasicForm',
				query: {
					action: 'edit',
					patientId: row.patientId
				}
			})
		},

		// 删除基础信息
		deleteBasicInfo(row) {
			this.$confirm(`确定删除患者 ${row.realName} 的信息?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientInfo/deletePatient'),
					method: 'post',
					data: this.$http.adornData({
						patientId: row.patientId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadBasicData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// 查看详情
		viewDetail(row) {
			this.$router.push({
				path: '/patientDetail',
				query: {
					patientId: row.patientId
				}
			})
		},

		// 查看医疗档案
		viewMedicalProfile(row) {
			this.$router.push({
				path: '/patientMedicalDetail',
				query: {
					patientId: row.patientId,
					patientName: row.realName
				}
			})
		},

		// 风险评估
		assessRisk(row) {
			this.$confirm(`确定对患者 ${row.realName} 进行风险评估?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/assessMedicalRisk'),
					method: 'post',
					data: this.$http.adornData({
						patientId: row.patientId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('风险评估完成')
						this.loadBasicData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// ==================== 健康状态管理 ====================
		// 加载健康状态数据
		loadHealthData() {
			this.healthLoading = true
			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getHealthStatusList'),
				method: 'get',
				params: this.$http.adornParams({
					page: this.healthPage.page,
					limit: this.healthPage.limit,
					patientName: this.healthSearch.patientName,
					healthLevel: this.healthSearch.healthLevel
				})
			}).then(({data}) => {
				this.healthLoading = false
				if (data && data.code === 0) {
					this.healthData = data.data
				}
			}).catch(() => {
				this.healthLoading = false
			})
		},

		// 搜索健康状态
		searchHealth() {
			this.healthPage.page = 1
			this.loadHealthData()
		},

		// 重置健康状态搜索
		resetHealth() {
			this.healthSearch = {
				patientName: '',
				healthLevel: ''
			}
			this.healthPage.page = 1
			this.loadHealthData()
		},

		// 健康状态分页
		handleHealthSizeChange(val) {
			this.healthPage.limit = val
			this.loadHealthData()
		},

		handleHealthCurrentChange(val) {
			this.healthPage.page = val
			this.loadHealthData()
		},

		// 添加健康状态
		addHealthStatus() {
			this.$router.push({
				path: '/healthStatusForm',
				query: { action: 'add' }
			})
		},

		// 编辑健康状态
		editHealthStatus(row) {
			this.$router.push({
				path: '/healthStatusForm',
				query: {
					action: 'edit',
					statusId: row.statusId
				}
			})
		},

		// 删除健康状态
		deleteHealthStatus(row) {
			this.$confirm(`确定删除该健康状态记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/deleteHealthStatus'),
					method: 'post',
					data: this.$http.adornData({
						statusId: row.statusId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadHealthData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// ==================== 病史记录管理 ====================
		// 加载病史记录数据
		loadHistoryData() {
			this.historyLoading = true
			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getMedicalHistoryList'),
				method: 'get',
				params: this.$http.adornParams({
					page: this.historyPage.page,
					limit: this.historyPage.limit,
					patientName: this.historySearch.patientName,
					historyType: this.historySearch.historyType
				})
			}).then(({data}) => {
				this.historyLoading = false
				if (data && data.code === 0) {
					this.historyData = data.data
				}
			}).catch(() => {
				this.historyLoading = false
			})
		},

		// 搜索病史记录
		searchHistory() {
			this.historyPage.page = 1
			this.loadHistoryData()
		},

		// 重置病史记录搜索
		resetHistory() {
			this.historySearch = {
				patientName: '',
				historyType: ''
			}
			this.historyPage.page = 1
			this.loadHistoryData()
		},

		// 病史记录分页
		handleHistorySizeChange(val) {
			this.historyPage.limit = val
			this.loadHistoryData()
		},

		handleHistoryCurrentChange(val) {
			this.historyPage.page = val
			this.loadHistoryData()
		},

		// 添加病史记录
		addMedicalHistory() {
			this.$router.push({
				path: '/medicalHistoryForm',
				query: { action: 'add' }
			})
		},

		// 编辑病史记录
		editMedicalHistory(row) {
			this.$router.push({
				path: '/medicalHistoryForm',
				query: {
					action: 'edit',
					historyId: row.historyId
				}
			})
		},

		// 删除病史记录
		deleteMedicalHistory(row) {
			this.$confirm(`确定删除该病史记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/deleteMedicalHistory'),
					method: 'post',
					data: this.$http.adornData({
						historyId: row.historyId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadHistoryData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// ==================== 过敏信息管理 ====================
		// 加载过敏信息数据
		loadAllergyData() {
			this.allergyLoading = true
			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getAllergyList'),
				method: 'get',
				params: this.$http.adornParams({
					page: this.allergyPage.page,
					limit: this.allergyPage.limit,
					patientName: this.allergySearch.patientName,
					allergyType: this.allergySearch.allergyType
				})
			}).then(({data}) => {
				this.allergyLoading = false
				if (data && data.code === 0) {
					this.allergyData = data.data
				}
			}).catch(() => {
				this.allergyLoading = false
			})
		},

		// 搜索过敏信息
		searchAllergy() {
			this.allergyPage.page = 1
			this.loadAllergyData()
		},

		// 重置过敏信息搜索
		resetAllergy() {
			this.allergySearch = {
				patientName: '',
				allergyType: ''
			}
			this.allergyPage.page = 1
			this.loadAllergyData()
		},

		// 过敏信息分页
		handleAllergySizeChange(val) {
			this.allergyPage.limit = val
			this.loadAllergyData()
		},

		handleAllergyCurrentChange(val) {
			this.allergyPage.page = val
			this.loadAllergyData()
		},

		// 添加过敏信息
		addAllergy() {
			this.$router.push({
				path: '/allergyForm',
				query: { action: 'add' }
			})
		},

		// 编辑过敏信息
		editAllergy(row) {
			this.$router.push({
				path: '/allergyForm',
				query: {
					action: 'edit',
					allergyId: row.allergyId
				}
			})
		},

		// 删除过敏信息
		deleteAllergy(row) {
			this.$confirm(`确定删除该过敏信息记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/deleteAllergy'),
					method: 'post',
					data: this.$http.adornData({
						allergyId: row.allergyId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadAllergyData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		}
	},
	mounted() {
		this.loadDictData()
		this.loadBasicData()
	}
}
</script>

<style scoped>
.el-table {
	margin-top: 20px;
}
</style>
