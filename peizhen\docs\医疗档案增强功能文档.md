# 患者医疗档案增强功能文档

## 功能概述

本次增强为陪诊系统的PatientInfo实体添加了全面的医疗信息管理功能，将基础的患者信息扩展为完整的医疗档案系统，支持临床决策和患者护理管理。

## 技术架构

### 核心组件
- **实体层 (Entity)**: 8个新增医疗信息实体
- **数据访问层 (DAO)**: 对应的Mapper接口
- **服务层 (Service)**: 综合医疗档案服务
- **控制器层 (Controller)**: RESTful API接口
- **数据传输层 (DTO)**: 验证和数据传输对象
- **工具层 (Util)**: 医疗数据验证工具

### 数据库设计
- **主表增强**: patient_info表新增21个医疗档案字段
- **新增表**: 8个专业医疗信息表
- **关系设计**: 外键约束确保数据完整性
- **索引优化**: 复合索引提升查询性能

## 功能模块详解

### 1. 健康状态管理 (PatientHealthStatus)

**功能描述**: 管理患者的整体健康状态和生活方式信息

**核心字段**:
- 基础指标: 身高、体重、BMI、血型
- 生活方式: 吸烟、饮酒、运动、睡眠状况
- 健康评估: 整体健康状态、精神状态、活动能力
- 风险评估: 慢性疾病、风险等级

**API接口**:
```
POST /app/patientMedical/saveHealthStatus - 保存健康状态
GET  /app/patientMedical/getHealthStatus - 获取健康状态
```

**使用示例**:
```json
{
  "patientId": 123,
  "overallHealth": 2,
  "height": 170.5,
  "weight": 65.0,
  "bloodType": "A",
  "rhType": 1,
  "smokingStatus": 0,
  "drinkingStatus": 1,
  "exerciseFrequency": 3,
  "sleepQuality": 2,
  "mentalStatus": 2,
  "mobilityStatus": 1
}
```

### 2. 病史记录管理 (PatientMedicalHistory)

**功能描述**: 记录患者的既往病史、手术史、家族史等医疗历史

**病史类型**:
1. 既往病史 - 过往疾病记录
2. 手术史 - 手术治疗记录
3. 外伤史 - 外伤事件记录
4. 输血史 - 输血治疗记录
5. 家族史 - 家族遗传疾病

**核心字段**:
- 疾病信息: 疾病名称、ICD-10编码、诊断时间
- 治疗信息: 治疗医院、主治医生、治疗结果
- 严重程度: 轻度/中度/重度
- 遗传信息: 是否遗传性、家族关系

**API接口**:
```
POST /app/patientMedical/saveMedicalHistory - 保存病史记录
GET  /app/patientMedical/getMedicalHistoryList - 分页查询病史
GET  /app/patientMedical/getActiveMedicalHistory - 获取活跃病史
POST /app/patientMedical/deleteMedicalHistory - 删除病史记录
```

### 3. 过敏信息管理 (PatientAllergy)

**功能描述**: 管理患者的过敏信息，预防过敏反应

**过敏类型**:
1. 药物过敏 - 药物过敏反应
2. 食物过敏 - 食物过敏反应
3. 环境过敏 - 环境因素过敏
4. 接触性过敏 - 接触性过敏反应
5. 其他过敏 - 其他类型过敏

**严重程度**:
1. 轻微 - 轻微不适
2. 中度 - 明显症状
3. 严重 - 严重反应
4. 危及生命 - 过敏性休克等

**API接口**:
```
POST /app/patientMedical/saveAllergy - 保存过敏记录
GET  /app/patientMedical/getAllergyList - 分页查询过敏记录
GET  /app/patientMedical/getActiveAllergies - 获取活跃过敏
POST /app/patientMedical/checkAllergyConflict - 检查过敏冲突
POST /app/patientMedical/deleteAllergy - 删除过敏记录
```

### 4. 用药记录管理 (PatientMedication)

**功能描述**: 管理患者的用药信息，包括当前用药和用药历史

**药物分类**:
1. 处方药 - 需处方的药物
2. 非处方药 - OTC药物
3. 中药 - 中药制剂
4. 生物制品 - 生物制品

**用药状态**:
1. 正在使用 - 当前正在服用
2. 已停用 - 已停止使用
3. 暂停使用 - 临时停用

**API接口**:
```
POST /app/patientMedical/saveMedication - 保存用药记录
GET  /app/patientMedical/getMedicationList - 分页查询用药记录
GET  /app/patientMedical/getCurrentMedications - 获取当前用药
POST /app/patientMedical/checkMedicationConflict - 检查用药冲突
POST /app/patientMedical/discontinueMedication - 停用药物
POST /app/patientMedical/deleteMedication - 删除用药记录
```

### 5. 生命体征管理 (PatientVitalSigns)

**功能描述**: 记录和监测患者的生命体征数据

**监测指标**:
- 血压: 收缩压、舒张压
- 心率: 每分钟心跳次数
- 体温: 体温测量值
- 呼吸: 呼吸频率
- 血氧: 血氧饱和度
- 疼痛: 疼痛评分(0-10分)
- 体重: 体重变化监测

**API接口**:
```
POST /app/patientMedical/saveVitalSigns - 保存生命体征
GET  /app/patientMedical/getVitalSignsList - 分页查询生命体征
GET  /app/patientMedical/getLatestVitalSigns - 获取最新生命体征
POST /app/patientMedical/deleteVitalSigns - 删除生命体征记录
```

### 6. 检查结果管理 (PatientTestResult)

**功能描述**: 管理各类医学检查和检验结果

**检查类型**:
1. 血液检查 - 血常规、生化等
2. 尿液检查 - 尿常规等
3. 影像检查 - X光、CT、MRI等
4. 心电图 - 心电图检查
5. 病理检查 - 病理学检查
6. 其他检查 - 其他类型检查

**结果状态**:
1. 正常 - 检查结果正常
2. 异常偏高 - 结果高于正常范围
3. 异常偏低 - 结果低于正常范围
4. 临界值 - 处于临界状态
5. 无法判断 - 结果无法明确判断

**API接口**:
```
POST /app/patientMedical/saveTestResult - 保存检查结果
GET  /app/patientMedical/getTestResultList - 分页查询检查结果
GET  /app/patientMedical/getRecentTestResults - 获取最近检查结果
POST /app/patientMedical/deleteTestResult - 删除检查结果
```

### 7. 疫苗接种管理 (PatientVaccination)

**功能描述**: 管理患者的疫苗接种记录和计划

**疫苗类型**:
1. 常规疫苗 - 常规免疫疫苗
2. 应急疫苗 - 应急接种疫苗
3. 旅行疫苗 - 旅行相关疫苗
4. 职业疫苗 - 职业暴露疫苗

**接种反应**:
0. 无反应 - 无不良反应
1. 轻微反应 - 轻微不适
2. 中度反应 - 中度不良反应
3. 严重反应 - 严重不良反应

**API接口**:
```
POST /app/patientMedical/saveVaccination - 保存疫苗接种记录
GET  /app/patientMedical/getVaccinationList - 分页查询接种记录
GET  /app/patientMedical/getVaccinationHistory - 获取接种历史
POST /app/patientMedical/deleteVaccination - 删除接种记录
```

### 8. 治疗方案管理 (PatientTreatmentPlan)

**功能描述**: 管理患者的治疗方案和治疗进度

**方案类型**:
1. 药物治疗 - 药物治疗方案
2. 手术治疗 - 手术治疗方案
3. 物理治疗 - 物理康复治疗
4. 心理治疗 - 心理干预治疗
5. 综合治疗 - 综合治疗方案

**方案状态**:
1. 计划中 - 治疗方案制定中
2. 进行中 - 治疗方案执行中
3. 已完成 - 治疗方案已完成
4. 已暂停 - 治疗方案暂停
5. 已取消 - 治疗方案取消

**API接口**:
```
POST /app/patientMedical/saveTreatmentPlan - 保存治疗方案
GET  /app/patientMedical/getTreatmentPlanList - 分页查询治疗方案
GET  /app/patientMedical/getCurrentTreatmentPlans - 获取当前治疗方案
POST /app/patientMedical/updateTreatmentPlanStatus - 更新方案状态
POST /app/patientMedical/deleteTreatmentPlan - 删除治疗方案
```

## 综合功能

### 完整医疗档案
```
GET /app/patientMedical/getCompleteMedicalProfile?patientId=123
```
返回患者的完整医疗档案，包括所有医疗信息模块的数据。

### 档案完整度评估
```
POST /app/patientMedical/updateProfileCompleteness
```
自动计算和更新患者医疗档案的完整度百分比。

### 医疗风险评估
```
POST /app/patientMedical/assessMedicalRisk
```
基于患者的医疗信息进行风险评估，返回风险等级和建议。

### 医疗数据统计
```
GET /app/patientMedical/getMedicalDataStatistics?patientId=123
```
提供患者医疗数据的统计分析。

## 数据验证

系统提供了完整的数据验证机制：

### 基础验证
- 日期格式验证 (yyyy-MM-dd)
- 时间格式验证 (yyyy-MM-dd HH:mm:ss)
- 手机号码格式验证
- 身份证号码格式验证
- 血型格式验证
- ICD-10疾病编码验证

### 医疗数据验证
- 生命体征数值范围验证
- 身体指标合理性验证
- 药物剂量安全性验证
- 过敏信息完整性验证
- 治疗方案逻辑性验证

### 敏感信息保护
- 手机号码脱敏显示
- 身份证号码脱敏显示
- 姓名脱敏显示

## 部署说明

### 1. 数据库迁移
执行以下SQL脚本：
```sql
-- 执行 medical_enhancement_migration.sql
-- 执行 medical_enhancement_migration_part2.sql
```

### 2. 依赖配置
确保项目包含以下依赖：
- MyBatis Plus 3.2.0+
- Spring Boot 2.6.11+
- Validation API
- Swagger 2.x

### 3. 配置更新
无需额外配置，使用现有的数据源和MyBatis配置。

## 使用示例

### 创建完整医疗档案
```java
// 1. 保存健康状态
PatientHealthStatus healthStatus = new PatientHealthStatus();
healthStatus.setPatientId(123L);
healthStatus.setHeight(new BigDecimal("170"));
healthStatus.setWeight(new BigDecimal("65"));
// ... 设置其他字段
medicalProfileService.saveHealthStatus(healthStatus);

// 2. 添加病史记录
PatientMedicalHistory history = new PatientMedicalHistory();
history.setPatientId(123L);
history.setHistoryType(1); // 既往病史
history.setDiseaseName("高血压");
// ... 设置其他字段
medicalProfileService.saveMedicalHistory(history);

// 3. 添加过敏记录
PatientAllergy allergy = new PatientAllergy();
allergy.setPatientId(123L);
allergy.setAllergyType(1); // 药物过敏
allergy.setAllergenName("青霉素");
allergy.setSeverity(3); // 严重
// ... 设置其他字段
medicalProfileService.saveAllergy(allergy);
```

### 查询完整医疗档案
```java
Result result = medicalProfileService.getCompleteMedicalProfile(123L);
PatientInfo patientInfo = (PatientInfo) result.get("data");

// 获取各类医疗信息
PatientHealthStatus healthStatus = patientInfo.getHealthStatus();
List<PatientMedicalHistory> historyList = patientInfo.getMedicalHistoryList();
List<PatientAllergy> allergyList = patientInfo.getAllergyList();
// ... 其他医疗信息
```

## 注意事项

### 数据安全
1. 医疗数据属于敏感信息，需要严格的访问控制
2. 建议启用数据加密存储
3. 定期备份医疗数据
4. 遵循医疗数据保护法规

### 性能优化
1. 大量数据查询时使用分页
2. 合理使用索引提升查询性能
3. 定期清理历史数据
4. 考虑数据归档策略

### 扩展性
1. 预留了扩展字段便于后续功能增强
2. 模块化设计支持独立功能扩展
3. 标准化的API接口便于第三方集成

## 技术支持

如有技术问题，请联系开发团队或查阅相关技术文档。
