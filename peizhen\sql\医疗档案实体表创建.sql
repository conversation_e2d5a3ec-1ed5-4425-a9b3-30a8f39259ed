-- 医疗档案系统实体表创建脚本
-- 创建日期: 2024-08-05
-- 说明: 为8个医疗信息实体类创建数据库表

-- 1. 患者健康状态表
CREATE TABLE patient_health_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    patient_id BIGINT NOT NULL COMMENT '患者ID，关联patient表',
    health_level VARCHAR(10) NOT NULL COMMENT '健康等级，关联字典表',
    check_date DATE NOT NULL COMMENT '检查日期',
    check_items VARCHAR(500) COMMENT '检查项目',
    result TEXT COMMENT '检查结果',
    doctor VA<PERSON>HA<PERSON>(100) COMMENT '检查医生',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_check_date (check_date),
    INDEX idx_health_level (health_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者健康状态表';

-- 2. 患者病史记录表
CREATE TABLE patient_medical_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    patient_id BIGINT NOT NULL COMMENT '患者ID，关联patient表',
    history_type VARCHAR(10) NOT NULL COMMENT '病史类型，关联字典表',
    diagnosis VARCHAR(200) NOT NULL COMMENT '诊断',
    description TEXT COMMENT '病史描述',
    record_date DATE COMMENT '记录日期',
    doctor VARCHAR(100) COMMENT '记录医生',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_history_type (history_type),
    INDEX idx_record_date (record_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者病史记录表';

-- 3. 患者过敏信息表
CREATE TABLE patient_allergy (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    patient_id BIGINT NOT NULL COMMENT '患者ID，关联patient表',
    allergy_type VARCHAR(10) NOT NULL COMMENT '过敏类型，关联字典表',
    allergen VARCHAR(200) NOT NULL COMMENT '过敏原',
    severity VARCHAR(10) NOT NULL COMMENT '严重程度，关联字典表',
    symptoms TEXT COMMENT '过敏症状',
    record_date DATE COMMENT '记录日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_allergy_type (allergy_type),
    INDEX idx_severity (severity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者过敏信息表';

-- 4. 患者用药记录表
CREATE TABLE patient_medication (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    patient_id BIGINT NOT NULL COMMENT '患者ID，关联patient表',
    medication_name VARCHAR(200) NOT NULL COMMENT '药物名称',
    medication_type VARCHAR(10) COMMENT '药物类型，关联字典表',
    dosage VARCHAR(100) COMMENT '用药剂量',
    frequency VARCHAR(100) COMMENT '用药频次',
    status VARCHAR(10) NOT NULL COMMENT '用药状态，关联字典表',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    prescribing_doctor VARCHAR(100) COMMENT '开药医生',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_medication_type (medication_type),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者用药记录表';

-- 5. 患者生命体征表
CREATE TABLE patient_vital_signs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    patient_id BIGINT NOT NULL COMMENT '患者ID，关联patient表',
    measurement_date DATETIME NOT NULL COMMENT '测量日期时间',
    blood_pressure_systolic INT COMMENT '收缩压(mmHg)',
    blood_pressure_diastolic INT COMMENT '舒张压(mmHg)',
    heart_rate INT COMMENT '心率(次/分)',
    temperature DECIMAL(4,1) COMMENT '体温(℃)',
    respiratory_rate INT COMMENT '呼吸频率(次/分)',
    oxygen_saturation DECIMAL(5,2) COMMENT '血氧饱和度(%)',
    weight DECIMAL(5,2) COMMENT '体重(kg)',
    height DECIMAL(5,2) COMMENT '身高(cm)',
    bmi DECIMAL(5,2) COMMENT 'BMI指数',
    measurement_by VARCHAR(100) COMMENT '测量人员',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_measurement_date (measurement_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者生命体征表';

-- 6. 患者检查结果表
CREATE TABLE patient_test_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    patient_id BIGINT NOT NULL COMMENT '患者ID，关联patient表',
    test_type VARCHAR(10) NOT NULL COMMENT '检查类型，关联字典表',
    test_name VARCHAR(200) NOT NULL COMMENT '检查项目',
    result VARCHAR(500) COMMENT '检查结果',
    result_status VARCHAR(10) COMMENT '结果状态，关联字典表',
    reference_range VARCHAR(200) COMMENT '参考范围',
    unit VARCHAR(50) COMMENT '单位',
    test_date DATE NOT NULL COMMENT '检查日期',
    doctor VARCHAR(100) COMMENT '检查医生',
    laboratory VARCHAR(100) COMMENT '检验科室',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_test_type (test_type),
    INDEX idx_test_date (test_date),
    INDEX idx_result_status (result_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者检查结果表';

-- 7. 患者疫苗接种表
CREATE TABLE patient_vaccination (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    patient_id BIGINT NOT NULL COMMENT '患者ID，关联patient表',
    vaccine_name VARCHAR(200) NOT NULL COMMENT '疫苗名称',
    vaccine_type VARCHAR(10) COMMENT '疫苗类型，关联字典表',
    manufacturer VARCHAR(200) COMMENT '生产厂家',
    batch_number VARCHAR(100) COMMENT '批次号',
    vaccination_date DATE NOT NULL COMMENT '接种日期',
    next_due_date DATE COMMENT '下次接种日期',
    vaccination_site VARCHAR(10) COMMENT '接种部位，关联字典表',
    dose VARCHAR(50) COMMENT '剂量',
    reaction TEXT COMMENT '不良反应',
    vaccinated_by VARCHAR(100) COMMENT '接种人员',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_vaccine_type (vaccine_type),
    INDEX idx_vaccination_date (vaccination_date),
    INDEX idx_next_due_date (next_due_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者疫苗接种表';

-- 8. 患者治疗方案表
CREATE TABLE patient_treatment_plan (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    patient_id BIGINT NOT NULL COMMENT '患者ID，关联patient表',
    plan_name VARCHAR(200) NOT NULL COMMENT '方案名称',
    plan_type VARCHAR(10) COMMENT '方案类型，关联字典表',
    diagnosis VARCHAR(500) COMMENT '诊断',
    treatment_goals TEXT COMMENT '治疗目标',
    treatment_methods TEXT COMMENT '治疗方法',
    medications TEXT COMMENT '用药方案',
    follow_up_plan TEXT COMMENT '随访计划',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    status VARCHAR(10) NOT NULL COMMENT '方案状态，关联字典表',
    created_by VARCHAR(100) COMMENT '制定医生',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_plan_type (plan_type),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者治疗方案表';

-- 添加表注释说明
ALTER TABLE patient_health_status COMMENT = '患者健康状态表 - 记录患者的健康评级和体检信息';
ALTER TABLE patient_medical_history COMMENT = '患者病史记录表 - 记录患者的既往史、现病史、家族史等';
ALTER TABLE patient_allergy COMMENT = '患者过敏信息表 - 记录患者的过敏史和过敏反应';
ALTER TABLE patient_medication COMMENT = '患者用药记录表 - 记录患者的用药历史和当前用药';
ALTER TABLE patient_vital_signs COMMENT = '患者生命体征表 - 记录患者的生命体征监测数据';
ALTER TABLE patient_test_result COMMENT = '患者检查结果表 - 记录患者的各项检查和化验结果';
ALTER TABLE patient_vaccination COMMENT = '患者疫苗接种表 - 记录患者的疫苗接种历史';
ALTER TABLE patient_treatment_plan COMMENT = '患者治疗方案表 - 记录患者的治疗计划和方案';
