-- 患者风险等级字典初始化脚本
-- 确保患者风险等级字典数据存在

-- 检查是否已存在患者风险等级字典
SELECT '检查现有数据' as '说明', COUNT(*) as '记录数' 
FROM sys_dict 
WHERE type = '患者风险等级';

-- 如果不存在，则创建患者风险等级字典数据
-- 清理可能存在的旧数据
DELETE FROM sys_dict WHERE type = '患者风险等级';

-- 插入父级记录
INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
VALUES ('患者风险等级', '患者风险等级', NULL, NULL, 1, '患者医疗风险等级分类', 0);

-- 插入子级记录（使用子查询获取父级ID）
INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '低风险', '患者风险等级', '1', '低风险', 1, '低风险', id 
FROM sys_dict WHERE type = '患者风险等级' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '中风险', '患者风险等级', '2', '中风险', 2, '中风险', id 
FROM sys_dict WHERE type = '患者风险等级' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '高风险', '患者风险等级', '3', '高风险', 3, '高风险', id 
FROM sys_dict WHERE type = '患者风险等级' AND parent_id = 0;

-- 验证插入结果
SELECT '患者风险等级字典' as '字典类型', id, name, type, code, value, order_num, parent_id 
FROM sys_dict 
WHERE type = '患者风险等级' 
ORDER BY parent_id, order_num;

-- 统计结果
SELECT 
    type as '字典类型',
    COUNT(*) as '记录数量',
    SUM(CASE WHEN parent_id = 0 THEN 1 ELSE 0 END) as '父级记录',
    SUM(CASE WHEN parent_id > 0 THEN 1 ELSE 0 END) as '子级记录'
FROM sys_dict 
WHERE type = '患者风险等级'
GROUP BY type;
