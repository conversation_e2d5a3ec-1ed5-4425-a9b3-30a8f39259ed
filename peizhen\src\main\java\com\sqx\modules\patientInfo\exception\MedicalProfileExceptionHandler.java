package com.sqx.modules.patientInfo.exception;

import com.sqx.common.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 患者医疗档案模块异常处理器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.sqx.modules.patientInfo")
public class MedicalProfileExceptionHandler {

    /**
     * 处理数据验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleValidationException(MethodArgumentNotValidException e) {
        log.warn("医疗档案数据验证失败: {}", e.getMessage());
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
        
        return Result.error("数据验证失败: " + errorMessage);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleBindException(BindException e) {
        log.warn("医疗档案数据绑定失败: {}", e.getMessage());
        
        String errorMessage = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
        
        return Result.error("数据格式错误: " + errorMessage);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("医疗档案约束验证失败: {}", e.getMessage());
        
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));
        
        return Result.error("数据约束验证失败: " + errorMessage);
    }

    /**
     * 处理数据完整性违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleDataIntegrityViolationException(DataIntegrityViolationException e) {
        log.error("医疗档案数据完整性违反: {}", e.getMessage(), e);
        
        String message = "数据操作失败";
        if (e.getCause() instanceof SQLIntegrityConstraintViolationException) {
            SQLIntegrityConstraintViolationException sqlEx = (SQLIntegrityConstraintViolationException) e.getCause();
            if (sqlEx.getMessage().contains("Duplicate entry")) {
                message = "数据已存在，请检查后重试";
            } else if (sqlEx.getMessage().contains("foreign key constraint")) {
                message = "关联数据不存在，请检查患者信息";
            }
        }
        
        return Result.error(message);
    }

    /**
     * 处理医疗档案业务异常
     */
    @ExceptionHandler(MedicalProfileBusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleMedicalProfileBusinessException(MedicalProfileBusinessException e) {
        log.warn("医疗档案业务异常: {}", e.getMessage());
        return Result.error(e.getMessage());
    }

    /**
     * 处理医疗数据验证异常
     */
    @ExceptionHandler(MedicalDataValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleMedicalDataValidationException(MedicalDataValidationException e) {
        log.warn("医疗数据验证异常: {}", e.getMessage());
        return Result.error("医疗数据验证失败: " + e.getMessage());
    }

    /**
     * 处理患者信息不存在异常
     */
    @ExceptionHandler(PatientNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result handlePatientNotFoundException(PatientNotFoundException e) {
        log.warn("患者信息不存在: {}", e.getMessage());
        return Result.error("患者信息不存在: " + e.getMessage());
    }

    /**
     * 处理医疗档案权限异常
     */
    @ExceptionHandler(MedicalProfileAccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result handleMedicalProfileAccessDeniedException(MedicalProfileAccessDeniedException e) {
        log.warn("医疗档案访问权限不足: {}", e.getMessage());
        return Result.error("访问权限不足: " + e.getMessage());
    }

    /**
     * 处理通用运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handleRuntimeException(RuntimeException e) {
        log.error("医疗档案模块运行时异常: {}", e.getMessage(), e);
        return Result.error("系统内部错误，请稍后重试");
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handleException(Exception e) {
        log.error("医疗档案模块未知异常: {}", e.getMessage(), e);
        return Result.error("系统异常，请联系管理员");
    }

    /**
     * 医疗档案业务异常
     */
    public static class MedicalProfileBusinessException extends RuntimeException {
        public MedicalProfileBusinessException(String message) {
            super(message);
        }
        
        public MedicalProfileBusinessException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 医疗数据验证异常
     */
    public static class MedicalDataValidationException extends RuntimeException {
        public MedicalDataValidationException(String message) {
            super(message);
        }
        
        public MedicalDataValidationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 患者信息不存在异常
     */
    public static class PatientNotFoundException extends RuntimeException {
        public PatientNotFoundException(String message) {
            super(message);
        }
        
        public PatientNotFoundException(Long patientId) {
            super("患者ID: " + patientId);
        }
    }

    /**
     * 医疗档案访问权限异常
     */
    public static class MedicalProfileAccessDeniedException extends RuntimeException {
        public MedicalProfileAccessDeniedException(String message) {
            super(message);
        }
        
        public MedicalProfileAccessDeniedException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
