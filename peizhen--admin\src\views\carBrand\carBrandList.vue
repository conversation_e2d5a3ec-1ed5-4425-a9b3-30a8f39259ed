<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="患者基础信息" name="first">
				<div style="display: flex;justify-content: space-between;align-items: center">
					<div>
						<!-- <div style="position: relative;display: inline-block;margin: 3px;">
							<span>就诊人名称：</span>
							<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入就诊人名称"
								v-model="realNameT"></el-input>&nbsp;&nbsp;
						</div> -->
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>患者姓名：</span>
							<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入患者姓名"
								v-model="realNameT"></el-input>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>所属用户昵称：</span>
							<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入所属用户昵称"
								v-model="userName"></el-input>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>患者电话：</span>
							<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入患者电话"
								v-model="phone"></el-input>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>风险等级：</span>
							<el-select style="width: 200px;" v-model="riskLevel" placeholder="请选择风险等级">
								<el-option label="全部" value=""></el-option>
								<el-option v-for="(item,index) in riskLevelList" :key="index" :label="item.value" :value="item.code">
								</el-option>
							</el-select>&nbsp;&nbsp;
						</div>
						<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="select">
							查询
						</el-button>
						<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="cleans">
							重置
						</el-button>
						<el-button style='margin: 10px 0;' :disabled="!isAuth('carBrandList:add')" size="mini"
							type="primary" icon="document" @click="addPatient()">添加患者
						</el-button>

						<el-button style='margin: 10px 0;' :disabled="!isAuth('carBrandList:export')" size="mini"
							type="success" icon="document" @click="exportData()">导出数据
						</el-button>
					</div>
				</div>
				<el-table v-loading="tableDataLoading" :data="tableData.records">
					<el-table-column fixed prop="patientId" label="患者ID" width="80">
					</el-table-column>
					<el-table-column prop="realName" label="患者姓名" width="120">
						<template slot-scope="scope">
							<el-button size="mini" style="color: #409EFF;background: #fff;border: none;padding: 0;" type="primary"
								@click="viewMedicalProfile(scope.row)">
								{{ scope.row.realName }}
							</el-button>
						</template>
					</el-table-column>
					<el-table-column prop="userName" label="所属用户" width="120">
						<template slot-scope="scope">
							<el-button size="mini" style="color: #008000;background: #fff;border: none;padding: 0;" type="primary"
								:disabled="!isAuth('userList:details')" @click="updates(scope.row.userId)">
								{{scope.row.userName ? scope.row.userName : '未绑定'}}
							</el-button>
						</template>
					</el-table-column>
					<el-table-column prop="sex" label="性别" width="60">
						<template slot-scope="scope">
							<span>{{ scope.row.sex == 1 ? '男' : '女' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="birthDate" label="出生日期" width="100">
					</el-table-column>
					<el-table-column prop="phone" label="联系电话" width="120">
					</el-table-column>
					<el-table-column prop="profileCompleteness" label="档案完整度" width="100">
						<template slot-scope="scope">
							<el-progress :percentage="scope.row.profileCompleteness || 0"
								:color="getCompletenessColor(scope.row.profileCompleteness)">
							</el-progress>
						</template>
					</el-table-column>
					<el-table-column prop="riskLevel" label="风险等级" width="100">
						<template slot-scope="scope">
							<el-tag :type="getRiskLevelType(scope.row.riskLevel)">
								{{ getRiskLevelText(scope.row.riskLevel) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="lastMedicalUpdateTime" label="最后更新" >
						<template slot-scope="scope">
							<span>{{ formatDate(scope.row.lastMedicalUpdateTime) }}</span>
						</template>
					</el-table-column>

					<el-table-column label="操作" width="350" fixed="right">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" :disabled="!isAuth('carBrandList:update')"
								@click="addPatient(scope.row)" style="margin: 2px;">编辑
							</el-button>
							<el-button size="mini" type="success" 
								@click="viewMedicalProfile(scope.row)" style="margin: 2px;">医疗档案
							</el-button>
							<el-button size="mini" type="warning"
								@click="assessRisk(scope.row)" style="margin: 2px;">风险评估
							</el-button>
							<el-button size="mini" type="danger" :disabled="!isAuth('carBrandList:delete')"
								@click="deletes(scope.row)" style="margin: 2px;">删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
				<div style="text-align: center;margin-top: 10px;">
					<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="limit" :current-page="page"
						layout="total,sizes, prev, pager, next,jumper" :total="tableData.total">
					</el-pagination>
				</div>
			</el-tab-pane>

			<!-- 健康状态管理 -->
			<el-tab-pane label="健康状态管理" name="health">
				<div style="padding: 20px;">
					<el-alert
						title="健康状态管理"
						type="info"
						description="管理患者的健康状态评级、体检记录等信息"
						show-icon
						style="margin-bottom: 20px;">
					</el-alert>

					<div style="margin-bottom: 20px;">
						<el-button type="primary" icon="el-icon-plus" @click="addHealthRecord">添加健康记录</el-button>
						<el-button type="success" icon="el-icon-download" @click="exportHealthData">导出数据</el-button>
					</div>

					<el-table :data="healthRecords" border style="width: 100%">
						<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
						<el-table-column prop="healthLevel" label="健康等级" width="100">
							<template slot-scope="scope">
								<el-tag :type="getHealthLevelType(scope.row.healthLevel)">
									{{ getHealthLevelText(scope.row.healthLevel) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="checkDate" label="检查日期" width="120"></el-table-column>
						<el-table-column prop="checkItems" label="检查项目"></el-table-column>
						<el-table-column prop="result" label="检查结果"></el-table-column>
						<el-table-column prop="doctor" label="检查医生" width="100"></el-table-column>
						<el-table-column label="操作" width="150">
							<template slot-scope="scope">
								<el-button size="mini" @click="editHealthRecord(scope.row)">编辑</el-button>
								<el-button size="mini" type="danger" @click="deleteHealthRecord(scope.row)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</el-tab-pane>

			<!-- 病史记录管理 -->
			<el-tab-pane label="病史记录管理" name="history">
				<div style="padding: 20px;">
					<el-alert
						title="病史记录管理"
						type="warning"
						description="管理患者的既往史、现病史、家族史等医疗历史信息"
						show-icon
						style="margin-bottom: 20px;">
					</el-alert>

					<div style="margin-bottom: 20px;">
						<el-button type="primary" icon="el-icon-plus" @click="addMedicalHistory">添加病史记录</el-button>
						<el-button type="success" icon="el-icon-download" @click="exportHistoryData">导出数据</el-button>
					</div>

					<el-table :data="medicalHistories" border style="width: 100%">
						<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
						<el-table-column prop="historyType" label="病史类型" width="100">
							<template slot-scope="scope">
								<el-tag :type="getHistoryTypeColor(scope.row.historyType)">
									{{ getHistoryTypeText(scope.row.historyType) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="diagnosis" label="诊断" width="150"></el-table-column>
						<el-table-column prop="description" label="病史描述"></el-table-column>
						<el-table-column prop="recordDate" label="记录日期" width="120"></el-table-column>
						<el-table-column prop="doctor" label="记录医生" width="100"></el-table-column>
						<el-table-column label="操作" width="150">
							<template slot-scope="scope">
								<el-button size="mini" @click="editMedicalHistory(scope.row)">编辑</el-button>
								<el-button size="mini" type="danger" @click="deleteMedicalHistory(scope.row)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</el-tab-pane>

			<!-- 过敏信息管理 -->
			<el-tab-pane label="过敏信息管理" name="allergy">
				<div style="padding: 20px;">
					<el-alert
						title="过敏信息管理"
						type="error"
						description="管理患者的过敏史，包括药物过敏、食物过敏等重要安全信息"
						show-icon
						style="margin-bottom: 20px;">
					</el-alert>

					<div style="margin-bottom: 20px;">
						<el-button type="primary" icon="el-icon-plus" @click="addAllergyRecord">添加过敏记录</el-button>
						<el-button type="success" icon="el-icon-download" @click="exportAllergyData">导出数据</el-button>
					</div>

					<el-table :data="allergyRecords" border style="width: 100%">
						<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
						<el-table-column prop="allergyType" label="过敏类型" width="100">
							<template slot-scope="scope">
								<el-tag :type="getAllergyTypeColor(scope.row.allergyType)">
									{{ getAllergyTypeText(scope.row.allergyType) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="allergen" label="过敏原" width="150"></el-table-column>
						<el-table-column prop="severity" label="严重程度" width="100">
							<template slot-scope="scope">
								<el-tag :type="getSeverityColor(scope.row.severity)">
									{{ getSeverityText(scope.row.severity) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="symptoms" label="过敏症状"></el-table-column>
						<el-table-column prop="recordDate" label="记录日期" width="120"></el-table-column>
						<el-table-column label="操作" width="150">
							<template slot-scope="scope">
								<el-button size="mini" @click="editAllergyRecord(scope.row)">编辑</el-button>
								<el-button size="mini" type="danger" @click="deleteAllergyRecord(scope.row)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</el-tab-pane>

			<!-- 用药检查管理 -->
			<el-tab-pane label="用药检查管理" name="medication">
				<div style="padding: 20px;">
					<el-alert
						title="用药检查管理"
						type="success"
						description="管理患者的用药记录和各项检查结果"
						show-icon
						style="margin-bottom: 20px;">
					</el-alert>

					<div style="margin-bottom: 20px;">
						<el-button type="primary" icon="el-icon-plus" @click="addMedicationRecord">添加用药记录</el-button>
						<el-button type="primary" icon="el-icon-plus" @click="addTestRecord">添加检查记录</el-button>
						<el-button type="success" icon="el-icon-download" @click="exportMedicationData">导出数据</el-button>
					</div>

					<el-tabs v-model="medicationActiveTab" type="card">
						<el-tab-pane label="用药记录" name="medicationTab">
							<el-table :data="medicationRecords" border style="width: 100%">
								<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
								<el-table-column prop="medicationName" label="药物名称" width="150"></el-table-column>
								<el-table-column prop="medicationType" label="药物类型" width="100"></el-table-column>
								<el-table-column prop="dosage" label="用药剂量" width="100"></el-table-column>
								<el-table-column prop="frequency" label="用药频次" width="100"></el-table-column>
								<el-table-column prop="status" label="用药状态" width="100">
									<template slot-scope="scope">
										<el-tag :type="getMedicationStatusColor(scope.row.status)">
											{{ getMedicationStatusText(scope.row.status) }}
										</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
								<el-table-column label="操作" width="150">
									<template slot-scope="scope">
										<el-button size="mini" @click="editMedicationRecord(scope.row)">编辑</el-button>
										<el-button size="mini" type="danger" @click="deleteMedicationRecord(scope.row)">删除</el-button>
									</template>
								</el-table-column>
							</el-table>
						</el-tab-pane>

						<el-tab-pane label="检查记录" name="testTab">
							<el-table :data="testRecords" border style="width: 100%">
								<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
								<el-table-column prop="testType" label="检查类型" width="120"></el-table-column>
								<el-table-column prop="testName" label="检查项目" width="150"></el-table-column>
								<el-table-column prop="result" label="检查结果" width="120"></el-table-column>
								<el-table-column prop="resultStatus" label="结果状态" width="100">
									<template slot-scope="scope">
										<el-tag :type="getTestResultColor(scope.row.resultStatus)">
											{{ getTestResultText(scope.row.resultStatus) }}
										</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="testDate" label="检查日期" width="120"></el-table-column>
								<el-table-column prop="doctor" label="检查医生" width="100"></el-table-column>
								<el-table-column label="操作" width="150">
									<template slot-scope="scope">
										<el-button size="mini" @click="editTestRecord(scope.row)">编辑</el-button>
										<el-button size="mini" type="danger" @click="deleteTestRecord(scope.row)">删除</el-button>
									</template>
								</el-table-column>
							</el-table>
						</el-tab-pane>
					</el-tabs>
				</div>
			</el-tab-pane>
		</el-tabs>
		<!--添加/编辑患者-->
		<el-dialog :title="titles" :visible.sync="dialogVisible" width="70%" center>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">所属用户：</span>
				<el-input v-model="numberValidateForm.userName" @focus="userBtn()" style="width:45%;"
					placeholder="请点击选择所属用户" readonly>
					<el-button slot="append" icon="el-icon-search" @click="userBtn()">选择用户</el-button>
				</el-input>
				<el-button v-if="numberValidateForm.userId" @click="clearSelectedUser()"
					style="margin-left: 10px;" size="small" type="warning" icon="el-icon-delete">
					清除选择
				</el-button>
			</div>

			<!-- 用户信息显示区域 -->
			<div v-if="numberValidateForm.userId" style="margin-bottom: 15px; padding: 15px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 8px; border-left: 4px solid #409EFF;">
				<div style="color: #303133; font-size: 14px; margin-bottom: 10px; display: flex; align-items: center;">
					<i class="el-icon-user" style="color: #409EFF; margin-right: 8px; font-size: 16px;"></i>
					<strong>已选择用户信息</strong>
					<el-tag size="mini" type="success" style="margin-left: 10px;">已绑定</el-tag>
				</div>
				<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; color: #606266;">
					<div style="display: flex; align-items: center;">
						<i class="el-icon-postcard" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>用户ID：</strong>{{ numberValidateForm.userId }}</span>
					</div>
					<div style="display: flex; align-items: center;">
						<i class="el-icon-user-solid" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>昵称：</strong>{{ numberValidateForm.userName || '未设置' }}</span>
					</div>
					<div v-if="selectedUserInfo.realName" style="display: flex; align-items: center;">
						<i class="el-icon-s-custom" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>真实姓名：</strong>{{ selectedUserInfo.realName }}</span>
					</div>
					<div v-if="selectedUserInfo.sex" style="display: flex; align-items: center;">
						<i class="el-icon-male" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>性别：</strong>{{ selectedUserInfo.sex == 1 ? '男' : '女' }}</span>
					</div>
					<div v-if="selectedUserInfo.phone" style="display: flex; align-items: center;">
						<i class="el-icon-phone" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>手机号：</strong>{{ selectedUserInfo.phone }}</span>
					</div>
					<div v-if="selectedUserInfo.birthDate" style="display: flex; align-items: center;">
						<i class="el-icon-date" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>出生日期：</strong>{{ selectedUserInfo.birthDate }}</span>
					</div>
				</div>
			</div>
			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">患者姓名：</span>
						<el-input v-model="numberValidateForm.realName" style="width:70%;" placeholder="请输入患者姓名">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">性别：</span>
						<el-radio-group v-model="numberValidateForm.sex">
							<el-radio :label="1">男</el-radio>
							<el-radio :label="2">女</el-radio>
						</el-radio-group>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">出生日期：</span>
						<el-date-picker v-model="numberValidateForm.birthDate" type="date"
							placeholder="选择出生日期" style="width:70%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
						</el-date-picker>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">是否成年：</span>
						<el-radio-group v-model="numberValidateForm.isUnderAge">
							<el-radio :label="0">是</el-radio>
							<el-radio :label="1">否</el-radio>
						</el-radio-group>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">身份证号：</span>
						<el-input v-model="numberValidateForm.idNumber" style="width:70%;" placeholder="请输入身份证号码">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">联系电话：</span>
						<el-input v-model="numberValidateForm.phone" style="width:70%;" placeholder="请输入联系电话">
						</el-input>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">民族：</span>
						<el-input v-model="numberValidateForm.ethnicity" style="width:70%;" placeholder="请输入民族">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">婚姻状况：</span>
						<el-select style="width: 70%" v-model="numberValidateForm.maritalStatus" placeholder="请选择婚姻状况">
							<el-option v-for="(item,index) in maritalStatusList" :key="index" :label="item.value" :value="item.code">
							</el-option>
						</el-select>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">职业：</span>
						<el-input v-model="numberValidateForm.occupation" style="width:70%;" placeholder="请输入职业">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">医保类型：</span>
						<el-select style="width: 70%" v-model="numberValidateForm.insuranceType" placeholder="请选择医保类型">
							<el-option v-for="(item,index) in insuranceTypeList" :key="index" :label="item.value" :value="item.code">
							</el-option>
						</el-select>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="24">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">现住址：</span>
						<el-input v-model="numberValidateForm.currentAddress" style="width:80%;" placeholder="请输入现住址">
						</el-input>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">紧急联系人：</span>
						<el-input v-model="numberValidateForm.primaryContactName" style="width:70%;" placeholder="请输入紧急联系人姓名">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">联系人关系：</span>
						<el-select style="width: 70%" v-model="numberValidateForm.primaryContactRelation" placeholder="请选择关系">
							<el-option v-for="(item,index) in gxList" :key="index" :label="item.value" :value="item.value">
							</el-option>
						</el-select>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">联系人电话：</span>
						<el-input v-model="numberValidateForm.primaryContactPhone" style="width:70%;" placeholder="请输入联系人电话">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">医保号：</span>
						<el-input v-model="numberValidateForm.insuranceNumber" style="width:70%;" placeholder="请输入医保号">
						</el-input>
					</div>
				</el-col>
			</el-row>
			<span slot="footer" class="dialog-footer" style="margin-top: 30px;text-align: center;">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="addNoticeTo()">确 定</el-button>
			</span>
		</el-dialog>
		<!--选择用户-->
		<el-dialog title="选择用户" :visible.sync="dialogVisibles" width="70%" center>
			<div style="margin:2% 0;display: inline-block;">
				<span>手机号:</span>
				<el-input style="width: 150px;" @keydown.enter.native="selectUser" clearable placeholder="请输入手机号"
					v-model="phoneUser"></el-input>
			</div>&emsp;&emsp;
			<div style="margin:2% 0;display: inline-block;">
				<span>昵称:</span>
				<el-input style="width: 150px;" @keydown.enter.native="selectUser" clearable placeholder="请输入昵称"
					v-model="userNameUser"></el-input>
			</div>&emsp;&emsp;
			<div style="display: inline-block;">
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="selectUser">查询
				</el-button>
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="cleansUser">重置
				</el-button>
			</div>
			<el-table v-loading="tableDataLoadings" :data="userList.list">
				<el-table-column fixed prop="userId" label="编号" width="80">
				</el-table-column>
				<el-table-column fixed prop="userName" label="昵称" width="120">
					<template slot-scope="scope">
						<span style="color: #f56c6c;">{{ scope.row.userName ? scope.row.userName : '未绑定' }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="realName" label="真实姓名" width="100">
					<template slot-scope="scope">
						<span style="color: #409EFF;">{{ scope.row.realName || '未设置' }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="sex" label="性别" width="60">
					<template slot-scope="scope">
						<span v-if="scope.row.sex==1">男</span>
						<span v-else-if="scope.row.sex==2">女</span>
						<span v-else>未设置</span>
					</template>
				</el-table-column>
				<el-table-column prop="birthDate" label="出生日期" width="100">
					<template slot-scope="scope">
						<span>{{ scope.row.birthDate || '未设置' }}</span>
					</template>
				</el-table-column>
				<el-table-column label="头像" >
					<template slot-scope="scope">
						<img v-if="scope.row.avatar==null" src="~@/assets/img/avatar.png" alt="" width="40" height="40">
						<img v-else :src="scope.row.avatar" alt="" width="40" height="40">
					</template>
				</el-table-column>
				<el-table-column prop="phone" label="手机号" >
					<template slot-scope="scope">
						<el-button size="mini" style="color: #008000;background: #fff;border: none;padding: 0;" type="primary"
							:disabled="!isAuth('userList:details')" @click="updates(scope.row.userId)">
							{{scope.row.phone ? scope.row.phone : '未绑定'}}
						</el-button>
					</template>
				</el-table-column>
				<el-table-column prop="idNumber" label="身份证号">
					<template slot-scope="scope">
						<span>{{ scope.row.idNumber ? scope.row.idNumber.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') : '未设置' }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作"  fixed="right">
					<template slot-scope="scope">
						<el-button size="mini" type="primary" :disabled="!isAuth('carBrandList:update')"
							@click="amendBanner(scope.row)" style="margin: 5px;">选择
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="text-align: center;margin-top: 10px;">
				<el-pagination @size-change="handleSizeChanges" @current-change="handleCurrentChanges"
					:page-sizes="[10, 20, 30, 40]" :page-size="limit1" :current-page="page1"
					layout="total,sizes, prev, pager, next,jumper" :total="userList.totalCount">
				</el-pagination>
			</div>
		</el-dialog>

		<!-- 健康记录对话框 -->
		<el-dialog :title="healthRecordDialogTitle" :visible.sync="healthRecordDialogVisible" width="60%">
			<el-form :model="healthRecordForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="healthRecordForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="健康等级" required>
							<el-select v-model="healthRecordForm.healthLevel" placeholder="请选择健康等级" style="width: 100%">
								<el-option label="优秀" value="1"></el-option>
								<el-option label="良好" value="2"></el-option>
								<el-option label="一般" value="3"></el-option>
								<el-option label="较差" value="4"></el-option>
								<el-option label="差" value="5"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="检查日期" required>
							<el-date-picker v-model="healthRecordForm.checkDate" type="date" placeholder="选择检查日期"
								style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
							</el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="检查医生">
							<el-input v-model="healthRecordForm.doctor" placeholder="请输入检查医生"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="检查项目">
					<el-input v-model="healthRecordForm.checkItems" placeholder="请输入检查项目"></el-input>
				</el-form-item>
				<el-form-item label="检查结果">
					<el-input type="textarea" v-model="healthRecordForm.result" placeholder="请输入检查结果" :rows="3"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="healthRecordDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveHealthRecord">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 病史记录对话框 -->
		<el-dialog :title="medicalHistoryDialogTitle" :visible.sync="medicalHistoryDialogVisible" width="60%">
			<el-form :model="medicalHistoryForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="medicalHistoryForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="病史类型" required>
							<el-select v-model="medicalHistoryForm.historyType" placeholder="请选择病史类型" style="width: 100%">
								<el-option label="既往史" value="1"></el-option>
								<el-option label="现病史" value="2"></el-option>
								<el-option label="家族史" value="3"></el-option>
								<el-option label="过敏史" value="4"></el-option>
								<el-option label="手术史" value="5"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="诊断" required>
							<el-input v-model="medicalHistoryForm.diagnosis" placeholder="请输入诊断"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="记录日期">
							<el-date-picker v-model="medicalHistoryForm.recordDate" type="date" placeholder="选择记录日期"
								style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
							</el-date-picker>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="记录医生">
					<el-input v-model="medicalHistoryForm.doctor" placeholder="请输入记录医生"></el-input>
				</el-form-item>
				<el-form-item label="病史描述">
					<el-input type="textarea" v-model="medicalHistoryForm.description" placeholder="请输入病史描述" :rows="4"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="medicalHistoryDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveMedicalHistory">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 过敏记录对话框 -->
		<el-dialog :title="allergyRecordDialogTitle" :visible.sync="allergyRecordDialogVisible" width="60%">
			<el-form :model="allergyRecordForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="allergyRecordForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="过敏类型" required>
							<el-select v-model="allergyRecordForm.allergyType" placeholder="请选择过敏类型" style="width: 100%">
								<el-option label="药物过敏" value="1"></el-option>
								<el-option label="食物过敏" value="2"></el-option>
								<el-option label="环境过敏" value="3"></el-option>
								<el-option label="接触过敏" value="4"></el-option>
								<el-option label="其他过敏" value="5"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="过敏原" required>
							<el-input v-model="allergyRecordForm.allergen" placeholder="请输入过敏原"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="严重程度" required>
							<el-select v-model="allergyRecordForm.severity" placeholder="请选择严重程度" style="width: 100%">
								<el-option label="轻微" value="1"></el-option>
								<el-option label="中度" value="2"></el-option>
								<el-option label="严重" value="3"></el-option>
								<el-option label="危及生命" value="4"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="记录日期">
					<el-date-picker v-model="allergyRecordForm.recordDate" type="date" placeholder="选择记录日期"
						style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
					</el-date-picker>
				</el-form-item>
				<el-form-item label="过敏症状">
					<el-input type="textarea" v-model="allergyRecordForm.symptoms" placeholder="请输入过敏症状" :rows="3"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="allergyRecordDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveAllergyRecord">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 用药记录对话框 -->
		<el-dialog :title="medicationRecordDialogTitle" :visible.sync="medicationRecordDialogVisible" width="60%">
			<el-form :model="medicationRecordForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="medicationRecordForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="药物名称" required>
							<el-input v-model="medicationRecordForm.medicationName" placeholder="请输入药物名称"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="药物类型">
							<el-select v-model="medicationRecordForm.medicationType" placeholder="请选择药物类型" style="width: 100%">
								<el-option label="处方药" value="处方药"></el-option>
								<el-option label="非处方药" value="非处方药"></el-option>
								<el-option label="中药" value="中药"></el-option>
								<el-option label="保健品" value="保健品"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="用药状态">
							<el-select v-model="medicationRecordForm.status" placeholder="请选择用药状态" style="width: 100%">
								<el-option label="正在使用" value="1"></el-option>
								<el-option label="已停用" value="2"></el-option>
								<el-option label="暂停使用" value="3"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="用药剂量">
							<el-input v-model="medicationRecordForm.dosage" placeholder="请输入用药剂量"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="用药频次">
							<el-input v-model="medicationRecordForm.frequency" placeholder="请输入用药频次"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="开始日期">
					<el-date-picker v-model="medicationRecordForm.startDate" type="date" placeholder="选择开始日期"
						style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
					</el-date-picker>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="medicationRecordDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveMedicationRecord">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 检查记录对话框 -->
		<el-dialog :title="testRecordDialogTitle" :visible.sync="testRecordDialogVisible" width="60%">
			<el-form :model="testRecordForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="testRecordForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="检查类型">
							<el-select v-model="testRecordForm.testType" placeholder="请选择检查类型" style="width: 100%">
								<el-option label="血液检查" value="血液检查"></el-option>
								<el-option label="尿液检查" value="尿液检查"></el-option>
								<el-option label="影像检查" value="影像检查"></el-option>
								<el-option label="心电图" value="心电图"></el-option>
								<el-option label="超声检查" value="超声检查"></el-option>
								<el-option label="其他检查" value="其他检查"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="检查项目" required>
							<el-input v-model="testRecordForm.testName" placeholder="请输入检查项目"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="结果状态">
							<el-select v-model="testRecordForm.resultStatus" placeholder="请选择结果状态" style="width: 100%">
								<el-option label="正常" value="1"></el-option>
								<el-option label="异常偏高" value="2"></el-option>
								<el-option label="异常偏低" value="3"></el-option>
								<el-option label="临界值" value="4"></el-option>
								<el-option label="严重异常" value="5"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="检查日期">
							<el-date-picker v-model="testRecordForm.testDate" type="date" placeholder="选择检查日期"
								style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
							</el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="检查医生">
							<el-input v-model="testRecordForm.doctor" placeholder="请输入检查医生"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="检查结果">
					<el-input type="textarea" v-model="testRecordForm.result" placeholder="请输入检查结果" :rows="3"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="testRecordDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveTestRecord">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				userList: {},
				gxList: [],
				maritalStatusList: [], // 婚姻状况字典
				insuranceTypeList: [], // 医保类型字典
				riskLevelList: [], // 患者风险等级字典
				page: 1,
				limit: 10,
				page1: 1,
				limit1: 10,
				tableData: {},
				memberName: '',
				memberImg: '',
				memberId: '',
				activeName: 'first',
				medicationActiveTab: 'medicationTab', // 用药检查管理子tab
				phone: '',
				userName: '',
				riskLevel: '',
				tableDataLoading: false,
				tableDataLoadings: false,
				dialogVisible: false,
				dialogVisibles: false,
				title: '添加患者',
				selectedUserInfo: {}, // 存储选中用户的详细信息
				// 新增的医疗档案数据
				healthRecords: [], // 健康记录
				medicalHistories: [], // 病史记录
				allergyRecords: [], // 过敏记录
				medicationRecords: [], // 用药记录
				testRecords: [], // 检查记录

				// 对话框控制
				healthRecordDialogVisible: false,
				medicalHistoryDialogVisible: false,
				allergyRecordDialogVisible: false,
				medicationRecordDialogVisible: false,
				testRecordDialogVisible: false,

				// 对话框标题
				healthRecordDialogTitle: '',
				medicalHistoryDialogTitle: '',
				allergyRecordDialogTitle: '',
				medicationRecordDialogTitle: '',
				testRecordDialogTitle: '',

				// 表单数据
				healthRecordForm: {},
				medicalHistoryForm: {},
				allergyRecordForm: {},
				medicationRecordForm: {},
				testRecordForm: {},
				numberValidateForm: {
					patientId: '',
					realName: '',
					sex: 1,
					birthDate: '',
					isUnderAge: 0,
					idNumber: '',
					ethnicity: '汉族',
					maritalStatus: '1',
					occupation: '',
					currentAddress: '',
					insuranceType: '1',
					insuranceNumber: '',
					primaryContactName: '',
					primaryContactRelation: '',
					primaryContactPhone: '',
					userName: '',
					userId: '',
					phone: '',
				},
				titles: '添加患者',
				phoneUser: '',
				userNameUser: '',
				realNameT:'',
			}
		},
		methods: {

			handleClick(tab) {
				if (tab._props.label == '患者基础信息') {
					this.dataSelect()
				}
			},
			// 获取完整度颜色
			getCompletenessColor(percentage) {
				if (percentage >= 80) return '#67c23a'
				if (percentage >= 60) return '#e6a23c'
				return '#f56c6c'
			},
			// 获取风险等级类型
			getRiskLevelType(level) {
				switch(level) {
					case 1: return 'success'
					case 2: return 'warning'
					case 3: return 'danger'
					default: return 'info'
				}
			},
			// 获取风险等级文本
			getRiskLevelText(level) {
				if (!level) return '未评估'
				const riskItem = this.riskLevelList.find(item => item.code == level)
				return riskItem ? riskItem.value : '未评估'
			},
			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return '-'
				return new Date(dateStr).toLocaleString('zh-CN')
			},
			// 查看医疗档案
			viewMedicalProfile(row) {
				this.$router.push({
					path: '/medicalProfile',
					query: {
						patientId: row.patientId,
						patientName: row.realName
					}
				})
			},
			// 风险评估
			assessRisk(row) {
				this.$confirm(`确定对患者 ${row.realName} 进行风险评估?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$http({
						url: this.$http.adornUrl('admin/patientMedical/assessMedicalRisk'),
						method: 'post',
						params: this.$http.adornParams({
							patientId: row.patientId
						})
					}).then(({data}) => {
						if (data.code == 0) {
							this.$message({
								message: '风险评估完成',
								type: 'success',
								duration: 1500
							})
							this.dataSelect()
						} else {
							this.$message({
								message: data.msg,
								type: 'warning',
								duration: 1500
							})
						}
					})
				}).catch(() => {})
			},
			// 导出数据
			exportData() {
				this.$confirm('确定导出患者数据?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					window.open(this.$http.adornUrl('admin/patientInfo/exportPatientData?' +
						this.$http.adornParams({
							realName: this.realNameT,
							phone: this.phone,
							userName: this.userName,
							riskLevel: this.riskLevel
						}, true)))
				}).catch(() => {})
			},
			//获取用户列表
			getUserList() {
				this.tableDataLoadings = true
				this.$http({
					url: this.$http.adornUrl('user/selectUserList'),
					method: 'get',
					params: this.$http.adornParams({
						'page': this.page1,
						'limit': this.limit1,
						'isPromotion': -1,
						'phone': this.phoneUser,
						'userName': this.userNameUser,
						'isAuthentication': 2,
						'isAgent': -1
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.tableDataLoadings = false
						let returnData = data.data;
						this.userList = returnData;
					}

				})
			},
			selectUser() {
				this.page1 = 1
				this.getUserList()
			},
			cleansUser() {
				this.page1 = 1
				this.phoneUser = ''
				this.userNameUser = ''
				this.getUserList()
			},
			// 用户列表弹框
			userBtn() {
				this.dialogVisibles = true;
				this.getUserList()
			},

			//获取就诊关系
			getgxList() {
				let data = {
					type: '就诊人关系'
				}
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams(data)
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.gxList = data.data
					}

				})
			},
			//获取婚姻状况字典
			getMaritalStatusList() {
				let data = {
					type: '婚姻状况'
				}
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams(data)
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.maritalStatusList = data.data
					}
				})
			},
			//获取医保类型字典
			getInsuranceTypeList() {
				let data = {
					type: '医保类型'
				}
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams(data)
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.insuranceTypeList = data.data
					}
				})
			},
			//获取患者风险等级字典
			getRiskLevelList() {
				let data = {
					type: '患者风险等级'
				}
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams(data)
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.riskLevelList = data.data
					}
				})
			},
			// 获取数据列表
			dataSelect() {
				this.tableDataLoading = true
				this.$http({
					url: this.$http.adornUrl('admin/patientInfo/getPatientList'),
					method: 'get',
					params: this.$http.adornParams({
						'page': this.page,
						'limit': this.limit,
						'realName': this.realNameT,
						'phone': this.phone,
						'userName': this.userName,
						'riskLevel': this.riskLevel
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.tableDataLoading = false
						let returnData = data.data;
						this.tableData = returnData;
					}

				})
			},
			select() {
				this.page = 1
				this.dataSelect()
			},
			cleans() {
				this.page = 1
				this.userName = ''
				this.phone = ''
				this.realNameT = ''
				this.riskLevel = ''
				this.dataSelect()
			},
			handleCurrentChange(val) {
				this.page = val
				this.dataSelect()
			},
			handleSizeChange(val) {
				this.limit = val
				this.dataSelect()
			},
			handleCurrentChanges(val) {
				this.page1 = val
				this.getUserList()
			},
			handleSizeChanges(val) {
				this.limit1 = val
				this.getUserList()
			},
			// 添加、修改患者弹框
			addPatient(row) {
				if (row) {
					this.titles = '修改患者信息'
					this.numberValidateForm.patientId = row.patientId
					this.numberValidateForm.userId = row.userId
					this.numberValidateForm.userName = row.userName
					this.numberValidateForm.realName = row.realName
					this.numberValidateForm.sex = row.sex
					this.numberValidateForm.birthDate = row.birthDate
					this.numberValidateForm.isUnderAge = row.isUnderAge
					this.numberValidateForm.phone = row.phone
					this.numberValidateForm.idNumber = row.idNumber
					this.numberValidateForm.ethnicity = row.ethnicity || '汉族'
					this.numberValidateForm.maritalStatus = row.maritalStatus || '1'
					this.numberValidateForm.occupation = row.occupation || ''
					this.numberValidateForm.currentAddress = row.currentAddress || ''
					this.numberValidateForm.insuranceType = row.insuranceType || '1'
					this.numberValidateForm.insuranceNumber = row.insuranceNumber || ''
					this.numberValidateForm.primaryContactName = row.primaryContactName || ''
					this.numberValidateForm.primaryContactRelation = row.primaryContactRelation || ''
					this.numberValidateForm.primaryContactPhone = row.primaryContactPhone || ''

					// 如果有用户信息，设置selectedUserInfo用于显示
					if (row.userId && row.userName) {
						this.selectedUserInfo = {
							realName: row.realName,
							sex: row.sex,
							phone: row.phone,
							birthDate: row.birthDate,
							idNumber: row.idNumber
						}
					}
				} else {
					this.titles = '添加患者'
					this.numberValidateForm.patientId = ''
					this.numberValidateForm.userId = ''
					this.numberValidateForm.userName = ''
					this.numberValidateForm.realName = ''
					this.numberValidateForm.sex = 1
					this.numberValidateForm.birthDate = ''
					this.numberValidateForm.isUnderAge = 0
					this.numberValidateForm.phone = ''
					this.numberValidateForm.idNumber = ''
					this.numberValidateForm.ethnicity = '汉族'
					this.numberValidateForm.maritalStatus = '1'
					this.numberValidateForm.occupation = ''
					this.numberValidateForm.currentAddress = ''
					this.numberValidateForm.insuranceType = '1'
					this.numberValidateForm.insuranceNumber = ''
					this.numberValidateForm.primaryContactName = ''
					this.numberValidateForm.primaryContactRelation = ''
					this.numberValidateForm.primaryContactPhone = ''

					// 清空用户信息显示
					this.selectedUserInfo = {}
				}
				this.dialogVisible = true
			},
			// 添加/修改患者
			addNoticeTo() {
				if (this.numberValidateForm.userId == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请选择所属用户',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.realName == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入患者姓名',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.phone == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入联系电话',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.idNumber == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入身份证号码',
						type: 'warning'
					});
					return
				}

				this.$http({
					url: this.$http.adornUrl('admin/patientInfo/savePatient'),
					method: 'post',
					data: this.$http.adornData({
						'patientId': this.numberValidateForm.patientId,
						'userId': this.numberValidateForm.userId,
						'realName': this.numberValidateForm.realName,
						'sex': this.numberValidateForm.sex,
						'birthDate': this.numberValidateForm.birthDate,
						'isUnderAge': this.numberValidateForm.isUnderAge,
						'phone': this.numberValidateForm.phone,
						'idNumber': this.numberValidateForm.idNumber,
						'ethnicity': this.numberValidateForm.ethnicity,
						'maritalStatus': this.numberValidateForm.maritalStatus,
						'occupation': this.numberValidateForm.occupation,
						'currentAddress': this.numberValidateForm.currentAddress,
						'insuranceType': this.numberValidateForm.insuranceType,
						'insuranceNumber': this.numberValidateForm.insuranceNumber,
						'primaryContactName': this.numberValidateForm.primaryContactName,
						'primaryContactRelation': this.numberValidateForm.primaryContactRelation,
						'primaryContactPhone': this.numberValidateForm.primaryContactPhone
					})
				}).then(({
					data
				}) => {
					if (data.code == 0) {
						this.dialogVisible = false
						this.$message({
							message: '操作成功',
							type: 'success',
							duration: 1500,
							onClose: () => {
								this.dataSelect()
							}
						})
					} else {
						this.$message({
							message: data.msg,
							type: 'warning',
							duration: 1500,
							onClose: () => {}
						})
					}

				})
			},
			// 选择用户
			amendBanner(row) {
				this.numberValidateForm.userName = row.userName
				this.numberValidateForm.userId = row.userId

				// 保存选中用户的详细信息用于显示
				this.selectedUserInfo = {
					realName: row.realName,
					sex: row.sex,
					phone: row.phone,
					birthDate: row.birthDate,
					idNumber: row.idNumber,
					avatar: row.avatar
				}

				// 自动填充用户的基本信息到表单
				if (row.realName) {
					this.numberValidateForm.realName = row.realName
				}
				if (row.sex) {
					this.numberValidateForm.sex = row.sex
				}
				if (row.phone) {
					this.numberValidateForm.phone = row.phone
				}
				if (row.birthDate) {
					this.numberValidateForm.birthDate = row.birthDate
				}
				if (row.idNumber) {
					this.numberValidateForm.idNumber = row.idNumber
				}

				this.dialogVisibles = false

				// 显示提示信息
				this.$message({
					message: `已选择用户：${row.userName}，相关信息已自动填充`,
					type: 'success',
					duration: 2000
				})
			},
			// 清除选择的用户
			clearSelectedUser() {
				this.$confirm('确定要清除已选择的用户吗？这将清空相关的自动填充信息。', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.numberValidateForm.userId = ''
					this.numberValidateForm.userName = ''
					this.selectedUserInfo = {}

					// 可选：是否清空自动填充的信息
					// this.numberValidateForm.realName = ''
					// this.numberValidateForm.sex = 1
					// this.numberValidateForm.phone = ''
					// this.numberValidateForm.birthDate = ''
					// this.numberValidateForm.idNumber = ''

					this.$message({
						message: '已清除用户选择',
						type: 'success',
						duration: 1500
					})
				}).catch(() => {})
			},
			//删除
			deletes(row) {
				this.$confirm(`确定删除此条信息?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					let data = {
						userId: row.userId,
						patientId: row.patientId
					}
					this.$http({
						url: this.$http.adornUrl('admin/patientInfo/deletePatient'),
						method: 'get',
						params: this.$http.adornParams(data)
					}).then(({
						data
					}) => {
						if (data.code == 0) {
							this.$message({
								message: '删除成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									this.dataSelect()
								}
							})
						} else {
							this.$message({
								message: data.msg,
								type: 'warning',
								duration: 1500,
								onClose: () => {}
							})
						}

					})
				}).catch(() => {})
			},
			// 详情跳转
			updates(userId) {
				if (userId) {
					this.$router.push({
						path: '/userDetail',
						query: {
							userId: userId
						}
					})
				}

			},


			// Tab切换处理
			handleClick(tab, event) {
				console.log('切换到tab:', tab.name)
				// 根据不同的tab加载对应的数据
				switch(tab.name) {
					case 'first':
						this.dataSelect() // 加载患者基础信息
						break
					case 'health':
						this.loadHealthRecords() // 加载健康记录
						break
					case 'history':
						this.loadMedicalHistories() // 加载病史记录
						break
					case 'allergy':
						this.loadAllergyRecords() // 加载过敏记录
						break
					case 'medication':
						this.loadMedicationRecords() // 加载用药记录
						this.loadTestRecords() // 加载检查记录
						break
				}
			},

			// 健康状态管理相关方法
			loadHealthRecords() {
				// 模拟数据，实际应该调用API
				this.healthRecords = [
					{
						patientName: '张三',
						healthLevel: '1',
						checkDate: '2024-01-15',
						checkItems: '常规体检',
						result: '各项指标正常',
						doctor: '李医生'
					}
				]
			},
			addHealthRecord() {
				this.healthRecordForm = {
					patientName: '',
					healthLevel: '1',
					checkDate: '',
					checkItems: '',
					result: '',
					doctor: ''
				}
				this.healthRecordDialogVisible = true
				this.healthRecordDialogTitle = '添加健康记录'
			},
			editHealthRecord(row) {
				this.healthRecordForm = { ...row }
				this.healthRecordDialogVisible = true
				this.healthRecordDialogTitle = '编辑健康记录'
			},
			deleteHealthRecord(row) {
				this.$confirm('确定要删除这条健康记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					// 从数组中删除
					const index = this.healthRecords.findIndex(item => item === row)
					if (index > -1) {
						this.healthRecords.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			saveHealthRecord() {
				if (!this.healthRecordForm.patientName || !this.healthRecordForm.checkDate) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.healthRecordDialogTitle === '添加健康记录') {
					// 添加新记录
					this.healthRecords.push({ ...this.healthRecordForm })
					this.$message.success('添加成功')
				} else {
					// 编辑记录
					const index = this.healthRecords.findIndex(item =>
						item.patientName === this.healthRecordForm.patientName &&
						item.checkDate === this.healthRecordForm.checkDate
					)
					if (index > -1) {
						this.healthRecords.splice(index, 1, { ...this.healthRecordForm })
						this.$message.success('修改成功')
					}
				}

				this.healthRecordDialogVisible = false
			},
			exportHealthData() {
				this.$message.info('导出健康数据功能开发中...')
			},
			getHealthLevelText(level) {
				const levels = {'1': '优秀', '2': '良好', '3': '一般', '4': '较差', '5': '差'}
				return levels[level] || '未评估'
			},
			getHealthLevelType(level) {
				const types = {'1': 'success', '2': 'success', '3': 'warning', '4': 'danger', '5': 'danger'}
				return types[level] || 'info'
			},

			// 病史记录管理相关方法
			loadMedicalHistories() {
				// 模拟数据，实际应该调用API
				this.medicalHistories = [
					{
						patientName: '张三',
						historyType: '1',
						diagnosis: '高血压',
						description: '患者有高血压病史3年',
						recordDate: '2024-01-10',
						doctor: '王医生'
					}
				]
			},
			addMedicalHistory() {
				this.medicalHistoryForm = {
					patientName: '',
					historyType: '1',
					diagnosis: '',
					description: '',
					recordDate: '',
					doctor: ''
				}
				this.medicalHistoryDialogVisible = true
				this.medicalHistoryDialogTitle = '添加病史记录'
			},
			editMedicalHistory(row) {
				this.medicalHistoryForm = { ...row }
				this.medicalHistoryDialogVisible = true
				this.medicalHistoryDialogTitle = '编辑病史记录'
			},
			deleteMedicalHistory(row) {
				this.$confirm('确定要删除这条病史记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const index = this.medicalHistories.findIndex(item => item === row)
					if (index > -1) {
						this.medicalHistories.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			saveMedicalHistory() {
				if (!this.medicalHistoryForm.patientName || !this.medicalHistoryForm.diagnosis) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.medicalHistoryDialogTitle === '添加病史记录') {
					this.medicalHistories.push({ ...this.medicalHistoryForm })
					this.$message.success('添加成功')
				} else {
					const index = this.medicalHistories.findIndex(item =>
						item.patientName === this.medicalHistoryForm.patientName &&
						item.diagnosis === this.medicalHistoryForm.diagnosis
					)
					if (index > -1) {
						this.medicalHistories.splice(index, 1, { ...this.medicalHistoryForm })
						this.$message.success('修改成功')
					}
				}

				this.medicalHistoryDialogVisible = false
			},
			exportHistoryData() {
				this.$message.info('导出病史数据功能开发中...')
			},
			getHistoryTypeText(type) {
				const types = {'1': '既往史', '2': '现病史', '3': '家族史', '4': '过敏史', '5': '手术史'}
				return types[type] || '未知'
			},
			getHistoryTypeColor(type) {
				const colors = {'1': 'primary', '2': 'warning', '3': 'info', '4': 'danger', '5': 'success'}
				return colors[type] || 'info'
			},

			// 过敏信息管理相关方法
			loadAllergyRecords() {
				// 模拟数据，实际应该调用API
				this.allergyRecords = [
					{
						patientName: '张三',
						allergyType: '1',
						allergen: '青霉素',
						severity: '3',
						symptoms: '皮疹、呼吸困难',
						recordDate: '2024-01-05'
					}
				]
			},
			addAllergyRecord() {
				this.allergyRecordForm = {
					patientName: '',
					allergyType: '1',
					allergen: '',
					severity: '1',
					symptoms: '',
					recordDate: ''
				}
				this.allergyRecordDialogVisible = true
				this.allergyRecordDialogTitle = '添加过敏记录'
			},
			editAllergyRecord(row) {
				this.allergyRecordForm = { ...row }
				this.allergyRecordDialogVisible = true
				this.allergyRecordDialogTitle = '编辑过敏记录'
			},
			deleteAllergyRecord(row) {
				this.$confirm('确定要删除这条过敏记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const index = this.allergyRecords.findIndex(item => item === row)
					if (index > -1) {
						this.allergyRecords.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			saveAllergyRecord() {
				if (!this.allergyRecordForm.patientName || !this.allergyRecordForm.allergen) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.allergyRecordDialogTitle === '添加过敏记录') {
					this.allergyRecords.push({ ...this.allergyRecordForm })
					this.$message.success('添加成功')
				} else {
					const index = this.allergyRecords.findIndex(item =>
						item.patientName === this.allergyRecordForm.patientName &&
						item.allergen === this.allergyRecordForm.allergen
					)
					if (index > -1) {
						this.allergyRecords.splice(index, 1, { ...this.allergyRecordForm })
						this.$message.success('修改成功')
					}
				}

				this.allergyRecordDialogVisible = false
			},
			exportAllergyData() {
				this.$message.info('导出过敏数据功能开发中...')
			},
			getAllergyTypeText(type) {
				const types = {'1': '药物过敏', '2': '食物过敏', '3': '环境过敏', '4': '接触过敏', '5': '其他过敏'}
				return types[type] || '未知'
			},
			getAllergyTypeColor(type) {
				const colors = {'1': 'danger', '2': 'warning', '3': 'info', '4': 'primary', '5': 'success'}
				return colors[type] || 'info'
			},
			getSeverityText(severity) {
				const severities = {'1': '轻微', '2': '中度', '3': '严重', '4': '危及生命'}
				return severities[severity] || '未知'
			},
			getSeverityColor(severity) {
				const colors = {'1': 'success', '2': 'warning', '3': 'danger', '4': 'danger'}
				return colors[severity] || 'info'
			},

			// 用药检查管理相关方法
			loadMedicationRecords() {
				// 模拟数据，实际应该调用API
				this.medicationRecords = [
					{
						patientName: '张三',
						medicationName: '阿司匹林',
						medicationType: '处方药',
						dosage: '100mg',
						frequency: '每日一次',
						status: '1',
						startDate: '2024-01-01'
					}
				]
			},
			loadTestRecords() {
				// 模拟数据，实际应该调用API
				this.testRecords = [
					{
						patientName: '张三',
						testType: '血液检查',
						testName: '血常规',
						result: '正常',
						resultStatus: '1',
						testDate: '2024-01-15',
						doctor: '李医生'
					}
				]
			},
			addMedicationRecord() {
				this.medicationRecordForm = {
					patientName: '',
					medicationName: '',
					medicationType: '处方药',
					dosage: '',
					frequency: '',
					status: '1',
					startDate: ''
				}
				this.medicationRecordDialogVisible = true
				this.medicationRecordDialogTitle = '添加用药记录'
			},
			addTestRecord() {
				this.testRecordForm = {
					patientName: '',
					testType: '血液检查',
					testName: '',
					result: '',
					resultStatus: '1',
					testDate: '',
					doctor: ''
				}
				this.testRecordDialogVisible = true
				this.testRecordDialogTitle = '添加检查记录'
			},
			editMedicationRecord(row) {
				this.medicationRecordForm = { ...row }
				this.medicationRecordDialogVisible = true
				this.medicationRecordDialogTitle = '编辑用药记录'
			},
			deleteMedicationRecord(row) {
				this.$confirm('确定要删除这条用药记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const index = this.medicationRecords.findIndex(item => item === row)
					if (index > -1) {
						this.medicationRecords.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			editTestRecord(row) {
				this.testRecordForm = { ...row }
				this.testRecordDialogVisible = true
				this.testRecordDialogTitle = '编辑检查记录'
			},
			deleteTestRecord(row) {
				this.$confirm('确定要删除这条检查记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const index = this.testRecords.findIndex(item => item === row)
					if (index > -1) {
						this.testRecords.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			saveMedicationRecord() {
				if (!this.medicationRecordForm.patientName || !this.medicationRecordForm.medicationName) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.medicationRecordDialogTitle === '添加用药记录') {
					this.medicationRecords.push({ ...this.medicationRecordForm })
					this.$message.success('添加成功')
				} else {
					const index = this.medicationRecords.findIndex(item =>
						item.patientName === this.medicationRecordForm.patientName &&
						item.medicationName === this.medicationRecordForm.medicationName
					)
					if (index > -1) {
						this.medicationRecords.splice(index, 1, { ...this.medicationRecordForm })
						this.$message.success('修改成功')
					}
				}

				this.medicationRecordDialogVisible = false
			},
			saveTestRecord() {
				if (!this.testRecordForm.patientName || !this.testRecordForm.testName) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.testRecordDialogTitle === '添加检查记录') {
					this.testRecords.push({ ...this.testRecordForm })
					this.$message.success('添加成功')
				} else {
					const index = this.testRecords.findIndex(item =>
						item.patientName === this.testRecordForm.patientName &&
						item.testName === this.testRecordForm.testName
					)
					if (index > -1) {
						this.testRecords.splice(index, 1, { ...this.testRecordForm })
						this.$message.success('修改成功')
					}
				}

				this.testRecordDialogVisible = false
			},
			exportMedicationData() {
				this.$message.info('导出用药检查数据功能开发中...')
			},
			getMedicationStatusText(status) {
				const statuses = {'1': '正在使用', '2': '已停用', '3': '暂停使用'}
				return statuses[status] || '未知'
			},
			getMedicationStatusColor(status) {
				const colors = {'1': 'success', '2': 'info', '3': 'warning'}
				return colors[status] || 'info'
			},
			getTestResultText(status) {
				const statuses = {'1': '正常', '2': '异常偏高', '3': '异常偏低', '4': '临界值', '5': '严重异常'}
				return statuses[status] || '未知'
			},
			getTestResultColor(status) {
				const colors = {'1': 'success', '2': 'danger', '3': 'danger', '4': 'warning', '5': 'danger'}
				return colors[status] || 'info'
			}
		},
		mounted() {
			this.dataSelect()
			this.getgxList();
			this.getMaritalStatusList();
			this.getInsuranceTypeList();
			this.getRiskLevelList();
		}
	}
</script>

<style>
</style>
