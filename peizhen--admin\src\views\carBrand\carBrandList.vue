<template>
	<div class="enhanced-patient-management">
		<el-tabs v-model="activeName" @tab-click="handleTabClick" type="border-card">
			<!-- 患者基础信息标签页 -->
			<el-tab-pane label="患者基础信息" name="basic">
				<patient-basic-info
					:risk-level-list="riskLevelList"
					@add-patient="handleAddPatient"
					@edit-patient="handleEditPatient"
					@delete-patient="handleDeletePatient"
					@view-profile="handleViewMedicalProfile"
					@view-user="handleViewUser"
					@assess-risk="handleAssessRisk"
					@export-data="handleExportData"
					ref="patientBasicInfo">
				</patient-basic-info>
			</el-tab-pane>

			<!-- 健康状态管理标签页 -->
			<el-tab-pane label="健康状态管理" name="health">
				<health-status-management
					:health-level-options="healthLevelOptions"
					@add-health-status="handleAddHealthStatus"
					@edit-health-status="handleEditHealthStatus"
					@delete-health-status="handleDeleteHealthStatus"
					@view-health-detail="handleViewHealthDetail"
					@view-patient="handleViewPatientFromHealth"
					@export-health-data="handleExportHealthData"
					ref="healthStatusManagement">
				</health-status-management>
			</el-tab-pane>

			<!-- 病史记录管理标签页 -->
			<el-tab-pane label="病史记录管理" name="history">
				<medical-history-management
					:history-type-options="historyTypeOptions"
					@add-medical-history="handleAddMedicalHistory"
					@edit-medical-history="handleEditMedicalHistory"
					@delete-medical-history="handleDeleteMedicalHistory"
					@view-history-detail="handleViewHistoryDetail"
					@view-patient="handleViewPatientFromHistory"
					@export-history-data="handleExportHistoryData"
					ref="medicalHistoryManagement">
				</medical-history-management>
			</el-tab-pane>

			<!-- 过敏信息管理标签页 -->
			<el-tab-pane label="过敏信息管理" name="allergy">
				<allergy-management
					:allergy-type-options="allergyTypeOptions"
					:severity-options="allergySeverityOptions"
					@add-allergy="handleAddAllergy"
					@edit-allergy="handleEditAllergy"
					@delete-allergy="handleDeleteAllergy"
					@view-allergy-detail="handleViewAllergyDetail"
					@view-patient="handleViewPatientFromAllergy"
					@export-allergy-data="handleExportAllergyData"
					ref="allergyManagement">
				</allergy-management>
			</el-tab-pane>

			<!-- 用药检查管理标签页 -->
			<el-tab-pane label="用药检查管理" name="medication">
				<medication-management
					:medication-type-options="medicationTypeOptions"
					:medication-status-options="medicationStatusOptions"
					@add-medication="handleAddMedication"
					@edit-medication="handleEditMedication"
					@delete-medication="handleDeleteMedication"
					@view-medication-detail="handleViewMedicationDetail"
					@edit-test-result="handleEditTestResult"
					@delete-test-result="handleDeleteTestResult"
					@view-test-detail="handleViewTestDetail"
					@view-patient="handleViewPatientFromMedication"
					@export-medication-data="handleExportMedicationData"
					ref="medicationManagement">
				</medication-management>
			</el-tab-pane>
		</el-tabs>

		<!-- 患者信息编辑对话框 -->
		<el-dialog :title="titles" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">
			<!-- 简化的患者表单，暂时使用基本表单 -->
			<el-form :model="numberValidateForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名">
							<el-input v-model="numberValidateForm.realName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="性别">
							<el-radio-group v-model="numberValidateForm.sex">
								<el-radio :label="1">男</el-radio>
								<el-radio :label="0">女</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="出生日期">
							<el-date-picker v-model="numberValidateForm.birthDate" type="date" placeholder="选择日期"></el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系电话">
							<el-input v-model="numberValidateForm.phone" placeholder="请输入联系电话"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="身份证号">
							<el-input v-model="numberValidateForm.idNumber" placeholder="请输入身份证号"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="民族">
							<el-input v-model="numberValidateForm.ethnicity" placeholder="请输入民族"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSavePatient">确定</el-button>
			</div>
		</el-dialog>

		<!-- 用户选择对话框 -->
		<el-dialog title="选择用户" :visible.sync="dialogVisibles" width="70%" :close-on-click-modal="false">
			<!-- 简化的用户选择表格 -->
			<div style="margin-bottom: 20px;">
				<el-input v-model="phoneUser" placeholder="用户手机号" style="width: 200px; margin-right: 10px;"></el-input>
				<el-input v-model="userNameUser" placeholder="用户昵称" style="width: 200px; margin-right: 10px;"></el-input>
				<el-button type="primary" @click="selectUser">搜索</el-button>
				<el-button @click="cleansUser">重置</el-button>
			</div>
			<el-table :data="userList.records" v-loading="tableDataLoadings" border>
				<el-table-column prop="userName" label="用户昵称"></el-table-column>
				<el-table-column prop="phone" label="手机号"></el-table-column>
				<el-table-column prop="realName" label="真实姓名"></el-table-column>
				<el-table-column label="操作" width="100">
					<template slot-scope="scope">
						<el-button size="mini" type="primary" @click="handleSelectUser(scope.row)">选择</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="text-align: center; margin-top: 20px;">
				<el-pagination
					@size-change="handleSizeChanges"
					@current-change="handleCurrentChanges"
					:page-sizes="[10, 20, 30, 40]"
					:page-size="limit1"
					:current-page="page1"
					layout="total,sizes, prev, pager, next,jumper"
					:total="userList.total">
				</el-pagination>
			</div>
		</el-dialog>
	</div>

		<!--添加/编辑患者-->
		<el-dialog :title="titles" :visible.sync="dialogVisible" width="70%" center>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">所属用户：</span>
				<el-input v-model="numberValidateForm.userName" @focus="userBtn()" style="width:45%;"
					placeholder="请点击选择所属用户" readonly>
					<el-button slot="append" icon="el-icon-search" @click="userBtn()">选择用户</el-button>
				</el-input>
				<el-button v-if="numberValidateForm.userId" @click="clearSelectedUser()"
					style="margin-left: 10px;" size="small" type="warning" icon="el-icon-delete">
					清除选择
				</el-button>
			</div>

			<!-- 用户信息显示区域 -->
			<div v-if="numberValidateForm.userId" style="margin-bottom: 15px; padding: 15px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 8px; border-left: 4px solid #409EFF;">
				<div style="color: #303133; font-size: 14px; margin-bottom: 10px; display: flex; align-items: center;">
					<i class="el-icon-user" style="color: #409EFF; margin-right: 8px; font-size: 16px;"></i>
					<strong>已选择用户信息</strong>
					<el-tag size="mini" type="success" style="margin-left: 10px;">已绑定</el-tag>
				</div>
				<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; color: #606266;">
					<div style="display: flex; align-items: center;">
						<i class="el-icon-postcard" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>用户ID：</strong>{{ numberValidateForm.userId }}</span>
					</div>
					<div style="display: flex; align-items: center;">
						<i class="el-icon-user-solid" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>昵称：</strong>{{ numberValidateForm.userName || '未设置' }}</span>
					</div>
					<div v-if="selectedUserInfo.realName" style="display: flex; align-items: center;">
						<i class="el-icon-s-custom" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>真实姓名：</strong>{{ selectedUserInfo.realName }}</span>
					</div>
					<div v-if="selectedUserInfo.sex" style="display: flex; align-items: center;">
						<i class="el-icon-male" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>性别：</strong>{{ selectedUserInfo.sex == 1 ? '男' : '女' }}</span>
					</div>
					<div v-if="selectedUserInfo.phone" style="display: flex; align-items: center;">
						<i class="el-icon-phone" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>手机号：</strong>{{ selectedUserInfo.phone }}</span>
					</div>
					<div v-if="selectedUserInfo.birthDate" style="display: flex; align-items: center;">
						<i class="el-icon-date" style="color: #909399; margin-right: 5px;"></i>
						<span><strong>出生日期：</strong>{{ selectedUserInfo.birthDate }}</span>
					</div>
				</div>
			</div>
			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">患者姓名：</span>
						<el-input v-model="numberValidateForm.realName" style="width:70%;" placeholder="请输入患者姓名">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">性别：</span>
						<el-radio-group v-model="numberValidateForm.sex">
							<el-radio :label="1">男</el-radio>
							<el-radio :label="2">女</el-radio>
						</el-radio-group>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">出生日期：</span>
						<el-date-picker v-model="numberValidateForm.birthDate" type="date"
							placeholder="选择出生日期" style="width:70%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
						</el-date-picker>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">是否成年：</span>
						<el-radio-group v-model="numberValidateForm.isUnderAge">
							<el-radio :label="0">是</el-radio>
							<el-radio :label="1">否</el-radio>
						</el-radio-group>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">身份证号：</span>
						<el-input v-model="numberValidateForm.idNumber" style="width:70%;" placeholder="请输入身份证号码">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">联系电话：</span>
						<el-input v-model="numberValidateForm.phone" style="width:70%;" placeholder="请输入联系电话">
						</el-input>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">民族：</span>
						<el-input v-model="numberValidateForm.ethnicity" style="width:70%;" placeholder="请输入民族">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">婚姻状况：</span>
						<el-select style="width: 70%" v-model="numberValidateForm.maritalStatus" placeholder="请选择婚姻状况">
							<el-option v-for="(item,index) in maritalStatusList" :key="index" :label="item.value" :value="item.code">
							</el-option>
						</el-select>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">职业：</span>
						<el-input v-model="numberValidateForm.occupation" style="width:70%;" placeholder="请输入职业">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">医保类型：</span>
						<el-select style="width: 70%" v-model="numberValidateForm.insuranceType" placeholder="请选择医保类型">
							<el-option v-for="(item,index) in insuranceTypeList" :key="index" :label="item.value" :value="item.code">
							</el-option>
						</el-select>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="24">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">现住址：</span>
						<el-input v-model="numberValidateForm.currentAddress" style="width:80%;" placeholder="请输入现住址">
						</el-input>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">紧急联系人：</span>
						<el-input v-model="numberValidateForm.primaryContactName" style="width:70%;" placeholder="请输入紧急联系人姓名">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">联系人关系：</span>
						<el-select style="width: 70%" v-model="numberValidateForm.primaryContactRelation" placeholder="请选择关系">
							<el-option v-for="(item,index) in gxList" :key="index" :label="item.value" :value="item.value">
							</el-option>
						</el-select>
					</div>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">联系人电话：</span>
						<el-input v-model="numberValidateForm.primaryContactPhone" style="width:70%;" placeholder="请输入联系人电话">
						</el-input>
					</div>
				</el-col>
				<el-col :span="12">
					<div style="margin-bottom: 15px;">
						<span style="width: 120px;display: inline-block;text-align: right;">医保号：</span>
						<el-input v-model="numberValidateForm.insuranceNumber" style="width:70%;" placeholder="请输入医保号">
						</el-input>
					</div>
				</el-col>
			</el-row>
			<span slot="footer" class="dialog-footer" style="margin-top: 30px;text-align: center;">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="addNoticeTo()">确 定</el-button>
			</span>
		</el-dialog>
		<!--选择用户-->
		<el-dialog title="选择用户" :visible.sync="dialogVisibles" width="70%" center>
			<div style="margin:2% 0;display: inline-block;">
				<span>手机号:</span>
				<el-input style="width: 150px;" @keydown.enter.native="selectUser" clearable placeholder="请输入手机号"
					v-model="phoneUser"></el-input>
			</div>&emsp;&emsp;
			<div style="margin:2% 0;display: inline-block;">
				<span>昵称:</span>
				<el-input style="width: 150px;" @keydown.enter.native="selectUser" clearable placeholder="请输入昵称"
					v-model="userNameUser"></el-input>
			</div>&emsp;&emsp;
			<div style="display: inline-block;">
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="selectUser">查询
				</el-button>
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="cleansUser">重置
				</el-button>
			</div>
			<el-table v-loading="tableDataLoadings" :data="userList.list">
				<el-table-column fixed prop="userId" label="编号" width="80">
				</el-table-column>
				<el-table-column fixed prop="userName" label="昵称" width="120">
					<template slot-scope="scope">
						<span style="color: #f56c6c;">{{ scope.row.userName ? scope.row.userName : '未绑定' }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="realName" label="真实姓名" width="100">
					<template slot-scope="scope">
						<span style="color: #409EFF;">{{ scope.row.realName || '未设置' }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="sex" label="性别" width="60">
					<template slot-scope="scope">
						<span v-if="scope.row.sex==1">男</span>
						<span v-else-if="scope.row.sex==2">女</span>
						<span v-else>未设置</span>
					</template>
				</el-table-column>
				<el-table-column prop="birthDate" label="出生日期" width="100">
					<template slot-scope="scope">
						<span>{{ scope.row.birthDate || '未设置' }}</span>
					</template>
				</el-table-column>
				<el-table-column label="头像" >
					<template slot-scope="scope">
						<img v-if="scope.row.avatar==null" src="~@/assets/img/avatar.png" alt="" width="40" height="40">
						<img v-else :src="scope.row.avatar" alt="" width="40" height="40">
					</template>
				</el-table-column>
				<el-table-column prop="phone" label="手机号" >
					<template slot-scope="scope">
						<el-button size="mini" style="color: #008000;background: #fff;border: none;padding: 0;" type="primary"
							:disabled="!isAuth('userList:details')" @click="updates(scope.row.userId)">
							{{scope.row.phone ? scope.row.phone : '未绑定'}}
						</el-button>
					</template>
				</el-table-column>
				<el-table-column prop="idNumber" label="身份证号">
					<template slot-scope="scope">
						<span>{{ scope.row.idNumber ? scope.row.idNumber.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') : '未设置' }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作"  fixed="right">
					<template slot-scope="scope">
						<el-button size="mini" type="primary" :disabled="!isAuth('carBrandList:update')"
							@click="amendBanner(scope.row)" style="margin: 5px;">选择
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="text-align: center;margin-top: 10px;">
				<el-pagination @size-change="handleSizeChanges" @current-change="handleCurrentChanges"
					:page-sizes="[10, 20, 30, 40]" :page-size="limit1" :current-page="page1"
					layout="total,sizes, prev, pager, next,jumper" :total="userList.totalCount">
				</el-pagination>
			</div>
		</el-dialog>

		<!-- 健康记录对话框 -->
		<el-dialog :title="healthRecordDialogTitle" :visible.sync="healthRecordDialogVisible" width="60%">
			<el-form :model="healthRecordForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="healthRecordForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="健康等级" required>
							<el-select v-model="healthRecordForm.healthLevel" placeholder="请选择健康等级" style="width: 100%">
								<el-option label="优秀" value="1"></el-option>
								<el-option label="良好" value="2"></el-option>
								<el-option label="一般" value="3"></el-option>
								<el-option label="较差" value="4"></el-option>
								<el-option label="差" value="5"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="检查日期" required>
							<el-date-picker v-model="healthRecordForm.checkDate" type="date" placeholder="选择检查日期"
								style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
							</el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="检查医生">
							<el-input v-model="healthRecordForm.doctor" placeholder="请输入检查医生"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="检查项目">
					<el-input v-model="healthRecordForm.checkItems" placeholder="请输入检查项目"></el-input>
				</el-form-item>
				<el-form-item label="检查结果">
					<el-input type="textarea" v-model="healthRecordForm.result" placeholder="请输入检查结果" :rows="3"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="healthRecordDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveHealthRecord">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 病史记录对话框 -->
		<el-dialog :title="medicalHistoryDialogTitle" :visible.sync="medicalHistoryDialogVisible" width="60%">
			<el-form :model="medicalHistoryForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="medicalHistoryForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="病史类型" required>
							<el-select v-model="medicalHistoryForm.historyType" placeholder="请选择病史类型" style="width: 100%">
								<el-option label="既往史" value="1"></el-option>
								<el-option label="现病史" value="2"></el-option>
								<el-option label="家族史" value="3"></el-option>
								<el-option label="过敏史" value="4"></el-option>
								<el-option label="手术史" value="5"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="诊断" required>
							<el-input v-model="medicalHistoryForm.diagnosis" placeholder="请输入诊断"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="记录日期">
							<el-date-picker v-model="medicalHistoryForm.recordDate" type="date" placeholder="选择记录日期"
								style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
							</el-date-picker>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="记录医生">
					<el-input v-model="medicalHistoryForm.doctor" placeholder="请输入记录医生"></el-input>
				</el-form-item>
				<el-form-item label="病史描述">
					<el-input type="textarea" v-model="medicalHistoryForm.description" placeholder="请输入病史描述" :rows="4"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="medicalHistoryDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveMedicalHistory">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 过敏记录对话框 -->
		<el-dialog :title="allergyRecordDialogTitle" :visible.sync="allergyRecordDialogVisible" width="60%">
			<el-form :model="allergyRecordForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="allergyRecordForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="过敏类型" required>
							<el-select v-model="allergyRecordForm.allergyType" placeholder="请选择过敏类型" style="width: 100%">
								<el-option label="药物过敏" value="1"></el-option>
								<el-option label="食物过敏" value="2"></el-option>
								<el-option label="环境过敏" value="3"></el-option>
								<el-option label="接触过敏" value="4"></el-option>
								<el-option label="其他过敏" value="5"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="过敏原" required>
							<el-input v-model="allergyRecordForm.allergen" placeholder="请输入过敏原"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="严重程度" required>
							<el-select v-model="allergyRecordForm.severity" placeholder="请选择严重程度" style="width: 100%">
								<el-option label="轻微" value="1"></el-option>
								<el-option label="中度" value="2"></el-option>
								<el-option label="严重" value="3"></el-option>
								<el-option label="危及生命" value="4"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="记录日期">
					<el-date-picker v-model="allergyRecordForm.recordDate" type="date" placeholder="选择记录日期"
						style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
					</el-date-picker>
				</el-form-item>
				<el-form-item label="过敏症状">
					<el-input type="textarea" v-model="allergyRecordForm.symptoms" placeholder="请输入过敏症状" :rows="3"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="allergyRecordDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveAllergyRecord">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 用药记录对话框 -->
		<el-dialog :title="medicationRecordDialogTitle" :visible.sync="medicationRecordDialogVisible" width="60%">
			<el-form :model="medicationRecordForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="medicationRecordForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="药物名称" required>
							<el-input v-model="medicationRecordForm.medicationName" placeholder="请输入药物名称"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="药物类型">
							<el-select v-model="medicationRecordForm.medicationType" placeholder="请选择药物类型" style="width: 100%">
								<el-option label="处方药" value="处方药"></el-option>
								<el-option label="非处方药" value="非处方药"></el-option>
								<el-option label="中药" value="中药"></el-option>
								<el-option label="保健品" value="保健品"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="用药状态">
							<el-select v-model="medicationRecordForm.status" placeholder="请选择用药状态" style="width: 100%">
								<el-option label="正在使用" value="1"></el-option>
								<el-option label="已停用" value="2"></el-option>
								<el-option label="暂停使用" value="3"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="用药剂量">
							<el-input v-model="medicationRecordForm.dosage" placeholder="请输入用药剂量"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="用药频次">
							<el-input v-model="medicationRecordForm.frequency" placeholder="请输入用药频次"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="开始日期">
					<el-date-picker v-model="medicationRecordForm.startDate" type="date" placeholder="选择开始日期"
						style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
					</el-date-picker>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="medicationRecordDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveMedicationRecord">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 检查记录对话框 -->
		<el-dialog :title="testRecordDialogTitle" :visible.sync="testRecordDialogVisible" width="60%">
			<el-form :model="testRecordForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="患者姓名" required>
							<el-input v-model="testRecordForm.patientName" placeholder="请输入患者姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="检查类型">
							<el-select v-model="testRecordForm.testType" placeholder="请选择检查类型" style="width: 100%">
								<el-option label="血液检查" value="血液检查"></el-option>
								<el-option label="尿液检查" value="尿液检查"></el-option>
								<el-option label="影像检查" value="影像检查"></el-option>
								<el-option label="心电图" value="心电图"></el-option>
								<el-option label="超声检查" value="超声检查"></el-option>
								<el-option label="其他检查" value="其他检查"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="检查项目" required>
							<el-input v-model="testRecordForm.testName" placeholder="请输入检查项目"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="结果状态">
							<el-select v-model="testRecordForm.resultStatus" placeholder="请选择结果状态" style="width: 100%">
								<el-option label="正常" value="1"></el-option>
								<el-option label="异常偏高" value="2"></el-option>
								<el-option label="异常偏低" value="3"></el-option>
								<el-option label="临界值" value="4"></el-option>
								<el-option label="严重异常" value="5"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="检查日期">
							<el-date-picker v-model="testRecordForm.testDate" type="date" placeholder="选择检查日期"
								style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
							</el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="检查医生">
							<el-input v-model="testRecordForm.doctor" placeholder="请输入检查医生"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="检查结果">
					<el-input type="textarea" v-model="testRecordForm.result" placeholder="请输入检查结果" :rows="3"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="testRecordDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveTestRecord">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
// 导入组件
import PatientBasicInfo from '@/components/medical/PatientBasicInfo.vue'
import HealthStatusManagement from '@/components/medical/HealthStatusManagement.vue'
import MedicalHistoryManagement from '@/components/medical/MedicalHistoryManagement.vue'
import AllergyManagement from '@/components/medical/AllergyManagement.vue'
import MedicationManagement from '@/components/medical/MedicationManagement.vue'

export default {
	name: 'EnhancedPatientManagement',
	components: {
		PatientBasicInfo,
		HealthStatusManagement,
		MedicalHistoryManagement,
		AllergyManagement,
		MedicationManagement
	},
	data() {
		return {
			// 当前活跃的标签页
			activeName: 'basic',

			// 数据字典
			riskLevelList: [],
			healthLevelOptions: [],
			historyTypeOptions: [],
			allergyTypeOptions: [],
			allergySeverityOptions: [],
			medicationTypeOptions: [],
			medicationStatusOptions: [],
			maritalStatusList: [],
			insuranceTypeList: [],
			gxList: [],

			// 用户相关数据
			userList: {},
			selectedUserInfo: {},

			// 对话框控制
			dialogVisible: false,
			dialogVisibles: false,
			titles: '添加患者',

			// 表单数据
			numberValidateForm: {
				patientId: '',
				realName: '',
				sex: 1,
				birthDate: '',
				isUnderAge: 0,
				idNumber: '',
				ethnicity: '汉族',
				maritalStatus: '1',
				occupation: '',
				currentAddress: '',
				insuranceType: '1',
				insuranceNumber: '',
				primaryContactName: '',
				primaryContactRelation: '',
				primaryContactPhone: '',
				userName: '',
				userId: '',
				phone: '',
			},

			// 用户搜索
			phoneUser: '',
			userNameUser: '',
			page1: 1,
			limit1: 10,
			tableDataLoadings: false
		}
	},
	methods: {
		// ==================== 标签页切换处理 ====================
		handleTabClick(tab) {
			console.log('切换到标签页:', tab.name)
			// 组件会自动处理数据加载，这里可以添加额外的逻辑
			this.$nextTick(() => {
				// 确保组件已经渲染完成后再执行相关操作
				switch(tab.name) {
					case 'basic':
						// 患者基础信息标签页
						break
					case 'health':
						// 健康状态管理标签页
						break
					case 'history':
						// 病史记录管理标签页
						break
					case 'allergy':
						// 过敏信息管理标签页
						break
					case 'medication':
						// 用药检查管理标签页
						break
				}
			})
		},

		// ==================== 患者基础信息事件处理 ====================
		handleAddPatient() {
			this.titles = '添加患者'
			this.resetPatientForm()
			this.dialogVisible = true
		},

		handleEditPatient(row) {
			this.titles = '修改患者信息'
			this.fillPatientForm(row)
			this.dialogVisible = true
		},

		handleDeletePatient(row) {
			this.$confirm(`确定删除患者 ${row.realName} 的信息?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.deletePatient(row.patientId)
			}).catch(() => {})
		},

		handleViewMedicalProfile(row) {
			this.$router.push({
				path: '/medicalProfile',
				query: {
					patientId: row.patientId,
					patientName: row.realName
				}
			})
		},

		handleViewUser(userId) {
			if (userId) {
				this.$router.push({
					path: '/userDetail',
					query: { userId: userId }
				})
			}
		},

		handleAssessRisk(row) {
			this.$confirm(`确定对患者 ${row.realName} 进行风险评估?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.assessPatientRisk(row.patientId)
			}).catch(() => {})
		},

		handleExportData(searchForm) {
			this.$confirm('确定导出患者数据?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.exportPatientData(searchForm)
			}).catch(() => {})
		},

		// ==================== 健康状态管理事件处理 ====================
		handleAddHealthStatus() {
			this.$message.info('添加健康状态功能开发中...')
		},

		handleEditHealthStatus(row) {
			this.$message.info('编辑健康状态功能开发中...')
		},

		handleDeleteHealthStatus(row) {
			this.$confirm(`确定删除该健康状态记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('删除成功')
			}).catch(() => {})
		},

		handleViewHealthDetail(row) {
			this.$message.info('查看健康状态详情功能开发中...')
		},

		handleViewPatientFromHealth(row) {
			this.handleViewMedicalProfile(row)
		},

		handleExportHealthData(searchForm) {
			this.$confirm('确定导出健康状态数据?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('导出成功')
			}).catch(() => {})
		},

		// ==================== 病史记录管理事件处理 ====================
		handleAddMedicalHistory() {
			this.$message.info('添加病史记录功能开发中...')
		},

		handleEditMedicalHistory(row) {
			this.$message.info('编辑病史记录功能开发中...')
		},

		handleDeleteMedicalHistory(row) {
			this.$confirm(`确定删除该病史记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('删除成功')
			}).catch(() => {})
		},

		handleViewHistoryDetail(row) {
			this.$message.info('查看病史详情功能开发中...')
		},

		handleViewPatientFromHistory(row) {
			this.handleViewMedicalProfile(row)
		},

		handleExportHistoryData(searchForm) {
			this.$confirm('确定导出病史数据?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('导出成功')
			}).catch(() => {})
		},

		// ==================== 过敏信息管理事件处理 ====================
		handleAddAllergy() {
			this.$message.info('添加过敏记录功能开发中...')
		},

		handleEditAllergy(row) {
			this.$message.info('编辑过敏记录功能开发中...')
		},

		handleDeleteAllergy(row) {
			this.$confirm(`确定删除该过敏记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('删除成功')
			}).catch(() => {})
		},

		handleViewAllergyDetail(row) {
			this.$message.info('查看过敏详情功能开发中...')
		},

		handleViewPatientFromAllergy(row) {
			this.handleViewMedicalProfile(row)
		},

		handleExportAllergyData(searchForm) {
			this.$confirm('确定导出过敏数据?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('导出成功')
			}).catch(() => {})
		},

		// ==================== 用药检查管理事件处理 ====================
		handleAddMedication() {
			this.$message.info('添加用药记录功能开发中...')
		},

		handleEditMedication(row) {
			this.$message.info('编辑用药记录功能开发中...')
		},

		handleDeleteMedication(row) {
			this.$confirm(`确定删除该用药记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('删除成功')
			}).catch(() => {})
		},

		handleViewMedicationDetail(row) {
			this.$message.info('查看用药详情功能开发中...')
		},

		handleEditTestResult(row) {
			this.$message.info('编辑检查结果功能开发中...')
		},

		handleDeleteTestResult(row) {
			this.$confirm(`确定删除该检查记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('删除成功')
			}).catch(() => {})
		},

		handleViewTestDetail(row) {
			this.$message.info('查看检查详情功能开发中...')
		},

		handleViewPatientFromMedication(row) {
			this.handleViewMedicalProfile(row)
		},

		handleExportMedicationData(searchForm) {
			this.$confirm('确定导出用药数据?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$message.success('导出成功')
			}).catch(() => {})
		},

		// ==================== 核心业务方法 ====================
		// 重置患者表单
		resetPatientForm() {
			this.numberValidateForm = {
				patientId: '',
				realName: '',
				sex: 1,
				birthDate: '',
				isUnderAge: 0,
				idNumber: '',
				ethnicity: '汉族',
				maritalStatus: '1',
				occupation: '',
				currentAddress: '',
				insuranceType: '1',
				insuranceNumber: '',
				primaryContactName: '',
				primaryContactRelation: '',
				primaryContactPhone: '',
				userName: '',
				userId: '',
				phone: '',
			}
		},

		// 填充患者表单
		fillPatientForm(row) {
			this.numberValidateForm = { ...row }
		},

		// 删除患者
		async deletePatient(patientId) {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/patientInfo/delete'),
					method: 'post',
					data: this.$http.adornData([patientId], false)
				})

				if (response.data && response.data.code === 0) {
					this.$message.success('删除成功')
					// 刷新患者基础信息组件数据
					if (this.$refs.patientBasicInfo) {
						this.$refs.patientBasicInfo.loadData()
					}
				} else {
					this.$message.error(response.data.msg || '删除失败')
				}
			} catch (error) {
				this.$message.error('删除失败')
			}
		},

		// 风险评估
		async assessPatientRisk(patientId) {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/patientMedical/assessMedicalRisk'),
					method: 'post',
					params: this.$http.adornParams({ patientId })
				})

				if (response.data && response.data.code === 0) {
					this.$message.success('风险评估完成')
					// 刷新患者基础信息组件数据
					if (this.$refs.patientBasicInfo) {
						this.$refs.patientBasicInfo.loadData()
					}
				} else {
					this.$message.error(response.data.msg || '风险评估失败')
				}
			} catch (error) {
				this.$message.error('风险评估失败')
			}
		},

		// 导出患者数据
		exportPatientData(searchForm) {
			const params = this.$http.adornParams(searchForm, true)
			window.open(this.$http.adornUrl(`admin/patientInfo/exportPatientData?${params}`))
		},

		// ==================== 对话框处理方法 ====================
		// 保存患者信息
		async handleSavePatient() {
			try {
				const url = this.numberValidateForm.patientId ?
					'admin/patientInfo/update' :
					'admin/patientInfo/save'

				const response = await this.$http({
					url: this.$http.adornUrl(url),
					method: 'post',
					data: this.$http.adornData(this.numberValidateForm)
				})

				if (response.data && response.data.code === 0) {
					this.$message.success(this.numberValidateForm.patientId ? '修改成功' : '添加成功')
					this.dialogVisible = false
					// 刷新患者基础信息组件数据
					if (this.$refs.patientBasicInfo) {
						this.$refs.patientBasicInfo.loadData()
					}
				} else {
					this.$message.error(response.data.msg || '操作失败')
				}
			} catch (error) {
				this.$message.error('操作失败')
			}
		},

		// 选择用户
		handleSelectUser(user) {
			this.numberValidateForm.userId = user.userId
			this.numberValidateForm.userName = user.userName
			this.selectedUserInfo = user
			this.dialogVisibles = false
			this.$message.success('用户选择成功')
		},

		// ==================== 数据字典加载 ====================
		// 加载所有数据字典
		async loadAllDictionaries() {
			await Promise.all([
				this.loadRiskLevelDict(),
				this.loadHealthLevelDict(),
				this.loadHistoryTypeDict(),
				this.loadAllergyTypeDict(),
				this.loadAllergySeverityDict(),
				this.loadMedicationTypeDict(),
				this.loadMedicationStatusDict(),
				this.loadMaritalStatusDict(),
				this.loadInsuranceTypeDict()
			])
		},

		// 加载风险等级字典
		async loadRiskLevelDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'patient_risk_level' })
				})
				if (response.data && response.data.code === 0) {
					this.riskLevelList = response.data.data || []
				}
			} catch (error) {
				console.error('加载风险等级字典失败:', error)
			}
		},

		// 加载健康等级字典
		async loadHealthLevelDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'health_level' })
				})
				if (response.data && response.data.code === 0) {
					this.healthLevelOptions = response.data.data || []
				}
			} catch (error) {
				console.error('加载健康等级字典失败:', error)
			}
		},

		// 加载病史类型字典
		async loadHistoryTypeDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'medical_history_type' })
				})
				if (response.data && response.data.code === 0) {
					this.historyTypeOptions = response.data.data || []
				}
			} catch (error) {
				console.error('加载病史类型字典失败:', error)
			}
		},

		// 加载过敏类型字典
		async loadAllergyTypeDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'allergy_type' })
				})
				if (response.data && response.data.code === 0) {
					this.allergyTypeOptions = response.data.data || []
				}
			} catch (error) {
				console.error('加载过敏类型字典失败:', error)
			}
		},

		// 加载过敏严重程度字典
		async loadAllergySeverityDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'allergy_severity' })
				})
				if (response.data && response.data.code === 0) {
					this.allergySeverityOptions = response.data.data || []
				}
			} catch (error) {
				console.error('加载过敏严重程度字典失败:', error)
			}
		},

		// 加载药物类型字典
		async loadMedicationTypeDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'medication_type' })
				})
				if (response.data && response.data.code === 0) {
					this.medicationTypeOptions = response.data.data || []
				}
			} catch (error) {
				console.error('加载药物类型字典失败:', error)
			}
		},

		// 加载用药状态字典
		async loadMedicationStatusDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'medication_status' })
				})
				if (response.data && response.data.code === 0) {
					this.medicationStatusOptions = response.data.data || []
				}
			} catch (error) {
				console.error('加载用药状态字典失败:', error)
			}
		},

		// 加载婚姻状况字典
		async loadMaritalStatusDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'marital_status' })
				})
				if (response.data && response.data.code === 0) {
					this.maritalStatusList = response.data.data || []
				}
			} catch (error) {
				console.error('加载婚姻状况字典失败:', error)
			}
		},

		// 加载医保类型字典
		async loadInsuranceTypeDict() {
			try {
				const response = await this.$http({
					url: this.$http.adornUrl('admin/dict/getByType'),
					method: 'get',
					params: this.$http.adornParams({ type: 'insurance_type' })
				})
				if (response.data && response.data.code === 0) {
					this.insuranceTypeList = response.data.data || []
				}
			} catch (error) {
				console.error('加载医保类型字典失败:', error)
			}
	},

	// ==================== 用户管理相关方法 ====================
	//获取用户列表
	getUserList() {
		this.tableDataLoadings = true
		this.$http({
			url: this.$http.adornUrl('user/selectUserList'),
			method: 'get',
			params: this.$http.adornParams({
				'page': this.page1,
				'limit': this.limit1,
				'isPromotion': -1,
				'phone': this.phoneUser,
				'userName': this.userNameUser,
				'isAuthentication': 2,
				'isAgent': -1
			})
		}).then(({
			data
		}) => {
			if (data && data.code === 0) {
				this.tableDataLoadings = false
				let returnData = data.data;
				this.userList = returnData;
			}
		})
	},

	selectUser() {
		this.page1 = 1
		this.getUserList()
	},

	cleansUser() {
		this.page1 = 1
		this.phoneUser = ''
		this.userNameUser = ''
		this.getUserList()
	},

	// 用户列表弹框
	userBtn() {
		this.dialogVisibles = true;
		this.getUserList()
	},

	//获取就诊关系
	getgxList() {
		let data = {
			type: '就诊人关系'
		}
		this.$http({
			url: this.$http.adornUrl('sys/dict/selectDictList'),
			method: 'get',
			params: this.$http.adornParams(data)
		}).then(({
			data
		}) => {
			if (data && data.code === 0) {
				this.gxList = data.data
			}
		})
	},

	// ==================== 用户分页处理 ====================
	handleCurrentChanges(val) {
		this.page1 = val
		this.getUserList()
	},

	handleSizeChanges(val) {
		this.limit1 = val
		this.getUserList()
	},
			// 添加、修改患者弹框
			addPatient(row) {
				if (row) {
					this.titles = '修改患者信息'
					this.numberValidateForm.patientId = row.patientId
					this.numberValidateForm.userId = row.userId
					this.numberValidateForm.userName = row.userName
					this.numberValidateForm.realName = row.realName
					this.numberValidateForm.sex = row.sex
					this.numberValidateForm.birthDate = row.birthDate
					this.numberValidateForm.isUnderAge = row.isUnderAge
					this.numberValidateForm.phone = row.phone
					this.numberValidateForm.idNumber = row.idNumber
					this.numberValidateForm.ethnicity = row.ethnicity || '汉族'
					this.numberValidateForm.maritalStatus = row.maritalStatus || '1'
					this.numberValidateForm.occupation = row.occupation || ''
					this.numberValidateForm.currentAddress = row.currentAddress || ''
					this.numberValidateForm.insuranceType = row.insuranceType || '1'
					this.numberValidateForm.insuranceNumber = row.insuranceNumber || ''
					this.numberValidateForm.primaryContactName = row.primaryContactName || ''
					this.numberValidateForm.primaryContactRelation = row.primaryContactRelation || ''
					this.numberValidateForm.primaryContactPhone = row.primaryContactPhone || ''

					// 如果有用户信息，设置selectedUserInfo用于显示
					if (row.userId && row.userName) {
						this.selectedUserInfo = {
							realName: row.realName,
							sex: row.sex,
							phone: row.phone,
							birthDate: row.birthDate,
							idNumber: row.idNumber
						}
					}
				} else {
					this.titles = '添加患者'
					this.numberValidateForm.patientId = ''
					this.numberValidateForm.userId = ''
					this.numberValidateForm.userName = ''
					this.numberValidateForm.realName = ''
					this.numberValidateForm.sex = 1
					this.numberValidateForm.birthDate = ''
					this.numberValidateForm.isUnderAge = 0
					this.numberValidateForm.phone = ''
					this.numberValidateForm.idNumber = ''
					this.numberValidateForm.ethnicity = '汉族'
					this.numberValidateForm.maritalStatus = '1'
					this.numberValidateForm.occupation = ''
					this.numberValidateForm.currentAddress = ''
					this.numberValidateForm.insuranceType = '1'
					this.numberValidateForm.insuranceNumber = ''
					this.numberValidateForm.primaryContactName = ''
					this.numberValidateForm.primaryContactRelation = ''
					this.numberValidateForm.primaryContactPhone = ''

					// 清空用户信息显示
					this.selectedUserInfo = {}
				}
				this.dialogVisible = true
			},
			// 添加/修改患者
			addNoticeTo() {
				if (this.numberValidateForm.userId == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请选择所属用户',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.realName == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入患者姓名',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.phone == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入联系电话',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.idNumber == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入身份证号码',
						type: 'warning'
					});
					return
				}

				this.$http({
					url: this.$http.adornUrl('admin/patientInfo/savePatient'),
					method: 'post',
					data: this.$http.adornData({
						'patientId': this.numberValidateForm.patientId,
						'userId': this.numberValidateForm.userId,
						'realName': this.numberValidateForm.realName,
						'sex': this.numberValidateForm.sex,
						'birthDate': this.numberValidateForm.birthDate,
						'isUnderAge': this.numberValidateForm.isUnderAge,
						'phone': this.numberValidateForm.phone,
						'idNumber': this.numberValidateForm.idNumber,
						'ethnicity': this.numberValidateForm.ethnicity,
						'maritalStatus': this.numberValidateForm.maritalStatus,
						'occupation': this.numberValidateForm.occupation,
						'currentAddress': this.numberValidateForm.currentAddress,
						'insuranceType': this.numberValidateForm.insuranceType,
						'insuranceNumber': this.numberValidateForm.insuranceNumber,
						'primaryContactName': this.numberValidateForm.primaryContactName,
						'primaryContactRelation': this.numberValidateForm.primaryContactRelation,
						'primaryContactPhone': this.numberValidateForm.primaryContactPhone
					})
				}).then(({
					data
				}) => {
					if (data.code == 0) {
						this.dialogVisible = false
						this.$message({
							message: '操作成功',
							type: 'success',
							duration: 1500,
							onClose: () => {
								this.dataSelect()
							}
						})
					} else {
						this.$message({
							message: data.msg,
							type: 'warning',
							duration: 1500,
							onClose: () => {}
						})
					}

				})
			},
			// 选择用户
			amendBanner(row) {
				this.numberValidateForm.userName = row.userName
				this.numberValidateForm.userId = row.userId

				// 保存选中用户的详细信息用于显示
				this.selectedUserInfo = {
					realName: row.realName,
					sex: row.sex,
					phone: row.phone,
					birthDate: row.birthDate,
					idNumber: row.idNumber,
					avatar: row.avatar
				}

				// 自动填充用户的基本信息到表单
				if (row.realName) {
					this.numberValidateForm.realName = row.realName
				}
				if (row.sex) {
					this.numberValidateForm.sex = row.sex
				}
				if (row.phone) {
					this.numberValidateForm.phone = row.phone
				}
				if (row.birthDate) {
					this.numberValidateForm.birthDate = row.birthDate
				}
				if (row.idNumber) {
					this.numberValidateForm.idNumber = row.idNumber
				}

				this.dialogVisibles = false

				// 显示提示信息
				this.$message({
					message: `已选择用户：${row.userName}，相关信息已自动填充`,
					type: 'success',
					duration: 2000
				})
			},
			// 清除选择的用户
			clearSelectedUser() {
				this.$confirm('确定要清除已选择的用户吗？这将清空相关的自动填充信息。', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.numberValidateForm.userId = ''
					this.numberValidateForm.userName = ''
					this.selectedUserInfo = {}

					// 可选：是否清空自动填充的信息
					// this.numberValidateForm.realName = ''
					// this.numberValidateForm.sex = 1
					// this.numberValidateForm.phone = ''
					// this.numberValidateForm.birthDate = ''
					// this.numberValidateForm.idNumber = ''

					this.$message({
						message: '已清除用户选择',
						type: 'success',
						duration: 1500
					})
				}).catch(() => {})
			},
			//删除
			deletes(row) {
				this.$confirm(`确定删除此条信息?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					let data = {
						userId: row.userId,
						patientId: row.patientId
					}
					this.$http({
						url: this.$http.adornUrl('admin/patientInfo/deletePatient'),
						method: 'get',
						params: this.$http.adornParams(data)
					}).then(({
						data
					}) => {
						if (data.code == 0) {
							this.$message({
								message: '删除成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									this.dataSelect()
								}
							})
						} else {
							this.$message({
								message: data.msg,
								type: 'warning',
								duration: 1500,
								onClose: () => {}
							})
						}

					})
				}).catch(() => {})
			},
			// 详情跳转
			updates(userId) {
				if (userId) {
					this.$router.push({
						path: '/userDetail',
						query: {
							userId: userId
						}
					})
				}

			},


			// Tab切换处理
			handleClick(tab, event) {
				console.log('切换到tab:', tab.name)
				// 根据不同的tab加载对应的数据
				switch(tab.name) {
					case 'first':
						this.dataSelect() // 加载患者基础信息
						break
					case 'health':
						this.loadHealthRecords() // 加载健康记录
						break
					case 'history':
						this.loadMedicalHistories() // 加载病史记录
						break
					case 'allergy':
						this.loadAllergyRecords() // 加载过敏记录
						break
					case 'medication':
						this.loadMedicationRecords() // 加载用药记录
						this.loadTestRecords() // 加载检查记录
						break
				}
			},

			// 健康状态管理相关方法
			loadHealthRecords() {
				// 模拟数据，实际应该调用API
				this.healthRecords = [
					{
						patientName: '张三',
						healthLevel: '1',
						checkDate: '2024-01-15',
						checkItems: '常规体检',
						result: '各项指标正常',
						doctor: '李医生'
					}
				]
			},
			addHealthRecord() {
				this.healthRecordForm = {
					patientName: '',
					healthLevel: '1',
					checkDate: '',
					checkItems: '',
					result: '',
					doctor: ''
				}
				this.healthRecordDialogVisible = true
				this.healthRecordDialogTitle = '添加健康记录'
			},
			editHealthRecord(row) {
				this.healthRecordForm = { ...row }
				this.healthRecordDialogVisible = true
				this.healthRecordDialogTitle = '编辑健康记录'
			},
			deleteHealthRecord(row) {
				this.$confirm('确定要删除这条健康记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					// 从数组中删除
					const index = this.healthRecords.findIndex(item => item === row)
					if (index > -1) {
						this.healthRecords.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			saveHealthRecord() {
				if (!this.healthRecordForm.patientName || !this.healthRecordForm.checkDate) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.healthRecordDialogTitle === '添加健康记录') {
					// 添加新记录
					this.healthRecords.push({ ...this.healthRecordForm })
					this.$message.success('添加成功')
				} else {
					// 编辑记录
					const index = this.healthRecords.findIndex(item =>
						item.patientName === this.healthRecordForm.patientName &&
						item.checkDate === this.healthRecordForm.checkDate
					)
					if (index > -1) {
						this.healthRecords.splice(index, 1, { ...this.healthRecordForm })
						this.$message.success('修改成功')
					}
				}

				this.healthRecordDialogVisible = false
			},
			exportHealthData() {
				this.$message.info('导出健康数据功能开发中...')
			},
			getHealthLevelText(level) {
				const levels = {'1': '优秀', '2': '良好', '3': '一般', '4': '较差', '5': '差'}
				return levels[level] || '未评估'
			},
			getHealthLevelType(level) {
				const types = {'1': 'success', '2': 'success', '3': 'warning', '4': 'danger', '5': 'danger'}
				return types[level] || 'info'
			},

			// 病史记录管理相关方法
			loadMedicalHistories() {
				// 模拟数据，实际应该调用API
				this.medicalHistories = [
					{
						patientName: '张三',
						historyType: '1',
						diagnosis: '高血压',
						description: '患者有高血压病史3年',
						recordDate: '2024-01-10',
						doctor: '王医生'
					}
				]
			},
			addMedicalHistory() {
				this.medicalHistoryForm = {
					patientName: '',
					historyType: '1',
					diagnosis: '',
					description: '',
					recordDate: '',
					doctor: ''
				}
				this.medicalHistoryDialogVisible = true
				this.medicalHistoryDialogTitle = '添加病史记录'
			},
			editMedicalHistory(row) {
				this.medicalHistoryForm = { ...row }
				this.medicalHistoryDialogVisible = true
				this.medicalHistoryDialogTitle = '编辑病史记录'
			},
			deleteMedicalHistory(row) {
				this.$confirm('确定要删除这条病史记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const index = this.medicalHistories.findIndex(item => item === row)
					if (index > -1) {
						this.medicalHistories.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			saveMedicalHistory() {
				if (!this.medicalHistoryForm.patientName || !this.medicalHistoryForm.diagnosis) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.medicalHistoryDialogTitle === '添加病史记录') {
					this.medicalHistories.push({ ...this.medicalHistoryForm })
					this.$message.success('添加成功')
				} else {
					const index = this.medicalHistories.findIndex(item =>
						item.patientName === this.medicalHistoryForm.patientName &&
						item.diagnosis === this.medicalHistoryForm.diagnosis
					)
					if (index > -1) {
						this.medicalHistories.splice(index, 1, { ...this.medicalHistoryForm })
						this.$message.success('修改成功')
					}
				}

				this.medicalHistoryDialogVisible = false
			},
			exportHistoryData() {
				this.$message.info('导出病史数据功能开发中...')
			},
			getHistoryTypeText(type) {
				const types = {'1': '既往史', '2': '现病史', '3': '家族史', '4': '过敏史', '5': '手术史'}
				return types[type] || '未知'
			},
			getHistoryTypeColor(type) {
				const colors = {'1': 'primary', '2': 'warning', '3': 'info', '4': 'danger', '5': 'success'}
				return colors[type] || 'info'
			},

			// 过敏信息管理相关方法
			loadAllergyRecords() {
				// 模拟数据，实际应该调用API
				this.allergyRecords = [
					{
						patientName: '张三',
						allergyType: '1',
						allergen: '青霉素',
						severity: '3',
						symptoms: '皮疹、呼吸困难',
						recordDate: '2024-01-05'
					}
				]
			},
			addAllergyRecord() {
				this.allergyRecordForm = {
					patientName: '',
					allergyType: '1',
					allergen: '',
					severity: '1',
					symptoms: '',
					recordDate: ''
				}
				this.allergyRecordDialogVisible = true
				this.allergyRecordDialogTitle = '添加过敏记录'
			},
			editAllergyRecord(row) {
				this.allergyRecordForm = { ...row }
				this.allergyRecordDialogVisible = true
				this.allergyRecordDialogTitle = '编辑过敏记录'
			},
			deleteAllergyRecord(row) {
				this.$confirm('确定要删除这条过敏记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const index = this.allergyRecords.findIndex(item => item === row)
					if (index > -1) {
						this.allergyRecords.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			saveAllergyRecord() {
				if (!this.allergyRecordForm.patientName || !this.allergyRecordForm.allergen) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.allergyRecordDialogTitle === '添加过敏记录') {
					this.allergyRecords.push({ ...this.allergyRecordForm })
					this.$message.success('添加成功')
				} else {
					const index = this.allergyRecords.findIndex(item =>
						item.patientName === this.allergyRecordForm.patientName &&
						item.allergen === this.allergyRecordForm.allergen
					)
					if (index > -1) {
						this.allergyRecords.splice(index, 1, { ...this.allergyRecordForm })
						this.$message.success('修改成功')
					}
				}

				this.allergyRecordDialogVisible = false
			},
			exportAllergyData() {
				this.$message.info('导出过敏数据功能开发中...')
			},
			getAllergyTypeText(type) {
				const types = {'1': '药物过敏', '2': '食物过敏', '3': '环境过敏', '4': '接触过敏', '5': '其他过敏'}
				return types[type] || '未知'
			},
			getAllergyTypeColor(type) {
				const colors = {'1': 'danger', '2': 'warning', '3': 'info', '4': 'primary', '5': 'success'}
				return colors[type] || 'info'
			},
			getSeverityText(severity) {
				const severities = {'1': '轻微', '2': '中度', '3': '严重', '4': '危及生命'}
				return severities[severity] || '未知'
			},
			getSeverityColor(severity) {
				const colors = {'1': 'success', '2': 'warning', '3': 'danger', '4': 'danger'}
				return colors[severity] || 'info'
			},

			// 用药检查管理相关方法
			loadMedicationRecords() {
				// 模拟数据，实际应该调用API
				this.medicationRecords = [
					{
						patientName: '张三',
						medicationName: '阿司匹林',
						medicationType: '处方药',
						dosage: '100mg',
						frequency: '每日一次',
						status: '1',
						startDate: '2024-01-01'
					}
				]
			},
			loadTestRecords() {
				// 模拟数据，实际应该调用API
				this.testRecords = [
					{
						patientName: '张三',
						testType: '血液检查',
						testName: '血常规',
						result: '正常',
						resultStatus: '1',
						testDate: '2024-01-15',
						doctor: '李医生'
					}
				]
			},
			addMedicationRecord() {
				this.medicationRecordForm = {
					patientName: '',
					medicationName: '',
					medicationType: '处方药',
					dosage: '',
					frequency: '',
					status: '1',
					startDate: ''
				}
				this.medicationRecordDialogVisible = true
				this.medicationRecordDialogTitle = '添加用药记录'
			},
			addTestRecord() {
				this.testRecordForm = {
					patientName: '',
					testType: '血液检查',
					testName: '',
					result: '',
					resultStatus: '1',
					testDate: '',
					doctor: ''
				}
				this.testRecordDialogVisible = true
				this.testRecordDialogTitle = '添加检查记录'
			},
			editMedicationRecord(row) {
				this.medicationRecordForm = { ...row }
				this.medicationRecordDialogVisible = true
				this.medicationRecordDialogTitle = '编辑用药记录'
			},
			deleteMedicationRecord(row) {
				this.$confirm('确定要删除这条用药记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const index = this.medicationRecords.findIndex(item => item === row)
					if (index > -1) {
						this.medicationRecords.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			editTestRecord(row) {
				this.testRecordForm = { ...row }
				this.testRecordDialogVisible = true
				this.testRecordDialogTitle = '编辑检查记录'
			},
			deleteTestRecord(row) {
				this.$confirm('确定要删除这条检查记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const index = this.testRecords.findIndex(item => item === row)
					if (index > -1) {
						this.testRecords.splice(index, 1)
						this.$message.success('删除成功')
					}
				}).catch(() => {})
			},
			saveMedicationRecord() {
				if (!this.medicationRecordForm.patientName || !this.medicationRecordForm.medicationName) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.medicationRecordDialogTitle === '添加用药记录') {
					this.medicationRecords.push({ ...this.medicationRecordForm })
					this.$message.success('添加成功')
				} else {
					const index = this.medicationRecords.findIndex(item =>
						item.patientName === this.medicationRecordForm.patientName &&
						item.medicationName === this.medicationRecordForm.medicationName
					)
					if (index > -1) {
						this.medicationRecords.splice(index, 1, { ...this.medicationRecordForm })
						this.$message.success('修改成功')
					}
				}

				this.medicationRecordDialogVisible = false
			},
			saveTestRecord() {
				if (!this.testRecordForm.patientName || !this.testRecordForm.testName) {
					this.$message.error('请填写必填字段')
					return
				}

				if (this.testRecordDialogTitle === '添加检查记录') {
					this.testRecords.push({ ...this.testRecordForm })
					this.$message.success('添加成功')
				} else {
					const index = this.testRecords.findIndex(item =>
						item.patientName === this.testRecordForm.patientName &&
						item.testName === this.testRecordForm.testName
					)
					if (index > -1) {
						this.testRecords.splice(index, 1, { ...this.testRecordForm })
						this.$message.success('修改成功')
					}
				}

				this.testRecordDialogVisible = false
			},
			exportMedicationData() {
				this.$message.info('导出用药检查数据功能开发中...')
			},
			getMedicationStatusText(status) {
				const statuses = {'1': '正在使用', '2': '已停用', '3': '暂停使用'}
				return statuses[status] || '未知'
			},
			getMedicationStatusColor(status) {
				const colors = {'1': 'success', '2': 'info', '3': 'warning'}
				return colors[status] || 'info'
			},
			getTestResultText(status) {
				const statuses = {'1': '正常', '2': '异常偏高', '3': '异常偏低', '4': '临界值', '5': '严重异常'}
				return statuses[status] || '未知'
			},
		getTestResultColor(status) {
			const colors = {'1': 'success', '2': 'danger', '3': 'danger', '4': 'warning', '5': 'danger'}
			return colors[status] || 'info'
		}
	},

	// ==================== 生命周期钩子 ====================
	async mounted() {
		// 加载所有数据字典
		await this.loadAllDictionaries()

		// 加载其他必要数据
		this.getgxList()
	}
}
</script>

<style scoped>
.enhanced-patient-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 标签页样式 */
.el-tabs--border-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: white;
}

.el-tabs--border-card > .el-tabs__header {
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  border: none;
  color: #606266;
  font-weight: 500;
  padding: 0 30px;
  height: 50px;
  line-height: 50px;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  background: #409EFF;
  color: white;
  border-radius: 6px 6px 0 0;
  margin: 4px 2px 0 2px;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 20px;
  background: white;
  border-radius: 0 0 8px 8px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-patient-management {
    padding: 10px;
  }

  .el-tabs--border-card > .el-tabs__header .el-tabs__item {
    padding: 0 15px;
    font-size: 14px;
  }

  .el-tabs--border-card > .el-tabs__content {
    padding: 15px;
  }
}

/* 加载状态样式 */
.el-loading-mask {
  border-radius: 8px;
}

/* 成功状态样式 */
.status-success {
  color: #67c23a;
}

.status-warning {
  color: #e6a23c;
}

.status-danger {
  color: #f56c6c;
}

.status-info {
  color: #909399;
}

/* 标签样式增强 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式增强 */
.el-button--mini {
  border-radius: 4px;
  font-weight: 500;
}

/* 表格样式增强 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background: #fafafa;
  color: #606266;
  font-weight: 600;
}

.el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.el-table--border {
  border: 1px solid #e4e7ed;
}

/* 分页样式 */
.el-pagination {
  text-align: center;
  margin-top: 20px;
}

.el-pagination .el-pager li {
  border-radius: 4px;
  margin: 0 2px;
}

.el-pagination .el-pager li.active {
  background: #409EFF;
  color: white;
}

/* 进度条样式 */
.el-progress-bar__outer {
  border-radius: 10px;
}

.el-progress-bar__inner {
  border-radius: 10px;
}

/* 输入框样式 */
.el-input__inner {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 选择器样式 */
.el-select .el-input__inner {
  cursor: pointer;
}

/* 日期选择器样式 */
.el-date-editor .el-input__inner {
  cursor: pointer;
}

/* 警告框样式 */
.el-alert {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 卡片样式 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
}

.el-card__header {
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #303133;
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* 工具提示样式 */
.el-tooltip__popper {
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
