-- 婚姻状况和医保类型字典初始化脚本（通用版）
-- 适用于MySQL、PostgreSQL等数据库

-- 清理已存在的数据
DELETE FROM sys_dict WHERE type IN ('marital_status', 'insurance_type');

-- 1. 婚姻状况字典数据
-- 父级记录（假设ID为200，实际执行时请根据您的数据库情况调整）
INSERT INTO sys_dict (id, name, type, code, value, order_num, remark, parent_id) 
VALUES (200, '婚姻状况', 'marital_status', NULL, NULL, 1, '患者婚姻状况', 0);

-- 子级记录
INSERT INTO sys_dict (id, name, type, code, value, order_num, remark, parent_id) VALUES
(201, '未婚', 'marital_status', '1', '未婚', 1, '未婚', 200),
(202, '已婚', 'marital_status', '2', '已婚', 2, '已婚', 200),
(203, '离异', 'marital_status', '3', '离异', 3, '离异', 200),
(204, '丧偶', 'marital_status', '4', '丧偶', 4, '丧偶', 200);

-- 2. 医保类型字典数据
-- 父级记录（假设ID为210，实际执行时请根据您的数据库情况调整）
INSERT INTO sys_dict (id, name, type, code, value, order_num, remark, parent_id) 
VALUES (210, '医保类型', 'insurance_type', NULL, NULL, 1, '医疗保险类型', 0);

-- 子级记录
INSERT INTO sys_dict (id, name, type, code, value, order_num, remark, parent_id) VALUES
(211, '城镇职工', 'insurance_type', '1', '城镇职工', 1, '城镇职工', 210),
(212, '城镇居民', 'insurance_type', '2', '城镇居民', 2, '城镇居民', 210),
(213, '新农合', 'insurance_type', '3', '新农合', 3, '新农合', 210),
(214, '商业保险', 'insurance_type', '4', '商业保险', 4, '商业保险', 210),
(215, '自费', 'insurance_type', '5', '自费', 5, '自费', 210);

-- 验证插入结果
SELECT 'marital_status' as dict_type, id, name, type, code, value, order_num, parent_id 
FROM sys_dict 
WHERE type = 'marital_status' 
ORDER BY parent_id, order_num;

SELECT 'insurance_type' as dict_type, id, name, type, code, value, order_num, parent_id 
FROM sys_dict 
WHERE type = 'insurance_type' 
ORDER BY parent_id, order_num;
