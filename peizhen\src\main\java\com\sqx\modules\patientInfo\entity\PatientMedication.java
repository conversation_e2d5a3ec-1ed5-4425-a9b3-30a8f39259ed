package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者用药记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_medication")
public class PatientMedication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用药记录id
     */
    @TableId(value = "medication_id", type = IdType.AUTO)
    @ApiModelProperty("用药记录id")
    private Long medicationId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 药物名称(通用名)
     */
    @ApiModelProperty("药物名称(通用名)")
    @TableField(condition = SqlCondition.LIKE)
    private String medicationName;

    /**
     * 商品名
     */
    @ApiModelProperty("商品名")
    private String brandName;

    /**
     * 药物编码
     */
    @ApiModelProperty("药物编码")
    private String medicationCode;

    /**
     * 药物分类(1处方药 2非处方药 3中药 4生物制品)
     */
    @ApiModelProperty("药物分类(1处方药 2非处方药 3中药 4生物制品)")
    private Integer medicationType;

    /**
     * 剂型(1片剂 2胶囊 3注射剂 4口服液 5外用药 6其他)
     */
    @ApiModelProperty("剂型(1片剂 2胶囊 3注射剂 4口服液 5外用药 6其他)")
    private Integer dosageForm;

    /**
     * 规格强度
     */
    @ApiModelProperty("规格强度")
    private String strength;

    /**
     * 单次剂量
     */
    @ApiModelProperty("单次剂量")
    private BigDecimal singleDose;

    /**
     * 剂量单位(mg、ml、片等)
     */
    @ApiModelProperty("剂量单位(mg、ml、片等)")
    private String doseUnit;

    /**
     * 用药频率(1每日一次 2每日两次 3每日三次 4每日四次 5按需服用 6其他)
     */
    @ApiModelProperty("用药频率(1每日一次 2每日两次 3每日三次 4每日四次 5按需服用 6其他)")
    private Integer frequency;

    /**
     * 用药时间(1餐前 2餐后 3餐中 4空腹 5睡前 6其他)
     */
    @ApiModelProperty("用药时间(1餐前 2餐后 3餐中 4空腹 5睡前 6其他)")
    private Integer timingOfAdministration;

    /**
     * 用药途径(1口服 2注射 3外用 4吸入 5直肠给药 6其他)
     */
    @ApiModelProperty("用药途径(1口服 2注射 3外用 4吸入 5直肠给药 6其他)")
    private Integer routeOfAdministration;

    /**
     * 开始用药时间
     */
    @ApiModelProperty("开始用药时间")
    private String startDate;

    /**
     * 结束用药时间
     */
    @ApiModelProperty("结束用药时间")
    private String endDate;

    /**
     * 用药目的/适应症
     */
    @ApiModelProperty("用药目的/适应症")
    private String indication;

    /**
     * 处方医生
     */
    @ApiModelProperty("处方医生")
    private String prescribingDoctor;

    /**
     * 处方医院
     */
    @ApiModelProperty("处方医院")
    private String prescribingHospital;

    /**
     * 处方日期
     */
    @ApiModelProperty("处方日期")
    private String prescriptionDate;

    /**
     * 处方编号
     */
    @ApiModelProperty("处方编号")
    private String prescriptionNumber;

    /**
     * 药房信息
     */
    @ApiModelProperty("药房信息")
    private String pharmacy;

    /**
     * 用药状态(1正在使用 2已停用 3暂停使用)
     */
    @ApiModelProperty("用药状态(1正在使用 2已停用 3暂停使用)")
    private Integer medicationStatus;

    /**
     * 停药原因
     */
    @ApiModelProperty("停药原因")
    private String discontinuationReason;

    /**
     * 依从性评估(1完全依从 2基本依从 3部分依从 4不依从)
     */
    @ApiModelProperty("依从性评估(1完全依从 2基本依从 3部分依从 4不依从)")
    private Integer adherence;

    /**
     * 疗效评估(1显效 2有效 3无效 4恶化)
     */
    @ApiModelProperty("疗效评估(1显效 2有效 3无效 4恶化)")
    private Integer effectiveness;

    /**
     * 副作用记录
     */
    @ApiModelProperty("副作用记录")
    private String sideEffects;

    /**
     * 特殊注意事项
     */
    @ApiModelProperty("特殊注意事项")
    private String specialInstructions;

    /**
     * 处方图片路径
     */
    @ApiModelProperty("处方图片路径")
    private String prescriptionImagePath;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String notes;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
