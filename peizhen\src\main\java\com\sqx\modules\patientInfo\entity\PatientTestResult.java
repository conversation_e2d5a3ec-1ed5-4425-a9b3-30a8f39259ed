package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者检查检验结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_test_result")
public class PatientTestResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检查结果id
     */
    @TableId(value = "test_id", type = IdType.AUTO)
    @ApiModelProperty("检查结果id")
    private Long testId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 检查类型(1血液检查 2尿液检查 3影像检查 4心电图 5病理检查 6其他)
     */
    @ApiModelProperty("检查类型(1血液检查 2尿液检查 3影像检查 4心电图 5病理检查 6其他)")
    private Integer testType;

    /**
     * 检查项目名称
     */
    @ApiModelProperty("检查项目名称")
    @TableField(condition = SqlCondition.LIKE)
    private String testName;

    /**
     * 检查项目编码
     */
    @ApiModelProperty("检查项目编码")
    private String testCode;

    /**
     * 检查日期
     */
    @ApiModelProperty("检查日期")
    private String testDate;

    /**
     * 检查医院
     */
    @ApiModelProperty("检查医院")
    private String testHospital;

    /**
     * 检查科室
     */
    @ApiModelProperty("检查科室")
    private String testDepartment;

    /**
     * 检查医生
     */
    @ApiModelProperty("检查医生")
    private String testDoctor;

    /**
     * 检查结果值
     */
    @ApiModelProperty("检查结果值")
    private String testValue;

    /**
     * 数值结果
     */
    @ApiModelProperty("数值结果")
    private BigDecimal numericValue;

    /**
     * 结果单位
     */
    @ApiModelProperty("结果单位")
    private String unit;

    /**
     * 参考范围
     */
    @ApiModelProperty("参考范围")
    private String referenceRange;

    /**
     * 结果状态(1正常 2异常偏高 3异常偏低 4临界值 5无法判断)
     */
    @ApiModelProperty("结果状态(1正常 2异常偏高 3异常偏低 4临界值 5无法判断)")
    private Integer resultStatus;

    /**
     * 异常程度(1轻度异常 2中度异常 3重度异常)
     */
    @ApiModelProperty("异常程度(1轻度异常 2中度异常 3重度异常)")
    private Integer abnormalityLevel;

    /**
     * 检查方法
     */
    @ApiModelProperty("检查方法")
    private String testMethod;

    /**
     * 检查设备
     */
    @ApiModelProperty("检查设备")
    private String testEquipment;

    /**
     * 标本类型(1血清 2血浆 3全血 4尿液 5粪便 6其他)
     */
    @ApiModelProperty("标本类型(1血清 2血浆 3全血 4尿液 5粪便 6其他)")
    private Integer specimenType;

    /**
     * 采样时间
     */
    @ApiModelProperty("采样时间")
    private String specimenCollectionTime;

    /**
     * 报告时间
     */
    @ApiModelProperty("报告时间")
    private String reportTime;

    /**
     * 报告医生
     */
    @ApiModelProperty("报告医生")
    private String reportDoctor;

    /**
     * 审核医生
     */
    @ApiModelProperty("审核医生")
    private String reviewDoctor;

    /**
     * 临床意义
     */
    @ApiModelProperty("临床意义")
    private String clinicalSignificance;

    /**
     * 建议
     */
    @ApiModelProperty("建议")
    private String recommendations;

    /**
     * 报告编号
     */
    @ApiModelProperty("报告编号")
    private String reportNumber;

    /**
     * 检查报告文件路径
     */
    @ApiModelProperty("检查报告文件路径")
    private String reportFilePath;

    /**
     * 影像文件路径
     */
    @ApiModelProperty("影像文件路径")
    private String imageFilePath;

    /**
     * 是否紧急结果(0否 1是)
     */
    @ApiModelProperty("是否紧急结果(0否 1是)")
    private Integer isUrgent;

    /**
     * 复查建议时间
     */
    @ApiModelProperty("复查建议时间")
    private String followUpDate;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String notes;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
