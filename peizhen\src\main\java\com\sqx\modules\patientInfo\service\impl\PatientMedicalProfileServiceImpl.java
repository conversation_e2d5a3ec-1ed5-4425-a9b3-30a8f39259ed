package com.sqx.modules.patientInfo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.patientInfo.dao.*;
import com.sqx.modules.patientInfo.entity.*;
import com.sqx.modules.patientInfo.service.PatientMedicalProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 患者医疗档案综合服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Service
public class PatientMedicalProfileServiceImpl implements PatientMedicalProfileService {

    @Autowired
    private PatientInfoDao patientInfoDao;
    
    @Autowired
    private PatientHealthStatusDao healthStatusDao;
    
    @Autowired
    private PatientMedicalHistoryDao medicalHistoryDao;
    
    @Autowired
    private PatientAllergyDao allergyDao;
    
    @Autowired
    private PatientMedicationDao medicationDao;
    
    @Autowired
    private PatientVitalSignsDao vitalSignsDao;
    
    @Autowired
    private PatientTestResultDao testResultDao;
    
    @Autowired
    private PatientVaccinationDao vaccinationDao;
    
    @Autowired
    private PatientTreatmentPlanDao treatmentPlanDao;

    // ==================== 综合医疗档案管理 ====================
    
    @Override
    public Result getCompleteMedicalProfile(Long patientId) {
        try {
            PatientInfo patientInfo = patientInfoDao.selectById(patientId);
            if (patientInfo == null) {
                return Result.error("患者信息不存在");
            }

            // 获取健康状态
            PatientHealthStatus healthStatus = healthStatusDao.getByPatientId(patientId);
            patientInfo.setHealthStatus(healthStatus);

            // 获取活跃病史
            List<PatientMedicalHistory> medicalHistoryList = medicalHistoryDao.getActiveHistoryByPatient(patientId);
            patientInfo.setMedicalHistoryList(medicalHistoryList);

            // 获取活跃过敏记录
            List<PatientAllergy> allergyList = allergyDao.getActiveAllergiesByPatient(patientId);
            patientInfo.setAllergyList(allergyList);

            // 获取当前用药
            List<PatientMedication> currentMedicationList = medicationDao.getCurrentMedicationsByPatient(patientId);
            patientInfo.setCurrentMedicationList(currentMedicationList);

            // 获取最新生命体征
            PatientVitalSigns latestVitalSigns = vitalSignsDao.getLatestByPatient(patientId);
            patientInfo.setLatestVitalSigns(latestVitalSigns);

            // 获取最近检查结果
            List<PatientTestResult> recentTestResults = testResultDao.getRecentByPatient(patientId, 10);
            patientInfo.setRecentTestResults(recentTestResults);

            // 获取当前治疗方案
            List<PatientTreatmentPlan> currentTreatmentPlans = treatmentPlanDao.getCurrentByPatient(patientId);
            patientInfo.setCurrentTreatmentPlans(currentTreatmentPlans);

            // 获取疫苗接种记录
            List<PatientVaccination> vaccinationList = vaccinationDao.getByPatient(patientId);
            patientInfo.setVaccinationList(vaccinationList);

            return Result.success().put("data", patientInfo);
        } catch (Exception e) {
            return Result.error("获取医疗档案失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result updateProfileCompleteness(Long patientId) {
        try {
            PatientInfo patientInfo = patientInfoDao.selectById(patientId);
            if (patientInfo == null) {
                return Result.error("患者信息不存在");
            }

            int completeness = calculateProfileCompleteness(patientId);
            patientInfo.setProfileCompleteness(completeness);
            patientInfo.setLastMedicalUpdateTime(LocalDateTime.now());
            
            patientInfoDao.updateById(patientInfo);
            return Result.success().put("completeness", completeness);
        } catch (Exception e) {
            return Result.error("更新档案完整度失败：" + e.getMessage());
        }
    }

    @Override
    public Result assessMedicalRisk(Long patientId) {
        try {
            int riskScore = calculateRiskScore(patientId);
            int riskLevel = 1; // 默认低风险
            
            if (riskScore >= 70) {
                riskLevel = 3; // 高风险
            } else if (riskScore >= 40) {
                riskLevel = 2; // 中风险
            }

            // 更新患者风险等级
            PatientInfo patientInfo = patientInfoDao.selectById(patientId);
            if (patientInfo != null) {
                patientInfo.setRiskLevel(riskLevel);
                patientInfoDao.updateById(patientInfo);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("riskScore", riskScore);
            result.put("riskLevel", riskLevel);
            result.put("riskDescription", getRiskDescription(riskLevel));
            
            return Result.success().put("data", result);
        } catch (Exception e) {
            return Result.error("风险评估失败：" + e.getMessage());
        }
    }

    // ==================== 健康状态管理 ====================
    
    @Override
    @Transactional
    public Result saveHealthStatus(PatientHealthStatus healthStatus) {
        try {
            if (healthStatus.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }

            // 计算BMI
            if (healthStatus.getHeight() != null && healthStatus.getWeight() != null) {
                double heightInMeters = healthStatus.getHeight().doubleValue() / 100;
                double bmi = healthStatus.getWeight().doubleValue() / (heightInMeters * heightInMeters);
                healthStatus.setBmi(java.math.BigDecimal.valueOf(Math.round(bmi * 100.0) / 100.0));
            }

            // 检查是否已存在健康状态记录
            PatientHealthStatus existing = healthStatusDao.getByPatientId(healthStatus.getPatientId());
            if (existing != null) {
                healthStatus.setStatusId(existing.getStatusId());
                healthStatusDao.updateById(healthStatus);
            } else {
                healthStatusDao.insert(healthStatus);
            }

            // 更新档案完整度
            updateProfileCompleteness(healthStatus.getPatientId());
            
            return Result.success("健康状态保存成功");
        } catch (Exception e) {
            return Result.error("保存健康状态失败：" + e.getMessage());
        }
    }

    @Override
    public PatientHealthStatus getHealthStatusByPatient(Long patientId) {
        return healthStatusDao.getByPatientId(patientId);
    }

    // ==================== 病史管理 ====================
    
    @Override
    @Transactional
    public Result saveMedicalHistory(PatientMedicalHistory medicalHistory) {
        try {
            if (medicalHistory.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }
            if (medicalHistory.getDiseaseName() == null || medicalHistory.getDiseaseName().trim().isEmpty()) {
                return Result.error("疾病名称不能为空");
            }

            if (medicalHistory.getHistoryId() == null) {
                medicalHistoryDao.insert(medicalHistory);
            } else {
                medicalHistoryDao.updateById(medicalHistory);
            }

            // 更新档案完整度
            updateProfileCompleteness(medicalHistory.getPatientId());
            
            return Result.success("病史记录保存成功");
        } catch (Exception e) {
            return Result.error("保存病史记录失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<PatientMedicalHistory> getMedicalHistoryList(Integer page, Integer limit, Long patientId, Integer historyType) {
        Page<PatientMedicalHistory> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return medicalHistoryDao.getHistoryList(pages, patientId, historyType);
    }

    @Override
    public List<PatientMedicalHistory> getActiveMedicalHistory(Long patientId) {
        return medicalHistoryDao.getActiveHistoryByPatient(patientId);
    }

    @Override
    @Transactional
    public Result deleteMedicalHistory(Long historyId) {
        try {
            PatientMedicalHistory medicalHistory = medicalHistoryDao.selectById(historyId);
            if (medicalHistory == null) {
                return Result.error("病史记录不存在");
            }
            
            medicalHistory.setIsDelete(1);
            medicalHistoryDao.updateById(medicalHistory);
            
            // 更新档案完整度
            updateProfileCompleteness(medicalHistory.getPatientId());
            
            return Result.success("病史记录删除成功");
        } catch (Exception e) {
            return Result.error("删除病史记录失败：" + e.getMessage());
        }
    }

    // ==================== 过敏管理 ====================

    @Override
    @Transactional
    public Result saveAllergy(PatientAllergy allergy) {
        try {
            if (allergy.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }
            if (allergy.getAllergenName() == null || allergy.getAllergenName().trim().isEmpty()) {
                return Result.error("过敏原名称不能为空");
            }

            // 检查是否已存在相同过敏原
            int existCount = allergyDao.checkAllergenExists(allergy.getPatientId(),
                allergy.getAllergenName(), allergy.getAllergyType());
            if (existCount > 0 && allergy.getAllergyId() == null) {
                return Result.error("该过敏原记录已存在");
            }

            if (allergy.getAllergyId() == null) {
                allergyDao.insert(allergy);
            } else {
                allergyDao.updateById(allergy);
            }

            // 更新档案完整度
            updateProfileCompleteness(allergy.getPatientId());

            return Result.success("过敏记录保存成功");
        } catch (Exception e) {
            return Result.error("保存过敏记录失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<PatientAllergy> getAllergyList(Integer page, Integer limit, Long patientId, Integer allergyType) {
        Page<PatientAllergy> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return allergyDao.getAllergyList(pages, patientId, allergyType);
    }

    @Override
    public List<PatientAllergy> getActiveAllergies(Long patientId) {
        return allergyDao.getActiveAllergiesByPatient(patientId);
    }

    @Override
    public Result checkAllergyConflict(Long patientId, String allergenName, Integer allergyType) {
        try {
            int existCount = allergyDao.checkAllergenExists(patientId, allergenName, allergyType);
            Map<String, Object> result = new HashMap<>();
            result.put("hasConflict", existCount > 0);
            result.put("conflictCount", existCount);

            if (existCount > 0) {
                List<PatientAllergy> conflicts = allergyDao.searchByAllergenName(patientId, allergenName);
                result.put("conflictDetails", conflicts);
            }

            return Result.success().put("data", result);
        } catch (Exception e) {
            return Result.error("检查过敏冲突失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result deleteAllergy(Long allergyId) {
        try {
            PatientAllergy allergy = allergyDao.selectById(allergyId);
            if (allergy == null) {
                return Result.error("过敏记录不存在");
            }

            allergy.setIsDelete(1);
            allergyDao.updateById(allergy);

            // 更新档案完整度
            updateProfileCompleteness(allergy.getPatientId());

            return Result.success("过敏记录删除成功");
        } catch (Exception e) {
            return Result.error("删除过敏记录失败：" + e.getMessage());
        }
    }

    // ==================== 用药管理 ====================

    @Override
    @Transactional
    public Result saveMedication(PatientMedication medication) {
        try {
            if (medication.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }
            if (medication.getMedicationName() == null || medication.getMedicationName().trim().isEmpty()) {
                return Result.error("药物名称不能为空");
            }

            if (medication.getMedicationId() == null) {
                medicationDao.insert(medication);
            } else {
                medicationDao.updateById(medication);
            }

            // 更新档案完整度
            updateProfileCompleteness(medication.getPatientId());

            return Result.success("用药记录保存成功");
        } catch (Exception e) {
            return Result.error("保存用药记录失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<PatientMedication> getMedicationList(Integer page, Integer limit, Long patientId, Integer medicationStatus) {
        Page<PatientMedication> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return medicationDao.getMedicationList(pages, patientId, medicationStatus);
    }

    @Override
    public List<PatientMedication> getCurrentMedications(Long patientId) {
        return medicationDao.getCurrentMedicationsByPatient(patientId);
    }

    @Override
    public Result checkMedicationConflict(Long patientId, String medicationName) {
        try {
            List<PatientMedication> conflicts = medicationDao.checkMedicationConflicts(patientId, medicationName);
            Map<String, Object> result = new HashMap<>();
            result.put("hasConflict", !conflicts.isEmpty());
            result.put("conflictCount", conflicts.size());
            result.put("conflictDetails", conflicts);

            return Result.success().put("data", result);
        } catch (Exception e) {
            return Result.error("检查用药冲突失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result discontinueMedication(Long medicationId, String reason) {
        try {
            PatientMedication medication = medicationDao.selectById(medicationId);
            if (medication == null) {
                return Result.error("用药记录不存在");
            }

            medication.setMedicationStatus(2); // 已停用
            medication.setDiscontinuationReason(reason);
            medication.setEndDate(java.time.LocalDate.now().toString());
            medicationDao.updateById(medication);

            // 更新档案完整度
            updateProfileCompleteness(medication.getPatientId());

            return Result.success("药物停用成功");
        } catch (Exception e) {
            return Result.error("停用药物失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result deleteMedication(Long medicationId) {
        try {
            PatientMedication medication = medicationDao.selectById(medicationId);
            if (medication == null) {
                return Result.error("用药记录不存在");
            }

            medication.setIsDelete(1);
            medicationDao.updateById(medication);

            // 更新档案完整度
            updateProfileCompleteness(medication.getPatientId());

            return Result.success("用药记录删除成功");
        } catch (Exception e) {
            return Result.error("删除用药记录失败：" + e.getMessage());
        }
    }

    // ==================== 生命体征管理 ====================

    @Override
    @Transactional
    public Result saveVitalSigns(PatientVitalSigns vitalSigns) {
        try {
            if (vitalSigns.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }

            if (vitalSigns.getVitalId() == null) {
                vitalSignsDao.insert(vitalSigns);
            } else {
                vitalSignsDao.updateById(vitalSigns);
            }

            // 更新档案完整度
            updateProfileCompleteness(vitalSigns.getPatientId());

            return Result.success("生命体征记录保存成功");
        } catch (Exception e) {
            return Result.error("保存生命体征记录失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<PatientVitalSigns> getVitalSignsList(Integer page, Integer limit, Long patientId, String startDate, String endDate) {
        Page<PatientVitalSigns> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return vitalSignsDao.getVitalSignsList(pages, patientId, startDate, endDate);
    }

    @Override
    public PatientVitalSigns getLatestVitalSigns(Long patientId) {
        return vitalSignsDao.getLatestByPatient(patientId);
    }

    @Override
    @Transactional
    public Result deleteVitalSigns(Long vitalId) {
        try {
            PatientVitalSigns vitalSigns = vitalSignsDao.selectById(vitalId);
            if (vitalSigns == null) {
                return Result.error("生命体征记录不存在");
            }

            vitalSigns.setIsDelete(1);
            vitalSignsDao.updateById(vitalSigns);

            // 更新档案完整度
            updateProfileCompleteness(vitalSigns.getPatientId());

            return Result.success("生命体征记录删除成功");
        } catch (Exception e) {
            return Result.error("删除生命体征记录失败：" + e.getMessage());
        }
    }

    // ==================== 检查结果管理 ====================

    @Override
    @Transactional
    public Result saveTestResult(PatientTestResult testResult) {
        try {
            if (testResult.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }
            if (testResult.getTestName() == null || testResult.getTestName().trim().isEmpty()) {
                return Result.error("检查项目名称不能为空");
            }

            if (testResult.getTestId() == null) {
                testResultDao.insert(testResult);
            } else {
                testResultDao.updateById(testResult);
            }

            // 更新档案完整度
            updateProfileCompleteness(testResult.getPatientId());

            return Result.success("检查结果保存成功");
        } catch (Exception e) {
            return Result.error("保存检查结果失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<PatientTestResult> getTestResultList(Integer page, Integer limit, Long patientId, Integer testType, String startDate, String endDate) {
        Page<PatientTestResult> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return testResultDao.getTestResultList(pages, patientId, testType, startDate, endDate);
    }

    @Override
    public List<PatientTestResult> getRecentTestResults(Long patientId, Integer limit) {
        return testResultDao.getRecentByPatient(patientId, limit);
    }

    @Override
    @Transactional
    public Result deleteTestResult(Long testId) {
        try {
            PatientTestResult testResult = testResultDao.selectById(testId);
            if (testResult == null) {
                return Result.error("检查结果不存在");
            }

            testResult.setIsDelete(1);
            testResultDao.updateById(testResult);

            // 更新档案完整度
            updateProfileCompleteness(testResult.getPatientId());

            return Result.success("检查结果删除成功");
        } catch (Exception e) {
            return Result.error("删除检查结果失败：" + e.getMessage());
        }
    }

    // ==================== 疫苗接种管理 ====================

    @Override
    @Transactional
    public Result saveVaccination(PatientVaccination vaccination) {
        try {
            if (vaccination.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }
            if (vaccination.getVaccineName() == null || vaccination.getVaccineName().trim().isEmpty()) {
                return Result.error("疫苗名称不能为空");
            }

            if (vaccination.getVaccinationId() == null) {
                vaccinationDao.insert(vaccination);
            } else {
                vaccinationDao.updateById(vaccination);
            }

            // 更新档案完整度
            updateProfileCompleteness(vaccination.getPatientId());

            return Result.success("疫苗接种记录保存成功");
        } catch (Exception e) {
            return Result.error("保存疫苗接种记录失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<PatientVaccination> getVaccinationList(Integer page, Integer limit, Long patientId, Integer vaccineType) {
        Page<PatientVaccination> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return vaccinationDao.getVaccinationList(pages, patientId, vaccineType);
    }

    @Override
    public List<PatientVaccination> getVaccinationHistory(Long patientId) {
        return vaccinationDao.getByPatient(patientId);
    }

    @Override
    @Transactional
    public Result deleteVaccination(Long vaccinationId) {
        try {
            PatientVaccination vaccination = vaccinationDao.selectById(vaccinationId);
            if (vaccination == null) {
                return Result.error("疫苗接种记录不存在");
            }

            vaccination.setIsDelete(1);
            vaccinationDao.updateById(vaccination);

            // 更新档案完整度
            updateProfileCompleteness(vaccination.getPatientId());

            return Result.success("疫苗接种记录删除成功");
        } catch (Exception e) {
            return Result.error("删除疫苗接种记录失败：" + e.getMessage());
        }
    }

    // ==================== 治疗方案管理 ====================

    @Override
    @Transactional
    public Result saveTreatmentPlan(PatientTreatmentPlan treatmentPlan) {
        try {
            if (treatmentPlan.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }
            if (treatmentPlan.getPlanName() == null || treatmentPlan.getPlanName().trim().isEmpty()) {
                return Result.error("治疗方案名称不能为空");
            }

            if (treatmentPlan.getPlanId() == null) {
                treatmentPlanDao.insert(treatmentPlan);
            } else {
                treatmentPlanDao.updateById(treatmentPlan);
            }

            // 更新档案完整度
            updateProfileCompleteness(treatmentPlan.getPatientId());

            return Result.success("治疗方案保存成功");
        } catch (Exception e) {
            return Result.error("保存治疗方案失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<PatientTreatmentPlan> getTreatmentPlanList(Integer page, Integer limit, Long patientId, Integer planStatus) {
        Page<PatientTreatmentPlan> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return treatmentPlanDao.getTreatmentPlanList(pages, patientId, planStatus);
    }

    @Override
    public List<PatientTreatmentPlan> getCurrentTreatmentPlans(Long patientId) {
        return treatmentPlanDao.getCurrentByPatient(patientId);
    }

    @Override
    @Transactional
    public Result updateTreatmentPlanStatus(Long planId, Integer status) {
        try {
            PatientTreatmentPlan treatmentPlan = treatmentPlanDao.selectById(planId);
            if (treatmentPlan == null) {
                return Result.error("治疗方案不存在");
            }

            treatmentPlan.setPlanStatus(status);
            treatmentPlanDao.updateById(treatmentPlan);

            // 更新档案完整度
            updateProfileCompleteness(treatmentPlan.getPatientId());

            return Result.success("治疗方案状态更新成功");
        } catch (Exception e) {
            return Result.error("更新治疗方案状态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result deleteTreatmentPlan(Long planId) {
        try {
            PatientTreatmentPlan treatmentPlan = treatmentPlanDao.selectById(planId);
            if (treatmentPlan == null) {
                return Result.error("治疗方案不存在");
            }

            treatmentPlan.setIsDelete(1);
            treatmentPlanDao.updateById(treatmentPlan);

            // 更新档案完整度
            updateProfileCompleteness(treatmentPlan.getPatientId());

            return Result.success("治疗方案删除成功");
        } catch (Exception e) {
            return Result.error("删除治疗方案失败：" + e.getMessage());
        }
    }

    // ==================== 医疗数据统计 ====================

    @Override
    public Result getMedicalDataStatistics(Long patientId) {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 基础统计
            PatientInfo patientInfo = patientInfoDao.selectById(patientId);
            if (patientInfo == null) {
                return Result.error("患者信息不存在");
            }

            // 健康状态统计
            PatientHealthStatus healthStatus = healthStatusDao.getByPatientId(patientId);
            statistics.put("hasHealthStatus", healthStatus != null);

            // 病史统计
            List<PatientMedicalHistory> historyList = medicalHistoryDao.getActiveHistoryByPatient(patientId);
            statistics.put("medicalHistoryCount", historyList != null ? historyList.size() : 0);

            // 过敏统计
            List<PatientAllergy> allergyList = allergyDao.getActiveAllergiesByPatient(patientId);
            statistics.put("allergyCount", allergyList != null ? allergyList.size() : 0);

            // 用药统计
            List<PatientMedication> medicationList = medicationDao.getCurrentMedicationsByPatient(patientId);
            statistics.put("currentMedicationCount", medicationList != null ? medicationList.size() : 0);

            // 生命体征统计
            PatientVitalSigns latestVitalSigns = vitalSignsDao.getLatestByPatient(patientId);
            statistics.put("hasLatestVitalSigns", latestVitalSigns != null);

            // 检查结果统计
            List<PatientTestResult> testResults = testResultDao.getRecentByPatient(patientId, 30);
            statistics.put("recentTestResultCount", testResults != null ? testResults.size() : 0);

            // 疫苗接种统计
            List<PatientVaccination> vaccinations = vaccinationDao.getByPatient(patientId);
            statistics.put("vaccinationCount", vaccinations != null ? vaccinations.size() : 0);

            // 治疗方案统计
            List<PatientTreatmentPlan> treatmentPlans = treatmentPlanDao.getCurrentByPatient(patientId);
            statistics.put("currentTreatmentPlanCount", treatmentPlans != null ? treatmentPlans.size() : 0);

            // 档案完整度
            statistics.put("profileCompleteness", patientInfo.getProfileCompleteness());

            // 风险等级
            statistics.put("riskLevel", patientInfo.getRiskLevel());

            return Result.success().put("data", statistics);
        } catch (Exception e) {
            return Result.error("获取医疗数据统计失败：" + e.getMessage());
        }
    }

    @Override
    public Result generateMedicalReport(Long patientId, String reportType) {
        try {
            Map<String, Object> report = new HashMap<>();

            PatientInfo patientInfo = patientInfoDao.selectById(patientId);
            if (patientInfo == null) {
                return Result.error("患者信息不存在");
            }

            report.put("patientInfo", patientInfo);
            report.put("reportType", reportType);
            report.put("generateTime", LocalDateTime.now());

            switch (reportType) {
                case "complete":
                    // 完整医疗报告
                    report.put("healthStatus", healthStatusDao.getByPatientId(patientId));
                    report.put("medicalHistory", medicalHistoryDao.getActiveHistoryByPatient(patientId));
                    report.put("allergies", allergyDao.getActiveAllergiesByPatient(patientId));
                    report.put("medications", medicationDao.getCurrentMedicationsByPatient(patientId));
                    report.put("vitalSigns", vitalSignsDao.getLatestByPatient(patientId));
                    report.put("testResults", testResultDao.getRecentByPatient(patientId, 10));
                    report.put("vaccinations", vaccinationDao.getByPatient(patientId));
                    report.put("treatmentPlans", treatmentPlanDao.getCurrentByPatient(patientId));
                    break;
                case "summary":
                    // 摘要报告
                    report.put("healthStatus", healthStatusDao.getByPatientId(patientId));
                    report.put("activeConditions", medicalHistoryDao.getActiveHistoryByPatient(patientId));
                    report.put("currentMedications", medicationDao.getCurrentMedicationsByPatient(patientId));
                    report.put("latestVitalSigns", vitalSignsDao.getLatestByPatient(patientId));
                    break;
                case "medication":
                    // 用药报告
                    report.put("currentMedications", medicationDao.getCurrentMedicationsByPatient(patientId));
                    report.put("medicationHistory", medicationDao.getMedicationHistoryByPatient(patientId));
                    report.put("allergies", allergyDao.getDrugAllergiesByPatient(patientId));
                    break;
                default:
                    return Result.error("不支持的报告类型");
            }

            return Result.success().put("data", report);
        } catch (Exception e) {
            return Result.error("生成医疗报告失败：" + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================
    
    /**
     * 计算档案完整度
     */
    private int calculateProfileCompleteness(Long patientId) {
        int totalFields = 10; // 总评估项目数
        int completedFields = 0;

        PatientInfo patientInfo = patientInfoDao.selectById(patientId);
        if (patientInfo != null) {
            // 基本信息完整度
            if (patientInfo.getRealName() != null && !patientInfo.getRealName().trim().isEmpty()) completedFields++;
            if (patientInfo.getPhone() != null && !patientInfo.getPhone().trim().isEmpty()) completedFields++;
            if (patientInfo.getIdNumber() != null && !patientInfo.getIdNumber().trim().isEmpty()) completedFields++;
        }

        // 健康状态完整度
        PatientHealthStatus healthStatus = healthStatusDao.getByPatientId(patientId);
        if (healthStatus != null) completedFields++;

        // 病史记录完整度
        List<PatientMedicalHistory> historyList = medicalHistoryDao.getActiveHistoryByPatient(patientId);
        if (historyList != null && !historyList.isEmpty()) completedFields++;

        // 过敏记录完整度
        List<PatientAllergy> allergyList = allergyDao.getActiveAllergiesByPatient(patientId);
        if (allergyList != null && !allergyList.isEmpty()) completedFields++;

        // 用药记录完整度
        List<PatientMedication> medicationList = medicationDao.getCurrentMedicationsByPatient(patientId);
        if (medicationList != null && !medicationList.isEmpty()) completedFields++;

        // 生命体征完整度
        PatientVitalSigns vitalSigns = vitalSignsDao.getLatestByPatient(patientId);
        if (vitalSigns != null) completedFields++;

        // 检查结果完整度
        List<PatientTestResult> testResults = testResultDao.getRecentByPatient(patientId, 1);
        if (testResults != null && !testResults.isEmpty()) completedFields++;

        // 疫苗接种完整度
        List<PatientVaccination> vaccinations = vaccinationDao.getByPatient(patientId);
        if (vaccinations != null && !vaccinations.isEmpty()) completedFields++;

        return (int) Math.round((double) completedFields / totalFields * 100);
    }

    /**
     * 计算风险评分
     */
    private int calculateRiskScore(Long patientId) {
        int riskScore = 0;

        // 基于年龄的风险评分
        PatientInfo patientInfo = patientInfoDao.selectById(patientId);
        if (patientInfo != null && patientInfo.getIsUnderAge() != null && patientInfo.getIsUnderAge() == 0) {
            riskScore += 10; // 成年人基础风险
        }

        // 基于病史的风险评分
        List<PatientMedicalHistory> historyList = medicalHistoryDao.getActiveHistoryByPatient(patientId);
        if (historyList != null) {
            riskScore += historyList.size() * 5; // 每个活跃病史增加5分
        }

        // 基于过敏的风险评分
        List<PatientAllergy> allergyList = allergyDao.getActiveAllergiesByPatient(patientId);
        if (allergyList != null) {
            for (PatientAllergy allergy : allergyList) {
                if (allergy.getSeverity() != null) {
                    riskScore += allergy.getSeverity() * 3; // 严重程度越高风险越大
                }
            }
        }

        // 基于用药的风险评分
        List<PatientMedication> medicationList = medicationDao.getCurrentMedicationsByPatient(patientId);
        if (medicationList != null) {
            riskScore += medicationList.size() * 2; // 每个用药增加2分
        }

        return Math.min(riskScore, 100); // 最高100分
    }

    /**
     * 获取风险等级描述
     */
    private String getRiskDescription(int riskLevel) {
        switch (riskLevel) {
            case 1:
                return "低风险：患者整体健康状况良好，无重大医疗风险";
            case 2:
                return "中风险：患者存在一定医疗风险，需要定期监测和关注";
            case 3:
                return "高风险：患者存在较高医疗风险，需要密切监测和专业医疗管理";
            default:
                return "未知风险等级";
        }
    }
}
