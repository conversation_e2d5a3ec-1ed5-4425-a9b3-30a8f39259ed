# 医疗档案系统开发计划

## 🎯 项目概述

基于已完成的前端医疗档案Tab页面功能，为8个医疗信息实体类创建完整的后端CRUD功能和前端接口对接。

## 📋 实体类分析

### 1. PatientHealthStatus - 健康状态
**字段设计：**
- id (主键)
- patientId (患者ID，外键)
- healthLevel (健康等级) - 字典表关联
- checkDate (检查日期)
- checkItems (检查项目)
- result (检查结果)
- doctor (检查医生)
- createTime, updateTime (审计字段)

**字典关联：**
- healthLevel → "健康等级"字典

### 2. PatientMedicalHistory - 病史记录
**字段设计：**
- id (主键)
- patientId (患者ID，外键)
- historyType (病史类型) - 字典表关联
- diagnosis (诊断)
- description (病史描述)
- recordDate (记录日期)
- doctor (记录医生)
- createTime, updateTime (审计字段)

**字典关联：**
- historyType → "病史类型"字典

### 3. PatientAllergy - 过敏信息
**字段设计：**
- id (主键)
- patientId (患者ID，外键)
- allergyType (过敏类型) - 字典表关联
- allergen (过敏原)
- severity (严重程度) - 字典表关联
- symptoms (过敏症状)
- recordDate (记录日期)
- createTime, updateTime (审计字段)

**字典关联：**
- allergyType → "过敏类型"字典
- severity → "过敏严重程度"字典

### 4. PatientMedication - 用药记录
**字段设计：**
- id (主键)
- patientId (患者ID，外键)
- medicationName (药物名称)
- medicationType (药物类型) - 字典表关联
- dosage (用药剂量)
- frequency (用药频次)
- status (用药状态) - 字典表关联
- startDate (开始日期)
- endDate (结束日期)
- prescribingDoctor (开药医生)
- createTime, updateTime (审计字段)

**字典关联：**
- medicationType → "药物类型"字典
- status → "用药状态"字典

### 5. PatientVitalSigns - 生命体征
**字段设计：**
- id (主键)
- patientId (患者ID，外键)
- measurementDate (测量日期)
- bloodPressureSystolic (收缩压)
- bloodPressureDiastolic (舒张压)
- heartRate (心率)
- temperature (体温)
- respiratoryRate (呼吸频率)
- oxygenSaturation (血氧饱和度)
- weight (体重)
- height (身高)
- bmi (BMI指数)
- measurementBy (测量人员)
- createTime, updateTime (审计字段)

### 6. PatientTestResult - 检查结果
**字段设计：**
- id (主键)
- patientId (患者ID，外键)
- testType (检查类型) - 字典表关联
- testName (检查项目)
- result (检查结果)
- resultStatus (结果状态) - 字典表关联
- referenceRange (参考范围)
- unit (单位)
- testDate (检查日期)
- doctor (检查医生)
- laboratory (检验科室)
- createTime, updateTime (审计字段)

**字典关联：**
- testType → "检查类型"字典
- resultStatus → "检查结果状态"字典

### 7. PatientVaccination - 疫苗接种
**字段设计：**
- id (主键)
- patientId (患者ID，外键)
- vaccineName (疫苗名称)
- vaccineType (疫苗类型) - 字典表关联
- manufacturer (生产厂家)
- batchNumber (批次号)
- vaccinationDate (接种日期)
- nextDueDate (下次接种日期)
- vaccinationSite (接种部位) - 字典表关联
- dose (剂量)
- reaction (不良反应)
- vaccinatedBy (接种人员)
- createTime, updateTime (审计字段)

**字典关联：**
- vaccineType → "疫苗类型"字典
- vaccinationSite → "接种部位"字典

### 8. PatientTreatmentPlan - 治疗方案
**字段设计：**
- id (主键)
- patientId (患者ID，外键)
- planName (方案名称)
- planType (方案类型) - 字典表关联
- diagnosis (诊断)
- treatmentGoals (治疗目标)
- treatmentMethods (治疗方法)
- medications (用药方案)
- followUpPlan (随访计划)
- startDate (开始日期)
- endDate (结束日期)
- status (方案状态) - 字典表关联
- createdBy (制定医生)
- createTime, updateTime (审计字段)

**字典关联：**
- planType → "治疗方案类型"字典
- status → "治疗方案状态"字典

## 🗄️ 数据字典设计

需要创建以下字典类型：

1. **健康等级** (健康状态)
   - 1: 优秀, 2: 良好, 3: 一般, 4: 较差, 5: 差

2. **病史类型** (病史记录)
   - 1: 既往史, 2: 现病史, 3: 家族史, 4: 过敏史, 5: 手术史

3. **过敏类型** (过敏信息)
   - 1: 药物过敏, 2: 食物过敏, 3: 环境过敏, 4: 接触过敏, 5: 其他过敏

4. **过敏严重程度** (过敏信息)
   - 1: 轻微, 2: 中度, 3: 严重, 4: 危及生命

5. **药物类型** (用药记录)
   - 1: 处方药, 2: 非处方药, 3: 中药, 4: 保健品

6. **用药状态** (用药记录)
   - 1: 正在使用, 2: 已停用, 3: 暂停使用

7. **检查类型** (检查结果)
   - 1: 血液检查, 2: 尿液检查, 3: 影像检查, 4: 心电图, 5: 超声检查, 6: 其他检查

8. **检查结果状态** (检查结果)
   - 1: 正常, 2: 异常偏高, 3: 异常偏低, 4: 临界值, 5: 严重异常

9. **疫苗类型** (疫苗接种)
   - 1: 常规疫苗, 2: 应急疫苗, 3: 特殊疫苗

10. **接种部位** (疫苗接种)
    - 1: 左上臂, 2: 右上臂, 3: 左大腿, 4: 右大腿, 5: 其他

11. **治疗方案类型** (治疗方案)
    - 1: 药物治疗, 2: 手术治疗, 3: 物理治疗, 4: 心理治疗, 5: 综合治疗

12. **治疗方案状态** (治疗方案)
    - 1: 制定中, 2: 执行中, 3: 已完成, 4: 已暂停, 5: 已取消

## 🏗️ 开发阶段规划

### 阶段1：数据库设计和字典初始化
1. 创建所有实体表的SQL脚本
2. 创建字典数据初始化脚本
3. 添加必要的索引和约束

### 阶段2：后端开发 (按实体逐个开发)
每个实体包含：
1. Entity实体类
2. Mapper接口和XML
3. Service接口和实现类
4. Controller控制器
5. 单元测试

### 阶段3：前端模块化重构
1. 将现有的大文件拆分为独立的组件
2. 创建独立的页面文件
3. 实现组件间的数据传递

### 阶段4：前端接口对接
1. 替换模拟数据为真实API调用
2. 实现患者选择功能
3. 实现字典数据动态加载
4. 添加数据验证和错误处理

### 阶段5：测试和优化
1. 功能测试
2. 性能优化
3. 用户体验优化

## 📁 文件结构规划

### 后端文件结构
```
src/main/java/io/renren/modules/medical/
├── controller/
│   ├── PatientHealthStatusController.java
│   ├── PatientMedicalHistoryController.java
│   ├── PatientAllergyController.java
│   ├── PatientMedicationController.java
│   ├── PatientVitalSignsController.java
│   ├── PatientTestResultController.java
│   ├── PatientVaccinationController.java
│   └── PatientTreatmentPlanController.java
├── entity/
│   ├── PatientHealthStatusEntity.java
│   ├── PatientMedicalHistoryEntity.java
│   ├── PatientAllergyEntity.java
│   ├── PatientMedicationEntity.java
│   ├── PatientVitalSignsEntity.java
│   ├── PatientTestResultEntity.java
│   ├── PatientVaccinationEntity.java
│   └── PatientTreatmentPlanEntity.java
├── service/
│   ├── PatientHealthStatusService.java
│   ├── PatientMedicalHistoryService.java
│   ├── PatientAllergyService.java
│   ├── PatientMedicationService.java
│   ├── PatientVitalSignsService.java
│   ├── PatientTestResultService.java
│   ├── PatientVaccinationService.java
│   └── PatientTreatmentPlanService.java
├── service/impl/
│   └── (对应的实现类)
└── dao/
    ├── PatientHealthStatusDao.java
    ├── PatientMedicalHistoryDao.java
    ├── PatientAllergyDao.java
    ├── PatientMedicationDao.java
    ├── PatientVitalSignsDao.java
    ├── PatientTestResultDao.java
    ├── PatientVaccinationDao.java
    └── PatientTreatmentPlanDao.java
```

### 前端文件结构
```
src/views/medical/
├── components/
│   ├── PatientSelector.vue (患者选择组件)
│   ├── DictSelect.vue (字典选择组件)
│   └── MedicalRecordTable.vue (通用表格组件)
├── healthStatus/
│   ├── index.vue
│   └── components/
├── medicalHistory/
│   ├── index.vue
│   └── components/
├── allergy/
│   ├── index.vue
│   └── components/
├── medication/
│   ├── index.vue
│   └── components/
├── vitalSigns/
│   ├── index.vue
│   └── components/
├── testResult/
│   ├── index.vue
│   └── components/
├── vaccination/
│   ├── index.vue
│   └── components/
└── treatmentPlan/
    ├── index.vue
    └── components/
```

## 🎯 下一步行动

1. **立即开始**：数据库设计和字典初始化
2. **优先级**：按照实体的依赖关系和重要性排序开发
3. **并行开发**：后端和前端可以并行进行
4. **持续集成**：每完成一个实体就进行测试验证

请确认这个开发计划，我将开始逐步实施！
