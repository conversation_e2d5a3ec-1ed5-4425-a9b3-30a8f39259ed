package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.patientInfo.entity.PatientVitalSigns;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 患者生命体征记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Mapper
public interface PatientVitalSignsDao extends BaseMapper<PatientVitalSigns> {

    /**
     * 分页查询患者生命体征记录
     */
    IPage<PatientVitalSigns> getVitalSignsList(@Param("pages") Page<PatientVitalSigns> pages, 
                                              @Param("patientId") Long patientId, 
                                              @Param("startDate") String startDate, 
                                              @Param("endDate") String endDate);

    /**
     * 获取患者最新生命体征
     */
    PatientVitalSigns getLatestByPatient(@Param("patientId") Long patientId);

    /**
     * 获取患者生命体征历史记录
     */
    List<PatientVitalSigns> getHistoryByPatient(@Param("patientId") Long patientId, 
                                               @Param("limit") Integer limit);

    /**
     * 获取异常生命体征记录
     */
    List<PatientVitalSigns> getAbnormalVitalSigns(@Param("patientId") Long patientId);
}
