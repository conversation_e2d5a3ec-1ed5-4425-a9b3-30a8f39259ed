-- 医疗档案管理菜单配置SQL脚本
-- 为carBrand目录下的医疗档案页面添加菜单项

-- 查看当前carBrandList相关的菜单配置
SELECT * FROM sys_menu WHERE name LIKE '%就诊%' OR url LIKE '%carBrand%' ORDER BY menu_id;

-- 1. 添加医疗档案管理父级菜单（如果不存在）
-- 假设当前最大menu_id为200，实际执行时请根据数据库情况调整
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (201, 0, '医疗档案管理', NULL, NULL, 0, 'fa fa-heartbeat', 5);

-- 2. 添加患者医疗档案菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (202, 201, '患者医疗档案', 'patientMedicalProfile', NULL, 1, 'fa fa-user-md', 1);

-- 3. 添加用药检查管理菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (203, 201, '用药检查管理', 'medicationManagement', NULL, 1, 'fa fa-pills', 2);

-- 4. 添加疫苗治疗管理菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (204, 201, '疫苗治疗管理', 'vaccinationTreatment', NULL, 1, 'fa fa-syringe', 3);

-- 5. 添加医疗字典管理菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (205, 201, '医疗字典管理', 'medicalDictManagement', NULL, 1, 'fa fa-cogs', 4);

-- 6. 为患者医疗档案添加按钮权限
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES
(206, 202, '查看', '', 'patientMedicalProfile:list', 2, '', 0),
(207, 202, '添加', '', 'patientMedicalProfile:add', 2, '', 1),
(208, 202, '修改', '', 'patientMedicalProfile:update', 2, '', 2),
(209, 202, '删除', '', 'patientMedicalProfile:delete', 2, '', 3),
(210, 202, '导出', '', 'patientMedicalProfile:export', 2, '', 4);

-- 7. 为用药检查管理添加按钮权限
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES
(211, 203, '查看', '', 'medicationManagement:list', 2, '', 0),
(212, 203, '添加', '', 'medicationManagement:add', 2, '', 1),
(213, 203, '修改', '', 'medicationManagement:update', 2, '', 2),
(214, 203, '删除', '', 'medicationManagement:delete', 2, '', 3),
(215, 203, '导出', '', 'medicationManagement:export', 2, '', 4);

-- 8. 为疫苗治疗管理添加按钮权限
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES
(216, 204, '查看', '', 'vaccinationTreatment:list', 2, '', 0),
(217, 204, '添加', '', 'vaccinationTreatment:add', 2, '', 1),
(218, 204, '修改', '', 'vaccinationTreatment:update', 2, '', 2),
(219, 204, '删除', '', 'vaccinationTreatment:delete', 2, '', 3),
(220, 204, '导出', '', 'vaccinationTreatment:export', 2, '', 4);

-- 9. 为医疗字典管理添加按钮权限
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES
(221, 205, '查看', '', 'medicalDictManagement:list', 2, '', 0),
(222, 205, '添加', '', 'medicalDictManagement:add', 2, '', 1),
(223, 205, '修改', '', 'medicalDictManagement:update', 2, '', 2),
(224, 205, '删除', '', 'medicalDictManagement:delete', 2, '', 3),
(225, 205, '配置', '', 'medicalDictManagement:config', 2, '', 4);

-- 验证插入结果
SELECT 
    m1.menu_id,
    m1.name as '父级菜单',
    m2.menu_id as '子菜单ID',
    m2.name as '子菜单名称',
    m2.url as '菜单URL',
    m2.type as '类型',
    m2.order_num as '排序'
FROM sys_menu m1 
LEFT JOIN sys_menu m2 ON m1.menu_id = m2.parent_id 
WHERE m1.name = '医疗档案管理' 
ORDER BY m1.menu_id, m2.order_num;

-- 查看所有新增的菜单项
SELECT 
    menu_id,
    parent_id,
    name,
    url,
    perms,
    CASE type 
        WHEN 0 THEN '目录'
        WHEN 1 THEN '菜单'
        WHEN 2 THEN '按钮'
    END as '类型',
    icon,
    order_num
FROM sys_menu 
WHERE menu_id >= 201 
ORDER BY menu_id;
