<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientMedicalHistoryDao">

    <!-- 分页查询患者病史记录 -->
    <select id="getHistoryList" resultType="com.sqx.modules.patientInfo.entity.PatientMedicalHistory">
        SELECT * FROM patient_medical_history 
        WHERE patient_id = #{patientId} AND is_delete = 0
        <if test="historyType != null">
            AND history_type = #{historyType}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 获取患者活跃病史记录 -->
    <select id="getActiveHistoryByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientMedicalHistory">
        SELECT * FROM patient_medical_history 
        WHERE patient_id = #{patientId} AND is_active = 1 AND is_delete = 0
        ORDER BY create_time DESC
    </select>

    <!-- 获取患者家族病史 -->
    <select id="getFamilyHistoryByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientMedicalHistory">
        SELECT * FROM patient_medical_history 
        WHERE patient_id = #{patientId} AND history_type = 5 AND is_delete = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据疾病名称搜索病史 -->
    <select id="searchByDiseaseName" resultType="com.sqx.modules.patientInfo.entity.PatientMedicalHistory">
        SELECT * FROM patient_medical_history 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND disease_name LIKE CONCAT('%', #{diseaseName}, '%')
        ORDER BY create_time DESC
    </select>

</mapper>
