package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者治疗方案表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_treatment_plan")
public class PatientTreatmentPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 治疗方案id
     */
    @TableId(value = "plan_id", type = IdType.AUTO)
    @ApiModelProperty("治疗方案id")
    private Long planId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    @TableField(condition = SqlCondition.LIKE)
    private String planName;

    /**
     * 主要诊断
     */
    @ApiModelProperty("主要诊断")
    private String primaryDiagnosis;

    /**
     * 诊断编码(ICD-10)
     */
    @ApiModelProperty("诊断编码(ICD-10)")
    private String diagnosisCode;

    /**
     * 次要诊断
     */
    @ApiModelProperty("次要诊断")
    private String secondaryDiagnosis;

    /**
     * 治疗目标
     */
    @ApiModelProperty("治疗目标")
    private String treatmentGoals;

    /**
     * 治疗方案类型(1药物治疗 2手术治疗 3物理治疗 4心理治疗 5综合治疗)
     */
    @ApiModelProperty("治疗方案类型(1药物治疗 2手术治疗 3物理治疗 4心理治疗 5综合治疗)")
    private Integer planType;

    /**
     * 治疗方案详情
     */
    @ApiModelProperty("治疗方案详情")
    private String planDetails;

    /**
     * 制定日期
     */
    @ApiModelProperty("制定日期")
    private String planDate;

    /**
     * 计划开始日期
     */
    @ApiModelProperty("计划开始日期")
    private String startDate;

    /**
     * 计划结束日期
     */
    @ApiModelProperty("计划结束日期")
    private String endDate;

    /**
     * 实际开始日期
     */
    @ApiModelProperty("实际开始日期")
    private String actualStartDate;

    /**
     * 实际结束日期
     */
    @ApiModelProperty("实际结束日期")
    private String actualEndDate;

    /**
     * 制定医生
     */
    @ApiModelProperty("制定医生")
    private String planningDoctor;

    /**
     * 执行医生
     */
    @ApiModelProperty("执行医生")
    private String executingDoctor;

    /**
     * 治疗医院
     */
    @ApiModelProperty("治疗医院")
    private String treatmentHospital;

    /**
     * 治疗科室
     */
    @ApiModelProperty("治疗科室")
    private String treatmentDepartment;

    /**
     * 方案状态(1计划中 2进行中 3已完成 4已暂停 5已取消)
     */
    @ApiModelProperty("方案状态(1计划中 2进行中 3已完成 4已暂停 5已取消)")
    private Integer planStatus;

    /**
     * 治疗进度(%)
     */
    @ApiModelProperty("治疗进度(%)")
    private Integer progress;

    /**
     * 疗效评估(1显效 2有效 3无效 4恶化)
     */
    @ApiModelProperty("疗效评估(1显效 2有效 3无效 4恶化)")
    private Integer effectiveness;

    /**
     * 副作用记录
     */
    @ApiModelProperty("副作用记录")
    private String sideEffects;

    /**
     * 依从性评估(1完全依从 2基本依从 3部分依从 4不依从)
     */
    @ApiModelProperty("依从性评估(1完全依从 2基本依从 3部分依从 4不依从)")
    private Integer adherence;

    /**
     * 调整记录
     */
    @ApiModelProperty("调整记录")
    private String adjustmentHistory;

    /**
     * 下次复诊日期
     */
    @ApiModelProperty("下次复诊日期")
    private String nextFollowUpDate;

    /**
     * 特殊注意事项
     */
    @ApiModelProperty("特殊注意事项")
    private String specialInstructions;

    /**
     * 相关文件路径
     */
    @ApiModelProperty("相关文件路径")
    private String attachmentPath;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String notes;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
