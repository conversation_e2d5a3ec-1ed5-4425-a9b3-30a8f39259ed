<template>
  <el-table v-loading="loading" :data="tableData.records" border stripe>
    <el-table-column prop="patientName" label="患者姓名" width="120">
      <template slot-scope="scope">
        <el-button 
          size="mini" 
          style="color: #409EFF;background: #fff;border: none;padding: 0;" 
          type="primary"
          @click="$emit('view-patient', scope.row)">
          {{ scope.row.patientName }}
        </el-button>
      </template>
    </el-table-column>
    <el-table-column prop="testType" label="检查类型" width="120">
      <template slot-scope="scope">
        <el-tag :type="getTestTypeColor(scope.row.testType)">
          {{ getTestTypeText(scope.row.testType) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="testName" label="检查项目" width="150">
      <template slot-scope="scope">
        <el-tooltip :content="scope.row.testName" placement="top">
          <span class="text-ellipsis">{{ scope.row.testName }}</span>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column prop="testValue" label="检查结果" width="120">
      <template slot-scope="scope">
        <span>{{ scope.row.testValue || '-' }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="numericValue" label="数值结果" width="100">
      <template slot-scope="scope">
        <span>{{ scope.row.numericValue ? scope.row.numericValue + (scope.row.unit || '') : '-' }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="referenceRange" label="参考范围" width="120">
      <template slot-scope="scope">
        <span>{{ scope.row.referenceRange || '-' }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="resultStatus" label="结果状态" width="100">
      <template slot-scope="scope">
        <el-tag :type="getResultStatusColor(scope.row.resultStatus)">
          {{ getResultStatusText(scope.row.resultStatus) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="testDate" label="检查日期" width="120"></el-table-column>
    <el-table-column prop="testHospital" label="检查医院" width="150">
      <template slot-scope="scope">
        <el-tooltip :content="scope.row.testHospital" placement="top">
          <span class="text-ellipsis">{{ scope.row.testHospital || '-' }}</span>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column prop="testDoctor" label="检查医生" width="100">
      <template slot-scope="scope">
        <span>{{ scope.row.testDoctor || '-' }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="isUrgent" label="紧急结果" width="80">
      <template slot-scope="scope">
        <el-tag :type="scope.row.isUrgent == 1 ? 'danger' : 'success'">
          {{ scope.row.isUrgent == 1 ? '是' : '否' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="200" fixed="right">
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="$emit('edit', scope.row)">编辑</el-button>
        <el-button size="mini" type="info" @click="$emit('view-detail', scope.row)">详情</el-button>
        <el-button size="mini" type="danger" @click="$emit('delete', scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'TestResultTable',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Object,
      default: () => ({ records: [], total: 0 })
    }
  },
  methods: {
    getTestTypeText(type) {
      const typeMap = {
        1: '血液检查',
        2: '尿液检查',
        3: '影像检查',
        4: '心电图',
        5: '病理检查',
        6: '其他'
      }
      return typeMap[type] || '-'
    },

    getTestTypeColor(type) {
      switch(type) {
        case 1: return 'danger'   // 血液检查
        case 2: return 'warning'  // 尿液检查
        case 3: return 'primary'  // 影像检查
        case 4: return 'success'  // 心电图
        case 5: return 'info'     // 病理检查
        case 6: return 'info'     // 其他
        default: return 'info'
      }
    },

    getResultStatusText(status) {
      const statusMap = {
        1: '正常',
        2: '异常偏高',
        3: '异常偏低',
        4: '临界值',
        5: '无法判断'
      }
      return statusMap[status] || '-'
    },

    getResultStatusColor(status) {
      switch(status) {
        case 1: return 'success'  // 正常
        case 2: return 'warning'  // 异常偏高
        case 3: return 'warning'  // 异常偏低
        case 4: return 'danger'   // 临界值
        case 5: return 'info'     // 无法判断
        default: return 'info'
      }
    }
  }
}
</script>

<style scoped>
.text-ellipsis {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
