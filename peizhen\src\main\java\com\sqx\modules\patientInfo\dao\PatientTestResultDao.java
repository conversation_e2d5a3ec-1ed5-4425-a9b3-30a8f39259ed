package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.patientInfo.entity.PatientTestResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 患者检查检验结果 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Mapper
public interface PatientTestResultDao extends BaseMapper<PatientTestResult> {

    /**
     * 分页查询患者检查结果
     */
    IPage<PatientTestResult> getTestResultList(@Param("pages") Page<PatientTestResult> pages, 
                                              @Param("patientId") Long patientId, 
                                              @Param("testType") Integer testType, 
                                              @Param("startDate") String startDate, 
                                              @Param("endDate") String endDate);

    /**
     * 获取患者最近检查结果
     */
    List<PatientTestResult> getRecentByPatient(@Param("patientId") Long patientId, 
                                              @Param("limit") Integer limit);

    /**
     * 获取异常检查结果
     */
    List<PatientTestResult> getAbnormalResults(@Param("patientId") Long patientId);

    /**
     * 根据检查项目名称搜索
     */
    List<PatientTestResult> searchByTestName(@Param("patientId") Long patientId, 
                                           @Param("testName") String testName);

    /**
     * 获取紧急检查结果
     */
    List<PatientTestResult> getUrgentResults(@Param("patientId") Long patientId);
}
