package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Data
public class PatientInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 信息id
     */
    @TableId(value = "patient_id", type = IdType.AUTO)
    @ApiModelProperty("信息id")
    private Long patientId;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 是否已满18岁(1是 0否)
     */
    @ApiModelProperty("是否已满18岁(1是 0否)")
    private Integer isUnderAge;

    /**
     * 性别 1男 2女
     */
    @ApiModelProperty("性别 1男 2女")
    private Integer sex;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @TableField(condition = SqlCondition.LIKE)
    private String realName;

    /**
     * 电话号码
     */
    @ApiModelProperty("电话号码")
    private String phone;

    /**
     * 紧急联系人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("紧急联系人")
    private String emergencyPhone;
    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    private String idNumber;
    @ApiModelProperty("是否删除")
    private Integer isDelete;
    @ApiModelProperty("就诊人关系")
    private String relationship;

    @TableField(exist = false)
    private String userName;

    // ==================== 医疗档案扩展字段 ====================

    /**
     * 出生日期
     */
    @ApiModelProperty("出生日期")
    private String birthDate;

    /**
     * 民族
     */
    @ApiModelProperty("民族")
    private String ethnicity;

    /**
     * 婚姻状况(1未婚 2已婚 3离异 4丧偶)
     */
    @ApiModelProperty("婚姻状况(1未婚 2已婚 3离异 4丧偶)")
    private Integer maritalStatus;

    /**
     * 职业
     */
    @ApiModelProperty("职业")
    private String occupation;

    /**
     * 工作单位
     */
    @ApiModelProperty("工作单位")
    private String employer;

    /**
     * 现住址
     */
    @ApiModelProperty("现住址")
    private String currentAddress;

    /**
     * 户籍地址
     */
    @ApiModelProperty("户籍地址")
    private String registeredAddress;

    /**
     * 医保类型(1城镇职工 2城镇居民 3新农合 4商业保险 5自费)
     */
    @ApiModelProperty("医保类型(1城镇职工 2城镇居民 3新农合 4商业保险 5自费)")
    private Integer insuranceType;

    /**
     * 医保卡号
     */
    @ApiModelProperty("医保卡号")
    private String insuranceNumber;

    /**
     * 主要联系人姓名
     */
    @ApiModelProperty("主要联系人姓名")
    private String primaryContactName;

    /**
     * 主要联系人关系
     */
    @ApiModelProperty("主要联系人关系")
    private String primaryContactRelation;

    /**
     * 主要联系人电话
     */
    @ApiModelProperty("主要联系人电话")
    private String primaryContactPhone;

    /**
     * 次要联系人姓名
     */
    @ApiModelProperty("次要联系人姓名")
    private String secondaryContactName;

    /**
     * 次要联系人关系
     */
    @ApiModelProperty("次要联系人关系")
    private String secondaryContactRelation;

    /**
     * 次要联系人电话
     */
    @ApiModelProperty("次要联系人电话")
    private String secondaryContactPhone;

    /**
     * 既往重大疾病史摘要
     */
    @ApiModelProperty("既往重大疾病史摘要")
    private String majorMedicalHistory;

    /**
     * 药物过敏史摘要
     */
    @ApiModelProperty("药物过敏史摘要")
    private String drugAllergySummary;

    /**
     * 当前主要用药摘要
     */
    @ApiModelProperty("当前主要用药摘要")
    private String currentMedicationSummary;

    /**
     * 特殊医疗需求
     */
    @ApiModelProperty("特殊医疗需求")
    private String specialMedicalNeeds;

    /**
     * 风险等级(1低风险 2中风险 3高风险)
     */
    @ApiModelProperty("风险等级(1低风险 2中风险 3高风险)")
    private Integer riskLevel;

    /**
     * 医疗档案完整度(%)
     */
    @ApiModelProperty("医疗档案完整度(%)")
    private Integer profileCompleteness;

    /**
     * 最后更新医疗信息时间
     */
    @ApiModelProperty("最后更新医疗信息时间")
    private LocalDateTime lastMedicalUpdateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // ==================== 关联医疗信息 (非数据库字段) ====================

    /**
     * 健康状态信息
     */
    @TableField(exist = false)
    @ApiModelProperty("健康状态信息")
    private PatientHealthStatus healthStatus;

    /**
     * 病史记录列表
     */
    @TableField(exist = false)
    @ApiModelProperty("病史记录列表")
    private List<PatientMedicalHistory> medicalHistoryList;

    /**
     * 过敏信息列表
     */
    @TableField(exist = false)
    @ApiModelProperty("过敏信息列表")
    private List<PatientAllergy> allergyList;

    /**
     * 当前用药列表
     */
    @TableField(exist = false)
    @ApiModelProperty("当前用药列表")
    private List<PatientMedication> currentMedicationList;

    /**
     * 最近生命体征
     */
    @TableField(exist = false)
    @ApiModelProperty("最近生命体征")
    private PatientVitalSigns latestVitalSigns;

    /**
     * 最近检查结果列表
     */
    @TableField(exist = false)
    @ApiModelProperty("最近检查结果列表")
    private List<PatientTestResult> recentTestResults;

    /**
     * 当前治疗方案列表
     */
    @TableField(exist = false)
    @ApiModelProperty("当前治疗方案列表")
    private List<PatientTreatmentPlan> currentTreatmentPlans;

    /**
     * 疫苗接种记录列表
     */
    @TableField(exist = false)
    @ApiModelProperty("疫苗接种记录列表")
    private List<PatientVaccination> vaccinationList;


}
