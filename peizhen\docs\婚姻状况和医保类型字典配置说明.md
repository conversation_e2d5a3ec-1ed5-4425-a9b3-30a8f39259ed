# 婚姻状况和医保类型字典配置说明

## 🎯 概述

已将患者管理页面中的"婚姻状况"和"医保类型"字段从硬编码选项改为从数据字典动态获取，提高了系统的灵活性和可维护性。

## 📋 修改内容

### 1. 前端代码修改

#### 数据结构添加
```javascript
data() {
    return {
        // 新增字典数据存储
        maritalStatusList: [], // 婚姻状况字典
        insuranceTypeList: [], // 医保类型字典
        // ... 其他数据
    }
}
```

#### 下拉选项动态化
**婚姻状况选择框：**
```html
<!-- 修改前：硬编码选项 -->
<el-select v-model="numberValidateForm.maritalStatus" placeholder="请选择婚姻状况">
    <el-option label="未婚" value="1"></el-option>
    <el-option label="已婚" value="2"></el-option>
    <el-option label="离异" value="3"></el-option>
    <el-option label="丧偶" value="4"></el-option>
</el-select>

<!-- 修改后：动态获取 -->
<el-select v-model="numberValidateForm.maritalStatus" placeholder="请选择婚姻状况">
    <el-option v-for="(item,index) in maritalStatusList" :key="index" 
               :label="item.value" :value="item.code">
    </el-option>
</el-select>
```

**医保类型选择框：**
```html
<!-- 修改前：硬编码选项 -->
<el-select v-model="numberValidateForm.insuranceType" placeholder="请选择医保类型">
    <el-option label="城镇职工" value="1"></el-option>
    <el-option label="城镇居民" value="2"></el-option>
    <el-option label="新农合" value="3"></el-option>
    <el-option label="商业保险" value="4"></el-option>
    <el-option label="自费" value="5"></el-option>
</el-select>

<!-- 修改后：动态获取 -->
<el-select v-model="numberValidateForm.insuranceType" placeholder="请选择医保类型">
    <el-option v-for="(item,index) in insuranceTypeList" :key="index" 
               :label="item.value" :value="item.code">
    </el-option>
</el-select>
```

#### 新增方法
```javascript
//获取婚姻状况字典
getMaritalStatusList() {
    let data = { type: 'marital_status' }
    this.$http({
        url: this.$http.adornUrl('sys/dict/selectDictList'),
        method: 'get',
        params: this.$http.adornParams(data)
    }).then(({data}) => {
        if (data && data.code === 0) {
            this.maritalStatusList = data.data
        }
    })
},

//获取医保类型字典
getInsuranceTypeList() {
    let data = { type: 'insurance_type' }
    this.$http({
        url: this.$http.adornUrl('sys/dict/selectDictList'),
        method: 'get',
        params: this.$http.adornParams(data)
    }).then(({data}) => {
        if (data && data.code === 0) {
            this.insuranceTypeList = data.data
        }
    })
}
```

#### 生命周期调用
```javascript
mounted() {
    this.dataSelect()
    this.getgxList();
    this.getMaritalStatusList(); // 新增
    this.getInsuranceTypeList(); // 新增
}
```

### 2. 数据库字典数据

#### 字典类型定义
- **婚姻状况**：`type = 'marital_status'`
- **医保类型**：`type = 'insurance_type'`

#### 数据结构
```sql
-- 婚姻状况字典
父级：婚姻状况 (parent_id = 0)
├── 未婚 (code = '1', value = '未婚')
├── 已婚 (code = '2', value = '已婚')  
├── 离异 (code = '3', value = '离异')
└── 丧偶 (code = '4', value = '丧偶')

-- 医保类型字典
父级：医保类型 (parent_id = 0)
├── 城镇职工 (code = '1', value = '城镇职工')
├── 城镇居民 (code = '2', value = '城镇居民')
├── 新农合 (code = '3', value = '新农合')
├── 商业保险 (code = '4', value = '商业保险')
└── 自费 (code = '5', value = '自费')
```

## 🚀 部署步骤

### 步骤1：初始化字典数据

选择以下任一方式初始化字典数据：

#### 方式一：使用Java服务初始化（推荐）
```bash
# 调用初始化接口
GET http://localhost:8080/sys/medical-dict/init
```

#### 方式二：执行SQL脚本
根据您的数据库环境选择合适的SQL脚本：

1. **MySQL（支持变量）**：`婚姻状况和医保类型字典初始化.sql`
2. **通用版本（指定ID）**：`婚姻状况和医保类型字典初始化_通用版.sql`
3. **自动ID版本**：`婚姻状况和医保类型字典初始化_自动ID.sql`

### 步骤2：验证数据
```sql
-- 查看婚姻状况字典
SELECT * FROM sys_dict WHERE type = 'marital_status' ORDER BY parent_id, order_num;

-- 查看医保类型字典  
SELECT * FROM sys_dict WHERE type = 'insurance_type' ORDER BY parent_id, order_num;
```

### 步骤3：更新前端代码
前端代码已经修改完成，重新部署前端应用即可。

### 步骤4：测试功能
1. 打开患者管理页面
2. 点击"添加患者"
3. 检查"婚姻状况"和"医保类型"下拉框是否正常显示选项
4. 测试选择和保存功能

## 🔍 验证方法

### 1. 数据库验证
```sql
-- 统计字典数据
SELECT 
    type as '字典类型',
    COUNT(*) as '记录数量',
    SUM(CASE WHEN parent_id = 0 THEN 1 ELSE 0 END) as '父级记录',
    SUM(CASE WHEN parent_id > 0 THEN 1 ELSE 0 END) as '子级记录'
FROM sys_dict 
WHERE type IN ('marital_status', 'insurance_type')
GROUP BY type;
```

预期结果：
- marital_status：6条记录（1个父级 + 4个子级）
- insurance_type：6条记录（1个父级 + 5个子级）

### 2. 前端验证
1. **下拉选项加载**：选项应该从数据库动态加载
2. **选项内容正确**：显示的选项与数据库中的数据一致
3. **保存功能正常**：选择的值能正确保存到数据库
4. **编辑功能正常**：编辑时能正确回显选中的值

### 3. 接口验证
```bash
# 测试婚姻状况字典接口
GET /sys/dict/selectDictList?type=marital_status

# 测试医保类型字典接口  
GET /sys/dict/selectDictList?type=insurance_type
```

## 🎯 优势

### 1. 灵活性提升
- **动态配置**：可以通过数据字典管理页面动态添加、修改、删除选项
- **无需重新部署**：修改字典数据不需要重新部署前端代码
- **多语言支持**：便于后续支持多语言

### 2. 维护性改善
- **统一管理**：所有字典数据在一个地方管理
- **数据一致性**：避免硬编码导致的数据不一致
- **扩展性好**：新增字典类型只需要添加数据和调用方法

### 3. 用户体验
- **加载速度**：页面加载时异步获取字典数据
- **实时更新**：字典数据修改后立即生效
- **错误处理**：网络异常时有相应的处理机制

## 🔧 故障排除

### 常见问题

1. **下拉框为空**
   - 检查字典数据是否正确插入
   - 检查接口是否正常返回数据
   - 检查前端方法是否正确调用

2. **选项显示异常**
   - 检查字典数据的code和value字段
   - 检查前端绑定的字段名是否正确

3. **保存失败**
   - 检查选中的值是否与数据库字段类型匹配
   - 检查后端保存逻辑是否支持新的字典值

### 调试方法

1. **查看网络请求**：使用浏览器开发者工具查看字典接口请求
2. **控制台日志**：在前端方法中添加console.log查看数据
3. **数据库查询**：直接查询数据库验证字典数据

## 📈 后续扩展

### 1. 其他字典类型
可以按照相同的模式添加其他字典类型：
- 民族字典
- 职业字典
- 学历字典
- 血型字典

### 2. 字典管理功能
- 添加字典管理页面
- 支持在线编辑字典数据
- 添加字典数据导入导出功能

### 3. 缓存优化
- 添加前端缓存机制
- 实现字典数据的增量更新
- 添加数据变更通知机制

通过这次改造，系统的灵活性和可维护性得到了显著提升！
