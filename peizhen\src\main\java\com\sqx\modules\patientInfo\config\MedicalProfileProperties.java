package com.sqx.modules.patientInfo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 患者医疗档案配置属性
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Data
@Component
@ConfigurationProperties(prefix = "medical.profile")
public class MedicalProfileProperties {

    /**
     * 数据验证配置
     */
    private ValidationConfig validation = new ValidationConfig();

    /**
     * 风险评估配置
     */
    private RiskAssessmentConfig riskAssessment = new RiskAssessmentConfig();

    /**
     * 文件上传配置
     */
    private FileUploadConfig fileUpload = new FileUploadConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 数据验证配置
     */
    @Data
    public static class ValidationConfig {
        
        /**
         * 是否启用严格验证模式
         */
        private boolean strictMode = false;

        /**
         * 生命体征正常范围
         */
        private VitalSignsRange vitalSigns = new VitalSignsRange();

        /**
         * 身体指标正常范围
         */
        private BodyMetricsRange bodyMetrics = new BodyMetricsRange();

        /**
         * 允许的血型列表
         */
        private List<String> allowedBloodTypes = Arrays.asList("A", "B", "AB", "O");

        /**
         * 允许的文件扩展名
         */
        private List<String> allowedFileExtensions = Arrays.asList(".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx");

        @Data
        public static class VitalSignsRange {
            private Range systolicPressure = new Range(50, 300);
            private Range diastolicPressure = new Range(30, 200);
            private Range heartRate = new Range(30, 250);
            private Range temperature = new Range(new BigDecimal("30.0"), new BigDecimal("45.0"));
            private Range respiratoryRate = new Range(5, 60);
            private Range oxygenSaturation = new Range(new BigDecimal("50.0"), new BigDecimal("100.0"));
        }

        @Data
        public static class BodyMetricsRange {
            private Range height = new Range(new BigDecimal("50.0"), new BigDecimal("250.0"));
            private Range weight = new Range(new BigDecimal("10.0"), new BigDecimal("300.0"));
            private Range bmi = new Range(new BigDecimal("10.0"), new BigDecimal("50.0"));
            private Range waistCircumference = new Range(new BigDecimal("30.0"), new BigDecimal("200.0"));
        }

        @Data
        public static class Range {
            private Number min;
            private Number max;

            public Range() {}

            public Range(Number min, Number max) {
                this.min = min;
                this.max = max;
            }
        }
    }

    /**
     * 风险评估配置
     */
    @Data
    public static class RiskAssessmentConfig {
        
        /**
         * 风险等级阈值
         */
        private RiskThresholds thresholds = new RiskThresholds();

        /**
         * 风险因子权重
         */
        private RiskFactorWeights weights = new RiskFactorWeights();

        /**
         * 是否启用自动风险评估
         */
        private boolean autoAssessment = true;

        /**
         * 风险评估更新间隔(小时)
         */
        private int assessmentInterval = 24;

        @Data
        public static class RiskThresholds {
            private int lowRisk = 30;
            private int mediumRisk = 60;
            private int highRisk = 100;
        }

        @Data
        public static class RiskFactorWeights {
            private int ageWeight = 10;
            private int medicalHistoryWeight = 5;
            private int allergyWeight = 3;
            private int medicationWeight = 2;
            private int vitalSignsWeight = 4;
        }
    }

    /**
     * 文件上传配置
     */
    @Data
    public static class FileUploadConfig {
        
        /**
         * 最大文件大小(MB)
         */
        private int maxFileSize = 10;

        /**
         * 文件存储路径
         */
        private String uploadPath = "/uploads/medical/";

        /**
         * 是否启用文件压缩
         */
        private boolean enableCompression = true;

        /**
         * 图片压缩质量(0.1-1.0)
         */
        private double imageQuality = 0.8;

        /**
         * 文件保留天数
         */
        private int retentionDays = 365;
    }

    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {
        
        /**
         * 是否启用缓存
         */
        private boolean enabled = true;

        /**
         * 缓存过期时间(分钟)
         */
        private Map<String, Integer> expiration = new HashMap<String, Integer>() {{
            put("patient-profile", 30);
            put("medical-history", 60);
            put("vital-signs", 15);
            put("test-results", 120);
        }};

        /**
         * 缓存最大条目数
         */
        private int maxEntries = 1000;

        /**
         * 是否启用缓存统计
         */
        private boolean enableStatistics = false;
    }

    /**
     * 数据完整度配置
     */
    @Data
    public static class CompletenessConfig {
        
        /**
         * 各模块权重配置
         */
        private Map<String, Integer> moduleWeights = new HashMap<String, Integer>() {{
            put("basic-info", 30);
            put("health-status", 15);
            put("medical-history", 15);
            put("allergies", 10);
            put("medications", 10);
            put("vital-signs", 10);
            put("test-results", 5);
            put("vaccinations", 5);
        }};

        /**
         * 最低完整度要求
         */
        private int minimumCompleteness = 60;

        /**
         * 是否启用完整度提醒
         */
        private boolean enableReminders = true;
    }

    /**
     * 数据保留配置
     */
    @Data
    public static class DataRetentionConfig {
        
        /**
         * 软删除数据保留天数
         */
        private int softDeleteRetentionDays = 365;

        /**
         * 日志数据保留天数
         */
        private int logRetentionDays = 90;

        /**
         * 是否启用自动清理
         */
        private boolean enableAutoCleanup = false;

        /**
         * 清理任务执行时间(cron表达式)
         */
        private String cleanupCron = "0 0 2 * * ?";
    }

    /**
     * 安全配置
     */
    @Data
    public static class SecurityConfig {
        
        /**
         * 是否启用数据脱敏
         */
        private boolean enableDataMasking = true;

        /**
         * 脱敏字段配置
         */
        private List<String> maskingFields = Arrays.asList("phone", "idNumber", "address");

        /**
         * 是否启用操作审计
         */
        private boolean enableAudit = true;

        /**
         * 敏感操作列表
         */
        private List<String> sensitiveOperations = Arrays.asList("delete", "export", "modify");
    }
}
