-- 医疗档案管理菜单配置SQL脚本（自动ID版本）
-- 自动获取最大ID，避免ID冲突

-- 查看当前最大menu_id
SELECT MAX(menu_id) as '当前最大菜单ID' FROM sys_menu;

-- 查看当前carBrandList相关的菜单配置
SELECT * FROM sys_menu WHERE name LIKE '%就诊%' OR url LIKE '%carBrand%' ORDER BY menu_id;

-- 设置变量（MySQL语法，其他数据库请相应调整）
SET @max_id = (SELECT MAX(menu_id) FROM sys_menu);
SET @parent_id = @max_id + 1;

-- 1. 添加医疗档案管理父级菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (@parent_id, 0, '医疗档案管理', NULL, NULL, 0, 'fa fa-heartbeat', 5);

-- 2. 添加患者医疗档案菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (@parent_id + 1, @parent_id, '患者医疗档案', 'patientMedicalProfile', NULL, 1, 'fa fa-user-md', 1);

-- 3. 添加用药检查管理菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (@parent_id + 2, @parent_id, '用药检查管理', 'medicationManagement', NULL, 1, 'fa fa-pills', 2);

-- 4. 添加疫苗治疗管理菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (@parent_id + 3, @parent_id, '疫苗治疗管理', 'vaccinationTreatment', NULL, 1, 'fa fa-syringe', 3);

-- 5. 添加医疗字典管理菜单
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) 
VALUES (@parent_id + 4, @parent_id, '医疗字典管理', 'medicalDictManagement', NULL, 1, 'fa fa-cogs', 4);

-- 6. 为患者医疗档案添加按钮权限
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES
(@parent_id + 5, @parent_id + 1, '查看', '', 'patientMedicalProfile:list', 2, '', 0),
(@parent_id + 6, @parent_id + 1, '添加', '', 'patientMedicalProfile:add', 2, '', 1),
(@parent_id + 7, @parent_id + 1, '修改', '', 'patientMedicalProfile:update', 2, '', 2),
(@parent_id + 8, @parent_id + 1, '删除', '', 'patientMedicalProfile:delete', 2, '', 3),
(@parent_id + 9, @parent_id + 1, '导出', '', 'patientMedicalProfile:export', 2, '', 4);

-- 7. 为用药检查管理添加按钮权限
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES
(@parent_id + 10, @parent_id + 2, '查看', '', 'medicationManagement:list', 2, '', 0),
(@parent_id + 11, @parent_id + 2, '添加', '', 'medicationManagement:add', 2, '', 1),
(@parent_id + 12, @parent_id + 2, '修改', '', 'medicationManagement:update', 2, '', 2),
(@parent_id + 13, @parent_id + 2, '删除', '', 'medicationManagement:delete', 2, '', 3),
(@parent_id + 14, @parent_id + 2, '导出', '', 'medicationManagement:export', 2, '', 4);

-- 8. 为疫苗治疗管理添加按钮权限
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES
(@parent_id + 15, @parent_id + 3, '查看', '', 'vaccinationTreatment:list', 2, '', 0),
(@parent_id + 16, @parent_id + 3, '添加', '', 'vaccinationTreatment:add', 2, '', 1),
(@parent_id + 17, @parent_id + 3, '修改', '', 'vaccinationTreatment:update', 2, '', 2),
(@parent_id + 18, @parent_id + 3, '删除', '', 'vaccinationTreatment:delete', 2, '', 3),
(@parent_id + 19, @parent_id + 3, '导出', '', 'vaccinationTreatment:export', 2, '', 4);

-- 9. 为医疗字典管理添加按钮权限
INSERT INTO sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES
(@parent_id + 20, @parent_id + 4, '查看', '', 'medicalDictManagement:list', 2, '', 0),
(@parent_id + 21, @parent_id + 4, '添加', '', 'medicalDictManagement:add', 2, '', 1),
(@parent_id + 22, @parent_id + 4, '修改', '', 'medicalDictManagement:update', 2, '', 2),
(@parent_id + 23, @parent_id + 4, '删除', '', 'medicalDictManagement:delete', 2, '', 3),
(@parent_id + 24, @parent_id + 4, '配置', '', 'medicalDictManagement:config', 2, '', 4);

-- 验证插入结果
SELECT 
    m1.menu_id,
    m1.name as '父级菜单',
    m2.menu_id as '子菜单ID',
    m2.name as '子菜单名称',
    m2.url as '菜单URL',
    CASE m2.type 
        WHEN 0 THEN '目录'
        WHEN 1 THEN '菜单'
        WHEN 2 THEN '按钮'
    END as '类型',
    m2.order_num as '排序'
FROM sys_menu m1 
LEFT JOIN sys_menu m2 ON m1.menu_id = m2.parent_id 
WHERE m1.name = '医疗档案管理' 
ORDER BY m1.menu_id, m2.order_num;

-- 统计新增的菜单数量
SELECT 
    '新增菜单统计' as '说明',
    COUNT(*) as '总数量',
    SUM(CASE WHEN type = 0 THEN 1 ELSE 0 END) as '目录数',
    SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as '菜单数',
    SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as '按钮数'
FROM sys_menu 
WHERE menu_id > @max_id;
