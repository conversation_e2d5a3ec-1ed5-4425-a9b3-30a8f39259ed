-- ========================================
-- 患者医疗档案增强功能 - 索引优化脚本
-- 创建时间: 2024-08-05
-- 描述: 为医疗档案系统创建优化索引，提升查询性能
-- ========================================

-- 1. 患者基础信息表索引优化
CREATE INDEX `idx_patient_risk_completeness` ON `patient_info` (`risk_level`, `profile_completeness`);
CREATE INDEX `idx_patient_medical_update` ON `patient_info` (`last_medical_update_time`);
CREATE INDEX `idx_patient_phone_name` ON `patient_info` (`phone`, `real_name`);
CREATE INDEX `idx_patient_id_number` ON `patient_info` (`id_number`);

-- 2. 健康状态表索引优化
CREATE INDEX `idx_health_bmi_risk` ON `patient_health_status` (`bmi`, `risk_level`);
CREATE INDEX `idx_health_blood_type` ON `patient_health_status` (`blood_type`, `rh_type`);
CREATE INDEX `idx_health_lifestyle` ON `patient_health_status` (`smoking_status`, `drinking_status`, `exercise_frequency`);

-- 3. 病史记录表索引优化
CREATE INDEX `idx_history_disease_code` ON `patient_medical_history` (`disease_code`);
CREATE INDEX `idx_history_hospital_doctor` ON `patient_medical_history` (`hospital_name`, `doctor_name`);
CREATE INDEX `idx_history_hereditary` ON `patient_medical_history` (`is_hereditary`, `family_relation`);
CREATE INDEX `idx_history_severity_active` ON `patient_medical_history` (`severity`, `is_active`);

-- 4. 过敏信息表索引优化
CREATE INDEX `idx_allergy_allergen_code` ON `patient_allergy` (`allergen_code`);
CREATE INDEX `idx_allergy_confirmation` ON `patient_allergy` (`is_confirmed`, `confirmation_method`);
CREATE INDEX `idx_allergy_emergency` ON `patient_allergy` (`requires_emergency_treatment`, `severity`);
CREATE INDEX `idx_allergy_discovery_date` ON `patient_allergy` (`first_discovery_date`);

-- 5. 用药记录表索引优化
CREATE INDEX `idx_medication_code_type` ON `patient_medication` (`medication_code`, `medication_type`);
CREATE INDEX `idx_medication_doctor_hospital` ON `patient_medication` (`prescribing_doctor`, `prescribing_hospital`);
CREATE INDEX `idx_medication_prescription` ON `patient_medication` (`prescription_number`, `prescription_date`);
CREATE INDEX `idx_medication_effectiveness` ON `patient_medication` (`effectiveness`, `adherence`);
CREATE INDEX `idx_medication_date_range` ON `patient_medication` (`start_date`, `end_date`);

-- 6. 生命体征表索引优化
CREATE INDEX `idx_vital_blood_pressure` ON `patient_vital_signs` (`systolic_pressure`, `diastolic_pressure`);
CREATE INDEX `idx_vital_temperature_heart` ON `patient_vital_signs` (`temperature`, `heart_rate`);
CREATE INDEX `idx_vital_glucose_timing` ON `patient_vital_signs` (`blood_glucose`, `glucose_measurement_timing`);
CREATE INDEX `idx_vital_abnormal` ON `patient_vital_signs` (`abnormal_indicators`(100));
CREATE INDEX `idx_vital_environment` ON `patient_vital_signs` (`measurement_environment`, `measured_by`);

-- 7. 检查结果表索引优化
CREATE INDEX `idx_test_hospital_department` ON `patient_test_result` (`test_hospital`, `test_department`);
CREATE INDEX `idx_test_doctor_reviewer` ON `patient_test_result` (`test_doctor`, `review_doctor`);
CREATE INDEX `idx_test_specimen_collection` ON `patient_test_result` (`specimen_type`, `specimen_collection_time`);
CREATE INDEX `idx_test_urgent_followup` ON `patient_test_result` (`is_urgent`, `follow_up_date`);
CREATE INDEX `idx_test_numeric_value` ON `patient_test_result` (`numeric_value`, `unit`);
CREATE INDEX `idx_test_abnormality` ON `patient_test_result` (`result_status`, `abnormality_level`);

-- 8. 疫苗接种表索引优化
CREATE INDEX `idx_vaccination_manufacturer_batch` ON `patient_vaccination` (`manufacturer`, `batch_number`);
CREATE INDEX `idx_vaccination_dose_total` ON `patient_vaccination` (`dose_number`, `total_doses`);
CREATE INDEX `idx_vaccination_site_doctor` ON `patient_vaccination` (`vaccination_site`, `vaccinating_doctor`);
CREATE INDEX `idx_vaccination_reaction` ON `patient_vaccination` (`reaction`, `reaction_description`(100));
CREATE INDEX `idx_vaccination_next_date` ON `patient_vaccination` (`next_vaccination_date`);
CREATE INDEX `idx_vaccination_certificate` ON `patient_vaccination` (`certificate_number`);

-- 9. 治疗方案表索引优化
CREATE INDEX `idx_plan_diagnosis_code` ON `patient_treatment_plan` (`diagnosis_code`);
CREATE INDEX `idx_plan_hospital_department` ON `patient_treatment_plan` (`treatment_hospital`, `treatment_department`);
CREATE INDEX `idx_plan_doctor_planning` ON `patient_treatment_plan` (`planning_doctor`, `executing_doctor`);
CREATE INDEX `idx_plan_dates` ON `patient_treatment_plan` (`start_date`, `end_date`);
CREATE INDEX `idx_plan_progress_effectiveness` ON `patient_treatment_plan` (`progress`, `effectiveness`);
CREATE INDEX `idx_plan_followup` ON `patient_treatment_plan` (`next_follow_up_date`);

-- 10. 全文搜索索引（如果支持）
-- ALTER TABLE `patient_medical_history` ADD FULLTEXT(`disease_name`, `description`);
-- ALTER TABLE `patient_allergy` ADD FULLTEXT(`allergen_name`, `symptoms`);
-- ALTER TABLE `patient_medication` ADD FULLTEXT(`medication_name`, `brand_name`);
-- ALTER TABLE `patient_test_result` ADD FULLTEXT(`test_name`, `clinical_significance`);
-- ALTER TABLE `patient_vaccination` ADD FULLTEXT(`vaccine_name`, `vaccination_reason`);
-- ALTER TABLE `patient_treatment_plan` ADD FULLTEXT(`plan_name`, `plan_details`);

-- 11. 统计信息更新（MySQL 8.0+）
-- ANALYZE TABLE patient_info, patient_health_status, patient_medical_history, 
--                patient_allergy, patient_medication, patient_vital_signs,
--                patient_test_result, patient_vaccination, patient_treatment_plan;

-- 索引创建完成提示
SELECT '医疗档案系统索引优化完成！' AS index_status;
