<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientVaccinationDao">

    <!-- 分页查询患者疫苗接种记录 -->
    <select id="getVaccinationList" resultType="com.sqx.modules.patientInfo.entity.PatientVaccination">
        SELECT * FROM patient_vaccination 
        WHERE patient_id = #{patientId} AND is_delete = 0
        <if test="vaccineType != null">
            AND vaccine_type = #{vaccineType}
        </if>
        ORDER BY vaccination_date DESC, create_time DESC
    </select>

    <!-- 获取患者疫苗接种记录 -->
    <select id="getByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientVaccination">
        SELECT * FROM patient_vaccination 
        WHERE patient_id = #{patientId} AND is_delete = 0
        ORDER BY vaccination_date DESC, create_time DESC
    </select>

    <!-- 获取未完成接种的疫苗 -->
    <select id="getIncompleteVaccinations" resultType="com.sqx.modules.patientInfo.entity.PatientVaccination">
        SELECT * FROM patient_vaccination 
        WHERE patient_id = #{patientId} AND is_completed = 0 AND is_delete = 0
        ORDER BY next_vaccination_date ASC, create_time DESC
    </select>

    <!-- 根据疫苗名称搜索接种记录 -->
    <select id="searchByVaccineName" resultType="com.sqx.modules.patientInfo.entity.PatientVaccination">
        SELECT * FROM patient_vaccination 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND vaccine_name LIKE CONCAT('%', #{vaccineName}, '%')
        ORDER BY vaccination_date DESC, create_time DESC
    </select>

    <!-- 获取需要复种的疫苗 -->
    <select id="getVaccinesNeedingBooster" resultType="com.sqx.modules.patientInfo.entity.PatientVaccination">
        SELECT * FROM patient_vaccination 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND next_vaccination_date IS NOT NULL 
        AND next_vaccination_date &lt;= CURDATE()
        ORDER BY next_vaccination_date ASC
    </select>

</mapper>
