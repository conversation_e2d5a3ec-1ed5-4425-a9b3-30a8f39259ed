<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientTreatmentPlanDao">

    <!-- 分页查询患者治疗方案 -->
    <select id="getTreatmentPlanList" resultType="com.sqx.modules.patientInfo.entity.PatientTreatmentPlan">
        SELECT * FROM patient_treatment_plan 
        WHERE patient_id = #{patientId} AND is_delete = 0
        <if test="planStatus != null">
            AND plan_status = #{planStatus}
        </if>
        ORDER BY plan_date DESC, create_time DESC
    </select>

    <!-- 获取患者当前治疗方案 -->
    <select id="getCurrentByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientTreatmentPlan">
        SELECT * FROM patient_treatment_plan 
        WHERE patient_id = #{patientId} AND plan_status IN (1, 2) AND is_delete = 0
        ORDER BY plan_date DESC, create_time DESC
    </select>

    <!-- 获取患者治疗方案历史 -->
    <select id="getHistoryByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientTreatmentPlan">
        SELECT * FROM patient_treatment_plan 
        WHERE patient_id = #{patientId} AND is_delete = 0
        ORDER BY plan_date DESC, create_time DESC
    </select>

    <!-- 根据方案名称搜索 -->
    <select id="searchByPlanName" resultType="com.sqx.modules.patientInfo.entity.PatientTreatmentPlan">
        SELECT * FROM patient_treatment_plan 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND plan_name LIKE CONCAT('%', #{planName}, '%')
        ORDER BY plan_date DESC, create_time DESC
    </select>

    <!-- 获取需要复诊的治疗方案 -->
    <select id="getPlansNeedingFollowUp" resultType="com.sqx.modules.patientInfo.entity.PatientTreatmentPlan">
        SELECT * FROM patient_treatment_plan 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND next_follow_up_date IS NOT NULL 
        AND next_follow_up_date &lt;= CURDATE()
        AND plan_status IN (1, 2)  -- 计划中或进行中
        ORDER BY next_follow_up_date ASC
    </select>

</mapper>
