<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientAllergyDao">

    <!-- 分页查询患者过敏记录 -->
    <select id="getAllergyList" resultType="com.sqx.modules.patientInfo.entity.PatientAllergy">
        SELECT * FROM patient_allergy 
        WHERE patient_id = #{patientId} AND is_delete = 0
        <if test="allergyType != null">
            AND allergy_type = #{allergyType}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 获取患者活跃过敏记录 -->
    <select id="getActiveAllergiesByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientAllergy">
        SELECT * FROM patient_allergy 
        WHERE patient_id = #{patientId} AND is_active = 1 AND is_delete = 0
        ORDER BY severity DESC, create_time DESC
    </select>

    <!-- 获取患者药物过敏记录 -->
    <select id="getDrugAllergiesByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientAllergy">
        SELECT * FROM patient_allergy 
        WHERE patient_id = #{patientId} AND allergy_type = 1 AND is_active = 1 AND is_delete = 0
        ORDER BY severity DESC, create_time DESC
    </select>

    <!-- 根据过敏原名称搜索 -->
    <select id="searchByAllergenName" resultType="com.sqx.modules.patientInfo.entity.PatientAllergy">
        SELECT * FROM patient_allergy 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND allergen_name LIKE CONCAT('%', #{allergenName}, '%')
        ORDER BY create_time DESC
    </select>

    <!-- 检查是否存在特定过敏原 -->
    <select id="checkAllergenExists" resultType="int">
        SELECT COUNT(*) FROM patient_allergy 
        WHERE patient_id = #{patientId} AND allergen_name = #{allergenName} 
        AND allergy_type = #{allergyType} AND is_delete = 0
    </select>

</mapper>
