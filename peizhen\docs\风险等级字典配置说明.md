# 风险等级字典配置说明

## 🎯 概述

将患者管理页面中的"风险等级"字段从硬编码选项改为从数据字典动态获取，使用"患者风险等级"字典类型，提高了系统的灵活性和可维护性。

## 📋 修改内容

### 1. 前端代码修改

#### 数据结构添加
```javascript
data() {
    return {
        // 新增风险等级字典数据存储
        riskLevelList: [], // 患者风险等级字典
        // ... 其他数据
    }
}
```

#### 下拉选项动态化
**风险等级选择框：**
```html
<!-- 修改前：硬编码选项 -->
<el-select v-model="riskLevel" placeholder="请选择风险等级">
    <el-option label="全部" value=""></el-option>
    <el-option label="低风险" value="1"></el-option>
    <el-option label="中风险" value="2"></el-option>
    <el-option label="高风险" value="3"></el-option>
</el-select>

<!-- 修改后：动态获取 -->
<el-select v-model="riskLevel" placeholder="请选择风险等级">
    <el-option label="全部" value=""></el-option>
    <el-option v-for="(item,index) in riskLevelList" :key="index" 
               :label="item.value" :value="item.code">
    </el-option>
</el-select>
```

#### 新增方法
```javascript
//获取患者风险等级字典
getRiskLevelList() {
    let data = { type: '患者风险等级' }
    this.$http({
        url: this.$http.adornUrl('sys/dict/selectDictList'),
        method: 'get',
        params: this.$http.adornParams(data)
    }).then(({data}) => {
        if (data && data.code === 0) {
            this.riskLevelList = data.data
        }
    })
}
```

#### 优化显示方法
```javascript
// 获取风险等级文本（修改后）
getRiskLevelText(level) {
    if (!level) return '未评估'
    const riskItem = this.riskLevelList.find(item => item.code == level)
    return riskItem ? riskItem.value : '未评估'
}
```

#### 生命周期调用
```javascript
mounted() {
    this.dataSelect()
    this.getgxList();
    this.getMaritalStatusList();
    this.getInsuranceTypeList();
    this.getRiskLevelList(); // 新增
}
```

### 2. 数据库字典数据

#### 字典类型定义
- **患者风险等级**：`type = '患者风险等级'`

#### 数据结构
```sql
-- 患者风险等级字典
父级：患者风险等级 (parent_id = 0)
├── 低风险 (code = '1', value = '低风险')
├── 中风险 (code = '2', value = '中风险')  
└── 高风险 (code = '3', value = '高风险')
```

## 🚀 部署步骤

### 步骤1：初始化字典数据

如果数据库中还没有"患者风险等级"字典数据，执行以下SQL脚本：

```sql
-- 执行 患者风险等级字典初始化.sql
```

### 步骤2：验证数据
```sql
-- 查看患者风险等级字典
SELECT * FROM sys_dict WHERE type = '患者风险等级' ORDER BY parent_id, order_num;
```

预期结果：
- 1个父级记录：患者风险等级
- 3个子级记录：低风险、中风险、高风险

### 步骤3：测试功能
1. 打开患者管理页面
2. 检查"风险等级"下拉框是否正常显示选项
3. 测试筛选和显示功能

## 🔍 验证方法

### 1. 数据库验证
```sql
-- 统计字典数据
SELECT 
    type as '字典类型',
    COUNT(*) as '记录数量',
    SUM(CASE WHEN parent_id = 0 THEN 1 ELSE 0 END) as '父级记录',
    SUM(CASE WHEN parent_id > 0 THEN 1 ELSE 0 END) as '子级记录'
FROM sys_dict 
WHERE type = '患者风险等级'
GROUP BY type;
```

预期结果：4条记录（1个父级 + 3个子级）

### 2. 前端验证
1. **下拉选项加载**：选项应该从数据库动态加载
2. **选项内容正确**：显示"低风险"、"中风险"、"高风险"
3. **筛选功能正常**：选择风险等级能正确筛选患者
4. **显示功能正常**：患者列表中风险等级能正确显示

### 3. 接口验证
```bash
# 测试患者风险等级字典接口
GET /sys/dict/selectDictList?type=患者风险等级
```

## 🎯 优势

### 1. 灵活性提升
- **动态配置**：可以通过数据字典管理页面动态添加、修改风险等级
- **无需重新部署**：修改字典数据不需要重新部署前端代码
- **扩展性好**：可以轻松添加新的风险等级

### 2. 维护性改善
- **统一管理**：风险等级数据在数据字典中统一管理
- **数据一致性**：避免硬编码导致的数据不一致
- **代码简洁**：减少硬编码，提高代码可读性

### 3. 用户体验
- **实时更新**：字典数据修改后立即生效
- **一致性**：与其他字典字段保持一致的交互体验

## 🔧 故障排除

### 常见问题

1. **下拉框显示"全部"但无其他选项**
   - 检查字典数据是否正确插入
   - 检查字典类型名称是否为"患者风险等级"
   - 检查接口是否正常返回数据

2. **风险等级显示为"未评估"**
   - 检查数据库中患者的风险等级值
   - 检查字典数据的code字段是否与患者数据匹配

3. **筛选功能失效**
   - 检查筛选条件的字段名和值
   - 检查后端接口是否支持风险等级筛选

### 调试方法

1. **查看网络请求**：使用浏览器开发者工具查看字典接口请求
2. **控制台日志**：在getRiskLevelList方法中添加console.log查看数据
3. **数据库查询**：直接查询数据库验证字典数据

## 📈 扩展建议

### 1. 风险等级评估
可以基于患者的医疗数据自动评估风险等级：
- 根据病史、用药情况等因素
- 提供风险等级评估算法
- 支持手动调整和自动评估

### 2. 风险等级颜色
为不同风险等级配置不同的显示颜色：
```javascript
getRiskLevelType(level) {
    const riskItem = this.riskLevelList.find(item => item.code == level)
    if (!riskItem) return 'info'
    
    switch(riskItem.code) {
        case '1': return 'success'  // 低风险 - 绿色
        case '2': return 'warning'  // 中风险 - 橙色  
        case '3': return 'danger'   // 高风险 - 红色
        default: return 'info'
    }
}
```

### 3. 风险等级统计
添加风险等级的统计分析功能：
- 各风险等级患者数量统计
- 风险等级分布图表
- 风险趋势分析

## 🎯 总结

通过将风险等级改为从字典表动态获取，系统的灵活性和可维护性得到了显著提升。现在管理员可以通过数据字典管理页面轻松调整风险等级的分类，而无需修改前端代码。

这种改造方式与婚姻状况、医保类型的改造保持一致，为整个系统的字典化管理奠定了良好的基础。
