-- ========================================
-- 患者医疗档案增强功能数据库迁移脚本 - 第二部分
-- 创建时间: 2024-08-04
-- 描述: 生命体征、检查结果、疫苗接种、治疗方案等表
-- ========================================

-- 8. 创建患者生命体征记录表
DROP TABLE IF EXISTS `patient_vital_signs`;
CREATE TABLE `patient_vital_signs` (
    `vital_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '生命体征记录id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `measurement_date` varchar(20) NOT NULL COMMENT '测量时间',
    `systolic_pressure` int(11) DEFAULT NULL COMMENT '收缩压(mmHg)',
    `diastolic_pressure` int(11) DEFAULT NULL COMMENT '舒张压(mmHg)',
    `heart_rate` int(11) DEFAULT NULL COMMENT '心率(次/分)',
    `temperature` decimal(4,2) DEFAULT NULL COMMENT '体温(℃)',
    `respiratory_rate` int(11) DEFAULT NULL COMMENT '呼吸频率(次/分)',
    `oxygen_saturation` decimal(5,2) DEFAULT NULL COMMENT '血氧饱和度(%)',
    `weight` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
    `height` decimal(5,2) DEFAULT NULL COMMENT '身高(cm)',
    `bmi` decimal(4,2) DEFAULT NULL COMMENT 'BMI指数',
    `waist_circumference` decimal(5,2) DEFAULT NULL COMMENT '腰围(cm)',
    `blood_glucose` decimal(5,2) DEFAULT NULL COMMENT '血糖(mmol/L)',
    `glucose_measurement_timing` int(1) DEFAULT NULL COMMENT '血糖测量时机(1空腹 2餐后2小时 3随机 4其他)',
    `pain_score` int(11) DEFAULT NULL COMMENT '疼痛评分(0-10分)',
    `consciousness_level` int(1) DEFAULT 1 COMMENT '意识状态(1清醒 2嗜睡 3昏迷 4其他)',
    `measurement_environment` int(1) DEFAULT NULL COMMENT '测量环境(1医院 2家庭 3体检中心 4其他)',
    `measurement_device` varchar(200) DEFAULT NULL COMMENT '测量设备',
    `measured_by` varchar(100) DEFAULT NULL COMMENT '测量人员',
    `abnormal_indicators` text DEFAULT NULL COMMENT '异常指标标记',
    `notes` text DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`vital_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_measurement_date` (`measurement_date`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者生命体征记录表';

-- 9. 创建患者检查检验结果表
DROP TABLE IF EXISTS `patient_test_result`;
CREATE TABLE `patient_test_result` (
    `test_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '检查结果id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `test_type` int(1) NOT NULL COMMENT '检查类型(1血液检查 2尿液检查 3影像检查 4心电图 5病理检查 6其他)',
    `test_name` varchar(200) NOT NULL COMMENT '检查项目名称',
    `test_code` varchar(50) DEFAULT NULL COMMENT '检查项目编码',
    `test_date` varchar(20) DEFAULT NULL COMMENT '检查日期',
    `test_hospital` varchar(200) DEFAULT NULL COMMENT '检查医院',
    `test_department` varchar(100) DEFAULT NULL COMMENT '检查科室',
    `test_doctor` varchar(100) DEFAULT NULL COMMENT '检查医生',
    `test_value` text DEFAULT NULL COMMENT '检查结果值',
    `numeric_value` decimal(15,6) DEFAULT NULL COMMENT '数值结果',
    `unit` varchar(50) DEFAULT NULL COMMENT '结果单位',
    `reference_range` varchar(200) DEFAULT NULL COMMENT '参考范围',
    `result_status` int(1) DEFAULT NULL COMMENT '结果状态(1正常 2异常偏高 3异常偏低 4临界值 5无法判断)',
    `abnormality_level` int(1) DEFAULT NULL COMMENT '异常程度(1轻度异常 2中度异常 3重度异常)',
    `test_method` varchar(200) DEFAULT NULL COMMENT '检查方法',
    `test_equipment` varchar(200) DEFAULT NULL COMMENT '检查设备',
    `specimen_type` int(1) DEFAULT NULL COMMENT '标本类型(1血清 2血浆 3全血 4尿液 5粪便 6其他)',
    `specimen_collection_time` varchar(20) DEFAULT NULL COMMENT '采样时间',
    `report_time` varchar(20) DEFAULT NULL COMMENT '报告时间',
    `report_doctor` varchar(100) DEFAULT NULL COMMENT '报告医生',
    `review_doctor` varchar(100) DEFAULT NULL COMMENT '审核医生',
    `clinical_significance` text DEFAULT NULL COMMENT '临床意义',
    `recommendations` text DEFAULT NULL COMMENT '建议',
    `report_number` varchar(100) DEFAULT NULL COMMENT '报告编号',
    `report_file_path` varchar(500) DEFAULT NULL COMMENT '检查报告文件路径',
    `image_file_path` varchar(500) DEFAULT NULL COMMENT '影像文件路径',
    `is_urgent` int(1) DEFAULT 0 COMMENT '是否紧急结果(0否 1是)',
    `follow_up_date` varchar(20) DEFAULT NULL COMMENT '复查建议时间',
    `notes` text DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`test_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_test_type` (`test_type`),
    KEY `idx_test_name` (`test_name`),
    KEY `idx_test_date` (`test_date`),
    KEY `idx_result_status` (`result_status`),
    KEY `idx_report_number` (`report_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者检查检验结果表';

-- 10. 创建患者疫苗接种记录表
DROP TABLE IF EXISTS `patient_vaccination`;
CREATE TABLE `patient_vaccination` (
    `vaccination_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '疫苗接种记录id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `vaccine_name` varchar(200) NOT NULL COMMENT '疫苗名称',
    `vaccine_code` varchar(50) DEFAULT NULL COMMENT '疫苗编码',
    `vaccine_type` int(1) DEFAULT NULL COMMENT '疫苗类型(1常规疫苗 2应急疫苗 3旅行疫苗 4职业疫苗)',
    `manufacturer` varchar(200) DEFAULT NULL COMMENT '疫苗厂家',
    `batch_number` varchar(100) DEFAULT NULL COMMENT '疫苗批号',
    `vaccination_date` varchar(20) DEFAULT NULL COMMENT '接种日期',
    `dose_number` int(11) DEFAULT NULL COMMENT '接种剂次(第几针)',
    `total_doses` int(11) DEFAULT NULL COMMENT '总剂次数',
    `injection_site` int(1) DEFAULT NULL COMMENT '接种部位(1左上臂 2右上臂 3左大腿 4右大腿 5其他)',
    `administration_route` int(1) DEFAULT NULL COMMENT '接种途径(1肌肉注射 2皮下注射 3口服 4鼻喷 5其他)',
    `dosage` varchar(50) DEFAULT NULL COMMENT '接种剂量(ml)',
    `vaccination_site` varchar(200) DEFAULT NULL COMMENT '接种机构',
    `vaccinating_doctor` varchar(100) DEFAULT NULL COMMENT '接种医生',
    `vaccinating_nurse` varchar(100) DEFAULT NULL COMMENT '接种护士',
    `next_vaccination_date` varchar(20) DEFAULT NULL COMMENT '下次接种日期',
    `reaction` int(1) DEFAULT 0 COMMENT '接种反应(0无反应 1轻微反应 2中度反应 3严重反应)',
    `reaction_description` text DEFAULT NULL COMMENT '反应描述',
    `reaction_treatment` text DEFAULT NULL COMMENT '反应处理',
    `contraindications` text DEFAULT NULL COMMENT '禁忌症',
    `vaccination_reason` text DEFAULT NULL COMMENT '接种原因',
    `vaccine_expiry_date` varchar(20) DEFAULT NULL COMMENT '疫苗有效期',
    `certificate_number` varchar(100) DEFAULT NULL COMMENT '接种证书编号',
    `record_file_path` varchar(500) DEFAULT NULL COMMENT '接种记录文件路径',
    `is_completed` int(1) DEFAULT 0 COMMENT '是否完成全程接种(0否 1是)',
    `notes` text DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`vaccination_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_vaccine_name` (`vaccine_name`),
    KEY `idx_vaccination_date` (`vaccination_date`),
    KEY `idx_dose_number` (`dose_number`),
    KEY `idx_is_completed` (`is_completed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者疫苗接种记录表';

-- 11. 创建患者治疗方案表
DROP TABLE IF EXISTS `patient_treatment_plan`;
CREATE TABLE `patient_treatment_plan` (
    `plan_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '治疗方案id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `plan_name` varchar(200) NOT NULL COMMENT '方案名称',
    `primary_diagnosis` varchar(500) DEFAULT NULL COMMENT '主要诊断',
    `diagnosis_code` varchar(50) DEFAULT NULL COMMENT '诊断编码(ICD-10)',
    `secondary_diagnosis` text DEFAULT NULL COMMENT '次要诊断',
    `treatment_goals` text DEFAULT NULL COMMENT '治疗目标',
    `plan_type` int(1) DEFAULT NULL COMMENT '治疗方案类型(1药物治疗 2手术治疗 3物理治疗 4心理治疗 5综合治疗)',
    `plan_details` text DEFAULT NULL COMMENT '治疗方案详情',
    `plan_date` varchar(20) DEFAULT NULL COMMENT '制定日期',
    `start_date` varchar(20) DEFAULT NULL COMMENT '计划开始日期',
    `end_date` varchar(20) DEFAULT NULL COMMENT '计划结束日期',
    `actual_start_date` varchar(20) DEFAULT NULL COMMENT '实际开始日期',
    `actual_end_date` varchar(20) DEFAULT NULL COMMENT '实际结束日期',
    `planning_doctor` varchar(100) DEFAULT NULL COMMENT '制定医生',
    `executing_doctor` varchar(100) DEFAULT NULL COMMENT '执行医生',
    `treatment_hospital` varchar(200) DEFAULT NULL COMMENT '治疗医院',
    `treatment_department` varchar(100) DEFAULT NULL COMMENT '治疗科室',
    `plan_status` int(1) DEFAULT 1 COMMENT '方案状态(1计划中 2进行中 3已完成 4已暂停 5已取消)',
    `progress` int(11) DEFAULT 0 COMMENT '治疗进度(%)',
    `effectiveness` int(1) DEFAULT NULL COMMENT '疗效评估(1显效 2有效 3无效 4恶化)',
    `side_effects` text DEFAULT NULL COMMENT '副作用记录',
    `adherence` int(1) DEFAULT NULL COMMENT '依从性评估(1完全依从 2基本依从 3部分依从 4不依从)',
    `adjustment_history` text DEFAULT NULL COMMENT '调整记录',
    `next_follow_up_date` varchar(20) DEFAULT NULL COMMENT '下次复诊日期',
    `special_instructions` text DEFAULT NULL COMMENT '特殊注意事项',
    `attachment_path` varchar(500) DEFAULT NULL COMMENT '相关文件路径',
    `notes` text DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`plan_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_plan_name` (`plan_name`),
    KEY `idx_plan_status` (`plan_status`),
    KEY `idx_plan_date` (`plan_date`),
    KEY `idx_planning_doctor` (`planning_doctor`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者治疗方案表';

-- 12. 添加外键约束
ALTER TABLE `patient_health_status` ADD CONSTRAINT `fk_health_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;
ALTER TABLE `patient_medical_history` ADD CONSTRAINT `fk_history_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;
ALTER TABLE `patient_allergy` ADD CONSTRAINT `fk_allergy_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;
ALTER TABLE `patient_adverse_reaction` ADD CONSTRAINT `fk_reaction_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;
ALTER TABLE `patient_medication` ADD CONSTRAINT `fk_medication_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;
ALTER TABLE `patient_vital_signs` ADD CONSTRAINT `fk_vital_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;
ALTER TABLE `patient_test_result` ADD CONSTRAINT `fk_test_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;
ALTER TABLE `patient_vaccination` ADD CONSTRAINT `fk_vaccination_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;
ALTER TABLE `patient_treatment_plan` ADD CONSTRAINT `fk_plan_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE;

-- 13. 创建复合索引以优化查询性能
CREATE INDEX `idx_patient_history_type_active` ON `patient_medical_history` (`patient_id`, `history_type`, `is_active`);
CREATE INDEX `idx_patient_allergy_type_active` ON `patient_allergy` (`patient_id`, `allergy_type`, `is_active`);
CREATE INDEX `idx_patient_medication_status_date` ON `patient_medication` (`patient_id`, `medication_status`, `start_date`);
CREATE INDEX `idx_patient_vital_date` ON `patient_vital_signs` (`patient_id`, `measurement_date`);
CREATE INDEX `idx_patient_test_type_date` ON `patient_test_result` (`patient_id`, `test_type`, `test_date`);
CREATE INDEX `idx_patient_plan_status_date` ON `patient_treatment_plan` (`patient_id`, `plan_status`, `plan_date`);

-- 14. 插入示例数据字典（可选）
-- INSERT INTO `sys_dict_type` VALUES (NULL, '患者风险等级', 'patient_risk_level', 1, '2024-08-04 00:00:00');
-- INSERT INTO `sys_dict_data` VALUES (NULL, 1, '低风险', '1', 'patient_risk_level', '', '', 1, '2024-08-04 00:00:00');
-- INSERT INTO `sys_dict_data` VALUES (NULL, 2, '中风险', '2', 'patient_risk_level', '', '', 1, '2024-08-04 00:00:00');
-- INSERT INTO `sys_dict_data` VALUES (NULL, 3, '高风险', '3', 'patient_risk_level', '', '', 1, '2024-08-04 00:00:00');

-- 迁移完成提示
SELECT '医疗档案增强功能数据库迁移完成！' AS migration_status;
