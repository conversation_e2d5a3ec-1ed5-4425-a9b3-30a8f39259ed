package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.patientInfo.entity.PatientTreatmentPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 患者治疗方案 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Mapper
public interface PatientTreatmentPlanDao extends BaseMapper<PatientTreatmentPlan> {

    /**
     * 分页查询患者治疗方案
     */
    IPage<PatientTreatmentPlan> getTreatmentPlanList(@Param("pages") Page<PatientTreatmentPlan> pages, 
                                                    @Param("patientId") Long patientId, 
                                                    @Param("planStatus") Integer planStatus);

    /**
     * 获取患者当前治疗方案
     */
    List<PatientTreatmentPlan> getCurrentByPatient(@Param("patientId") Long patientId);

    /**
     * 获取患者治疗方案历史
     */
    List<PatientTreatmentPlan> getHistoryByPatient(@Param("patientId") Long patientId);

    /**
     * 根据方案名称搜索
     */
    List<PatientTreatmentPlan> searchByPlanName(@Param("patientId") Long patientId, 
                                               @Param("planName") String planName);

    /**
     * 获取需要复诊的治疗方案
     */
    List<PatientTreatmentPlan> getPlansNeedingFollowUp(@Param("patientId") Long patientId);
}
