<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<!-- 疫苗接种管理 -->
			<el-tab-pane label="疫苗接种管理" name="vaccination">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchVaccination" placeholder="请输入患者姓名"
							v-model="vaccinationSearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>疫苗类型：</span>
						<el-select style="width: 200px;" v-model="vaccinationSearch.vaccineType" placeholder="请选择疫苗类型">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in vaccineTypeOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>接种反应：</span>
						<el-select style="width: 200px;" v-model="vaccinationSearch.reaction" placeholder="请选择接种反应">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in vaccinationReactionOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchVaccination">
						查询
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetVaccination">
						重置
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addVaccination()">
						添加疫苗接种
					</el-button>
				</div>
				
				<el-table v-loading="vaccinationLoading" :data="vaccinationData.records" border>
					<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
					<el-table-column prop="vaccineName" label="疫苗名称" width="150"></el-table-column>
					<el-table-column prop="vaccineType" label="疫苗类型" width="100">
						<template slot-scope="scope">
							<span>{{ getVaccineTypeText(scope.row.vaccineType) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="vaccinationDate" label="接种日期" width="120"></el-table-column>
					<el-table-column prop="doseNumber" label="接种剂次" width="100">
						<template slot-scope="scope">
							<span>{{ scope.row.doseNumber }}/{{ scope.row.totalDoses }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="manufacturer" label="生产厂家" width="150"></el-table-column>
					<el-table-column prop="batchNumber" label="批号" width="120"></el-table-column>
					<el-table-column prop="reaction" label="接种反应" width="100">
						<template slot-scope="scope">
							<el-tag :type="getVaccinationReactionType(scope.row.reaction)">
								{{ getVaccinationReactionText(scope.row.reaction) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="nextVaccinationDate" label="下次接种日期" width="140"></el-table-column>
					<el-table-column label="操作" width="200">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editVaccination(scope.row)">编辑</el-button>
							<el-button size="mini" type="danger" @click="deleteVaccination(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleVaccinationSizeChange" @current-change="handleVaccinationCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="vaccinationPage.limit" :current-page="vaccinationPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="vaccinationData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
			
			<!-- 治疗方案管理 -->
			<el-tab-pane label="治疗方案管理" name="treatment">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchTreatment" placeholder="请输入患者姓名"
							v-model="treatmentSearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>方案类型：</span>
						<el-select style="width: 200px;" v-model="treatmentSearch.planType" placeholder="请选择方案类型">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in treatmentPlanTypeOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>方案状态：</span>
						<el-select style="width: 200px;" v-model="treatmentSearch.planStatus" placeholder="请选择方案状态">
							<el-option label="全部" value=""></el-option>
							<el-option v-for="item in treatmentPlanStatusOptions" :key="item.dictValue" 
								:label="item.dictLabel" :value="item.dictValue">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchTreatment">
						查询
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="resetTreatment">
						重置
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addTreatment()">
						添加治疗方案
					</el-button>
				</div>
				
				<el-table v-loading="treatmentLoading" :data="treatmentData.records" border>
					<el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
					<el-table-column prop="planName" label="方案名称" width="150"></el-table-column>
					<el-table-column prop="planType" label="方案类型" width="100">
						<template slot-scope="scope">
							<span>{{ getTreatmentPlanTypeText(scope.row.planType) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="planDate" label="制定日期" width="120"></el-table-column>
					<el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
					<el-table-column prop="endDate" label="结束日期" width="120"></el-table-column>
					<el-table-column prop="planStatus" label="方案状态" width="100">
						<template slot-scope="scope">
							<el-tag :type="getTreatmentPlanStatusType(scope.row.planStatus)">
								{{ getTreatmentPlanStatusText(scope.row.planStatus) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="progress" label="进度" width="80">
						<template slot-scope="scope">
							<span>{{ scope.row.progress }}%</span>
						</template>
					</el-table-column>
					<el-table-column prop="effectiveness" label="疗效" width="80">
						<template slot-scope="scope">
							<el-rate v-model="scope.row.effectiveness" disabled show-score text-color="#ff9900" score-template="{value}">
							</el-rate>
						</template>
					</el-table-column>
					<el-table-column prop="nextFollowUpDate" label="下次复诊" width="120"></el-table-column>
					<el-table-column label="操作" width="250">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" @click="editTreatment(scope.row)">编辑</el-button>
							<el-button size="mini" type="success" @click="updateTreatmentStatus(scope.row)" 
								v-if="scope.row.planStatus != '3'">更新状态</el-button>
							<el-button size="mini" type="danger" @click="deleteTreatment(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				
				<div style="text-align: center;margin-top: 20px;">
					<el-pagination @size-change="handleTreatmentSizeChange" @current-change="handleTreatmentCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="treatmentPage.limit" :current-page="treatmentPage.page"
						layout="total,sizes, prev, pager, next,jumper" :total="treatmentData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
			
			<!-- 医疗数据统计 -->
			<el-tab-pane label="医疗数据统计" name="statistics">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>患者姓名：</span>
						<el-input style="width: 200px;" @keydown.enter.native="searchStatistics" placeholder="请输入患者姓名"
							v-model="statisticsSearch.patientName"></el-input>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-search" @click="searchStatistics">
						查询统计
					</el-button>
					<el-button style="margin-left:15px;" size="mini" type="success" icon="el-icon-download" @click="exportReport">
						导出报告
					</el-button>
				</div>
				
				<el-card v-if="statisticsData.patientInfo" class="box-card">
					<div slot="header" class="clearfix">
						<span>{{ statisticsData.patientInfo.realName }} - 医疗数据统计</span>
						<el-button style="float: right; padding: 3px 0" type="text" @click="generateReport">生成完整报告</el-button>
					</div>
					
					<el-row :gutter="20">
						<el-col :span="6">
							<div class="statistic-item">
								<div class="statistic-title">档案完整度</div>
								<el-progress :percentage="statisticsData.profileCompleteness || 0" 
									:color="getCompletenessColor(statisticsData.profileCompleteness)">
								</el-progress>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="statistic-item">
								<div class="statistic-title">风险等级</div>
								<el-tag :type="getRiskLevelType(statisticsData.riskLevel)" size="large">
									{{ getRiskLevelText(statisticsData.riskLevel) }}
								</el-tag>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="statistic-item">
								<div class="statistic-title">活跃病史</div>
								<div class="statistic-number">{{ statisticsData.medicalHistoryCount || 0 }}</div>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="statistic-item">
								<div class="statistic-title">过敏记录</div>
								<div class="statistic-number">{{ statisticsData.allergyCount || 0 }}</div>
							</div>
						</el-col>
					</el-row>
					
					<el-row :gutter="20" style="margin-top: 20px;">
						<el-col :span="6">
							<div class="statistic-item">
								<div class="statistic-title">当前用药</div>
								<div class="statistic-number">{{ statisticsData.currentMedicationCount || 0 }}</div>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="statistic-item">
								<div class="statistic-title">最新体征</div>
								<div class="statistic-status">
									{{ statisticsData.hasLatestVitalSigns ? '已记录' : '未记录' }}
								</div>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="statistic-item">
								<div class="statistic-title">近期检查</div>
								<div class="statistic-number">{{ statisticsData.recentTestResultCount || 0 }}</div>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="statistic-item">
								<div class="statistic-title">疫苗接种</div>
								<div class="statistic-number">{{ statisticsData.vaccinationCount || 0 }}</div>
							</div>
						</el-col>
					</el-row>
				</el-card>
				
				<el-empty v-else description="请输入患者姓名查询统计数据"></el-empty>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
export default {
	data() {
		return {
			activeName: 'vaccination',
			
			// 数据字典选项
			vaccineTypeOptions: [],
			vaccinationReactionOptions: [],
			treatmentPlanTypeOptions: [],
			treatmentPlanStatusOptions: [],
			
			// 疫苗接种
			vaccinationSearch: {
				patientName: '',
				vaccineType: '',
				reaction: ''
			},
			vaccinationPage: {
				page: 1,
				limit: 10
			},
			vaccinationData: {},
			vaccinationLoading: false,
			
			// 治疗方案
			treatmentSearch: {
				patientName: '',
				planType: '',
				planStatus: ''
			},
			treatmentPage: {
				page: 1,
				limit: 10
			},
			treatmentData: {},
			treatmentLoading: false,
			
			// 统计数据
			statisticsSearch: {
				patientName: ''
			},
			statisticsData: {}
		}
	},
	methods: {
		// 标签页切换
		handleClick(tab) {
			switch(tab.name) {
				case 'vaccination':
					this.loadVaccinationData()
					break
				case 'treatment':
					this.loadTreatmentData()
					break
				case 'statistics':
					// 统计页面不自动加载数据
					break
			}
		},
		
		// 加载数据字典
		loadDictData() {
			const dictTypes = ['vaccine_type', 'vaccination_reaction', 'treatment_plan_type', 'treatment_plan_status', 'patient_risk_level']

			dictTypes.forEach(type => {
				this.$http({
					url: this.$http.adornUrl('sys/dict/list'),
					method: 'get',
					params: this.$http.adornParams({ type: type })
				}).then(({data}) => {
					if (data && data.code === 0) {
						// 转换数据格式以适配现有的 sys_dict 表结构
						const options = data.data.map(item => ({
							dictValue: item.code,
							dictLabel: item.value || item.name
						}))

						switch(type) {
							case 'vaccine_type':
								this.vaccineTypeOptions = options
								break
							case 'vaccination_reaction':
								this.vaccinationReactionOptions = options
								break
							case 'treatment_plan_type':
								this.treatmentPlanTypeOptions = options
								break
							case 'treatment_plan_status':
								this.treatmentPlanStatusOptions = options
								break
							case 'patient_risk_level':
								this.riskLevelOptions = options
								break
						}
					}
				})
			})
		},

		// 工具方法
		getDictText(options, value) {
			const item = options.find(opt => opt.dictValue == value)
			return item ? item.dictLabel : value
		},

		getVaccineTypeText(type) {
			return this.getDictText(this.vaccineTypeOptions, type)
		},

		getVaccinationReactionText(reaction) {
			return this.getDictText(this.vaccinationReactionOptions, reaction)
		},

		getVaccinationReactionType(reaction) {
			switch(reaction) {
				case '0': return 'success'
				case '1': return 'info'
				case '2': return 'warning'
				case '3': return 'danger'
				default: return 'info'
			}
		},

		getTreatmentPlanTypeText(type) {
			return this.getDictText(this.treatmentPlanTypeOptions, type)
		},

		getTreatmentPlanStatusText(status) {
			return this.getDictText(this.treatmentPlanStatusOptions, status)
		},

		getTreatmentPlanStatusType(status) {
			switch(status) {
				case '1': return 'info'
				case '2': return 'primary'
				case '3': return 'success'
				case '4': return 'warning'
				case '5': return 'danger'
				default: return 'info'
			}
		},

		getRiskLevelText(level) {
			return this.getDictText(this.riskLevelOptions, level)
		},

		getRiskLevelType(level) {
			switch(level) {
				case '1': return 'success'
				case '2': return 'warning'
				case '3': return 'danger'
				default: return 'info'
			}
		},

		getCompletenessColor(percentage) {
			if (percentage >= 80) return '#67c23a'
			if (percentage >= 60) return '#e6a23c'
			return '#f56c6c'
		},

		// ==================== 疫苗接种管理 ====================
		// 加载疫苗接种数据
		loadVaccinationData() {
			this.vaccinationLoading = true
			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getVaccinationList'),
				method: 'get',
				params: this.$http.adornParams({
					page: this.vaccinationPage.page,
					limit: this.vaccinationPage.limit,
					patientName: this.vaccinationSearch.patientName,
					vaccineType: this.vaccinationSearch.vaccineType,
					reaction: this.vaccinationSearch.reaction
				})
			}).then(({data}) => {
				this.vaccinationLoading = false
				if (data && data.code === 0) {
					this.vaccinationData = data.data
				}
			}).catch(() => {
				this.vaccinationLoading = false
			})
		},

		// 搜索疫苗接种
		searchVaccination() {
			this.vaccinationPage.page = 1
			this.loadVaccinationData()
		},

		// 重置疫苗接种搜索
		resetVaccination() {
			this.vaccinationSearch = {
				patientName: '',
				vaccineType: '',
				reaction: ''
			}
			this.vaccinationPage.page = 1
			this.loadVaccinationData()
		},

		// 疫苗接种分页
		handleVaccinationSizeChange(val) {
			this.vaccinationPage.limit = val
			this.loadVaccinationData()
		},

		handleVaccinationCurrentChange(val) {
			this.vaccinationPage.page = val
			this.loadVaccinationData()
		},

		// 添加疫苗接种
		addVaccination() {
			this.$router.push({
				path: '/vaccinationForm',
				query: { action: 'add' }
			})
		},

		// 编辑疫苗接种
		editVaccination(row) {
			this.$router.push({
				path: '/vaccinationForm',
				query: {
					action: 'edit',
					vaccinationId: row.vaccinationId
				}
			})
		},

		// 删除疫苗接种
		deleteVaccination(row) {
			this.$confirm(`确定删除该疫苗接种记录?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/deleteVaccination'),
					method: 'post',
					data: this.$http.adornData({
						vaccinationId: row.vaccinationId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadVaccinationData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// ==================== 治疗方案管理 ====================
		// 加载治疗方案数据
		loadTreatmentData() {
			this.treatmentLoading = true
			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getTreatmentPlanList'),
				method: 'get',
				params: this.$http.adornParams({
					page: this.treatmentPage.page,
					limit: this.treatmentPage.limit,
					patientName: this.treatmentSearch.patientName,
					planType: this.treatmentSearch.planType,
					planStatus: this.treatmentSearch.planStatus
				})
			}).then(({data}) => {
				this.treatmentLoading = false
				if (data && data.code === 0) {
					this.treatmentData = data.data
				}
			}).catch(() => {
				this.treatmentLoading = false
			})
		},

		// 搜索治疗方案
		searchTreatment() {
			this.treatmentPage.page = 1
			this.loadTreatmentData()
		},

		// 重置治疗方案搜索
		resetTreatment() {
			this.treatmentSearch = {
				patientName: '',
				planType: '',
				planStatus: ''
			}
			this.treatmentPage.page = 1
			this.loadTreatmentData()
		},

		// 治疗方案分页
		handleTreatmentSizeChange(val) {
			this.treatmentPage.limit = val
			this.loadTreatmentData()
		},

		handleTreatmentCurrentChange(val) {
			this.treatmentPage.page = val
			this.loadTreatmentData()
		},

		// 添加治疗方案
		addTreatment() {
			this.$router.push({
				path: '/treatmentPlanForm',
				query: { action: 'add' }
			})
		},

		// 编辑治疗方案
		editTreatment(row) {
			this.$router.push({
				path: '/treatmentPlanForm',
				query: {
					action: 'edit',
					planId: row.planId
				}
			})
		},

		// 更新治疗方案状态
		updateTreatmentStatus(row) {
			this.$prompt('请选择新的状态', '更新治疗方案状态', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputType: 'select',
				inputOptions: this.treatmentPlanStatusOptions.map(item => ({
					value: item.dictValue,
					label: item.dictLabel
				}))
			}).then(({ value }) => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/updateTreatmentPlanStatus'),
					method: 'post',
					data: this.$http.adornData({
						planId: row.planId,
						status: value
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('状态更新成功')
						this.loadTreatmentData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// 删除治疗方案
		deleteTreatment(row) {
			this.$confirm(`确定删除该治疗方案?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/deleteTreatmentPlan'),
					method: 'post',
					data: this.$http.adornData({
						planId: row.planId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadTreatmentData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// ==================== 医疗数据统计 ====================
		// 搜索统计数据
		searchStatistics() {
			if (!this.statisticsSearch.patientName.trim()) {
				this.$message.warning('请输入患者姓名')
				return
			}

			this.$http({
				url: this.$http.adornUrl('admin/patientMedical/getMedicalDataStatistics'),
				method: 'get',
				params: this.$http.adornParams({
					patientName: this.statisticsSearch.patientName
				})
			}).then(({data}) => {
				if (data && data.code === 0) {
					this.statisticsData = data.data
				} else {
					this.$message.error(data.msg || '查询失败')
					this.statisticsData = {}
				}
			}).catch(() => {
				this.$message.error('查询失败')
				this.statisticsData = {}
			})
		},

		// 生成完整报告
		generateReport() {
			if (!this.statisticsData.patientInfo) {
				this.$message.warning('请先查询患者统计数据')
				return
			}

			this.$prompt('请选择报告类型', '生成医疗报告', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputType: 'select',
				inputOptions: [
					{ value: 'complete', label: '完整报告' },
					{ value: 'summary', label: '摘要报告' },
					{ value: 'medication', label: '用药报告' }
				]
			}).then(({ value }) => {
				this.$http({
					url: this.$http.adornUrl('admin/patientMedical/generateMedicalReport'),
					method: 'post',
					data: this.$http.adornData({
						patientId: this.statisticsData.patientInfo.patientId,
						reportType: value
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('报告生成成功')
						// 可以在这里处理报告数据，比如打开新窗口显示或下载
						this.showReport(data.data)
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// 显示报告
		showReport(reportData) {
			// 这里可以实现报告显示逻辑
			// 比如打开新的对话框或页面显示报告内容
			this.$alert('报告已生成，可在报告管理中查看', '提示', {
				confirmButtonText: '确定'
			})
		},

		// 导出报告
		exportReport() {
			if (!this.statisticsData.patientInfo) {
				this.$message.warning('请先查询患者统计数据')
				return
			}

			this.$confirm('确定导出该患者的医疗数据报告?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'info'
			}).then(() => {
				window.open(this.$http.adornUrl('admin/patientMedical/exportMedicalReport?' +
					this.$http.adornParams({
						patientId: this.statisticsData.patientInfo.patientId,
						patientName: this.statisticsData.patientInfo.realName
					}, true)))
			}).catch(() => {})
		}
	},
	mounted() {
		this.loadDictData()
		this.loadVaccinationData()
	}
}
</script>

<style scoped>
.el-table {
	margin-top: 20px;
}

.statistic-item {
	text-align: center;
	padding: 20px;
	border: 1px solid #ebeef5;
	border-radius: 4px;
}

.statistic-title {
	font-size: 14px;
	color: #909399;
	margin-bottom: 10px;
}

.statistic-number {
	font-size: 24px;
	font-weight: bold;
	color: #409EFF;
}

.statistic-status {
	font-size: 16px;
	color: #67c23a;
}

.box-card {
	margin-top: 20px;
}
</style>
