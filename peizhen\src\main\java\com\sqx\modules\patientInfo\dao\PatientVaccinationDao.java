package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.patientInfo.entity.PatientVaccination;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 患者疫苗接种记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Mapper
public interface PatientVaccinationDao extends BaseMapper<PatientVaccination> {

    /**
     * 分页查询患者疫苗接种记录
     */
    IPage<PatientVaccination> getVaccinationList(@Param("pages") Page<PatientVaccination> pages, 
                                                @Param("patientId") Long patientId, 
                                                @Param("vaccineType") Integer vaccineType);

    /**
     * 获取患者疫苗接种记录
     */
    List<PatientVaccination> getByPatient(@Param("patientId") Long patientId);

    /**
     * 获取未完成接种的疫苗
     */
    List<PatientVaccination> getIncompleteVaccinations(@Param("patientId") Long patientId);

    /**
     * 根据疫苗名称搜索接种记录
     */
    List<PatientVaccination> searchByVaccineName(@Param("patientId") Long patientId, 
                                                @Param("vaccineName") String vaccineName);

    /**
     * 获取需要复种的疫苗
     */
    List<PatientVaccination> getVaccinesNeedingBooster(@Param("patientId") Long patientId);
}
