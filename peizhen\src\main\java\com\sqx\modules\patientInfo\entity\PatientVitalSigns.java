package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者生命体征记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_vital_signs")
public class PatientVitalSigns implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 生命体征记录id
     */
    @TableId(value = "vital_id", type = IdType.AUTO)
    @ApiModelProperty("生命体征记录id")
    private Long vitalId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 测量时间
     */
    @ApiModelProperty("测量时间")
    private String measurementDate;

    /**
     * 收缩压(mmHg)
     */
    @ApiModelProperty("收缩压(mmHg)")
    private Integer systolicPressure;

    /**
     * 舒张压(mmHg)
     */
    @ApiModelProperty("舒张压(mmHg)")
    private Integer diastolicPressure;

    /**
     * 心率(次/分)
     */
    @ApiModelProperty("心率(次/分)")
    private Integer heartRate;

    /**
     * 体温(℃)
     */
    @ApiModelProperty("体温(℃)")
    private BigDecimal temperature;

    /**
     * 呼吸频率(次/分)
     */
    @ApiModelProperty("呼吸频率(次/分)")
    private Integer respiratoryRate;

    /**
     * 血氧饱和度(%)
     */
    @ApiModelProperty("血氧饱和度(%)")
    private BigDecimal oxygenSaturation;

    /**
     * 体重(kg)
     */
    @ApiModelProperty("体重(kg)")
    private BigDecimal weight;

    /**
     * 身高(cm)
     */
    @ApiModelProperty("身高(cm)")
    private BigDecimal height;

    /**
     * BMI指数
     */
    @ApiModelProperty("BMI指数")
    private BigDecimal bmi;

    /**
     * 腰围(cm)
     */
    @ApiModelProperty("腰围(cm)")
    private BigDecimal waistCircumference;

    /**
     * 血糖(mmol/L)
     */
    @ApiModelProperty("血糖(mmol/L)")
    private BigDecimal bloodGlucose;

    /**
     * 血糖测量时机(1空腹 2餐后2小时 3随机 4其他)
     */
    @ApiModelProperty("血糖测量时机(1空腹 2餐后2小时 3随机 4其他)")
    private Integer glucoseMeasurementTiming;

    /**
     * 疼痛评分(0-10分)
     */
    @ApiModelProperty("疼痛评分(0-10分)")
    private Integer painScore;

    /**
     * 意识状态(1清醒 2嗜睡 3昏迷 4其他)
     */
    @ApiModelProperty("意识状态(1清醒 2嗜睡 3昏迷 4其他)")
    private Integer consciousnessLevel;

    /**
     * 测量环境(1医院 2家庭 3体检中心 4其他)
     */
    @ApiModelProperty("测量环境(1医院 2家庭 3体检中心 4其他)")
    private Integer measurementEnvironment;

    /**
     * 测量设备
     */
    @ApiModelProperty("测量设备")
    private String measurementDevice;

    /**
     * 测量人员
     */
    @ApiModelProperty("测量人员")
    private String measuredBy;

    /**
     * 异常指标标记
     */
    @ApiModelProperty("异常指标标记")
    private String abnormalIndicators;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String notes;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
