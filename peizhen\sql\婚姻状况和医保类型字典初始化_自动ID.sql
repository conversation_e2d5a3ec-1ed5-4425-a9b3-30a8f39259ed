-- 婚姻状况和医保类型字典初始化脚本（自动ID版本）
-- 让数据库自动分配ID，更安全

-- 清理已存在的数据
DELETE FROM sys_dict WHERE type IN ('marital_status', 'insurance_type');

-- 1. 婚姻状况字典数据
-- 插入父级记录
INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
VALUES ('婚姻状况', 'marital_status', NULL, NULL, 1, '患者婚姻状况', 0);

-- 插入子级记录（使用子查询获取父级ID）
INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '未婚', 'marital_status', '1', '未婚', 1, '未婚', id 
FROM sys_dict WHERE type = 'marital_status' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '已婚', 'marital_status', '2', '已婚', 2, '已婚', id 
FROM sys_dict WHERE type = 'marital_status' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '离异', 'marital_status', '3', '离异', 3, '离异', id 
FROM sys_dict WHERE type = 'marital_status' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '丧偶', 'marital_status', '4', '丧偶', 4, '丧偶', id 
FROM sys_dict WHERE type = 'marital_status' AND parent_id = 0;

-- 2. 医保类型字典数据
-- 插入父级记录
INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
VALUES ('医保类型', 'insurance_type', NULL, NULL, 1, '医疗保险类型', 0);

-- 插入子级记录（使用子查询获取父级ID）
INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '城镇职工', 'insurance_type', '1', '城镇职工', 1, '城镇职工', id 
FROM sys_dict WHERE type = 'insurance_type' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '城镇居民', 'insurance_type', '2', '城镇居民', 2, '城镇居民', id 
FROM sys_dict WHERE type = 'insurance_type' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '新农合', 'insurance_type', '3', '新农合', 3, '新农合', id 
FROM sys_dict WHERE type = 'insurance_type' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '商业保险', 'insurance_type', '4', '商业保险', 4, '商业保险', id 
FROM sys_dict WHERE type = 'insurance_type' AND parent_id = 0;

INSERT INTO sys_dict (name, type, code, value, order_num, remark, parent_id) 
SELECT '自费', 'insurance_type', '5', '自费', 5, '自费', id 
FROM sys_dict WHERE type = 'insurance_type' AND parent_id = 0;

-- 验证插入结果
SELECT '婚姻状况字典' as '字典类型', id, name, type, code, value, order_num, parent_id 
FROM sys_dict 
WHERE type = 'marital_status' 
ORDER BY parent_id, order_num;

SELECT '医保类型字典' as '字典类型', id, name, type, code, value, order_num, parent_id 
FROM sys_dict 
WHERE type = 'insurance_type' 
ORDER BY parent_id, order_num;

-- 统计结果
SELECT 
    type as '字典类型',
    COUNT(*) as '记录数量',
    SUM(CASE WHEN parent_id = 0 THEN 1 ELSE 0 END) as '父级记录',
    SUM(CASE WHEN parent_id > 0 THEN 1 ELSE 0 END) as '子级记录'
FROM sys_dict 
WHERE type IN ('marital_status', 'insurance_type')
GROUP BY type;
