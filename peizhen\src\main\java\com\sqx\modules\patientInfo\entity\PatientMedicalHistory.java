package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者病史记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_medical_history")
public class PatientMedicalHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病史记录id
     */
    @TableId(value = "history_id", type = IdType.AUTO)
    @ApiModelProperty("病史记录id")
    private Long historyId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 病史类型(1既往病史 2手术史 3外伤史 4输血史 5家族史)
     */
    @ApiModelProperty("病史类型(1既往病史 2手术史 3外伤史 4输血史 5家族史)")
    private Integer historyType;

    /**
     * 疾病/手术名称
     */
    @ApiModelProperty("疾病/手术名称")
    @TableField(condition = SqlCondition.LIKE)
    private String diseaseName;

    /**
     * 疾病编码(ICD-10)
     */
    @ApiModelProperty("疾病编码(ICD-10)")
    private String diseaseCode;

    /**
     * 诊断时间/手术时间
     */
    @ApiModelProperty("诊断时间/手术时间")
    private String diagnosisDate;

    /**
     * 治疗医院
     */
    @ApiModelProperty("治疗医院")
    private String hospitalName;

    /**
     * 主治医生
     */
    @ApiModelProperty("主治医生")
    private String doctorName;

    /**
     * 治疗结果(1治愈 2好转 3未愈 4死亡 5未知)
     */
    @ApiModelProperty("治疗结果(1治愈 2好转 3未愈 4死亡 5未知)")
    private Integer treatmentResult;

    /**
     * 病情严重程度(1轻度 2中度 3重度)
     */
    @ApiModelProperty("病情严重程度(1轻度 2中度 3重度)")
    private Integer severity;

    /**
     * 是否遗传性疾病(0否 1是)
     */
    @ApiModelProperty("是否遗传性疾病(0否 1是)")
    private Integer isHereditary;

    /**
     * 家族关系(父亲、母亲、兄弟、姐妹等)
     */
    @ApiModelProperty("家族关系(父亲、母亲、兄弟、姐妹等)")
    private String familyRelation;

    /**
     * 详细描述
     */
    @ApiModelProperty("详细描述")
    private String description;

    /**
     * 相关文件附件路径
     */
    @ApiModelProperty("相关文件附件路径")
    private String attachmentPath;

    /**
     * 是否当前活跃病史(0否 1是)
     */
    @ApiModelProperty("是否当前活跃病史(0否 1是)")
    private Integer isActive;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
