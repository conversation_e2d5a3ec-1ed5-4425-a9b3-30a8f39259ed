package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者健康状态表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_health_status")
public class PatientHealthStatus implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 健康状态id
     */
    @TableId(value = "status_id", type = IdType.AUTO)
    @ApiModelProperty("健康状态id")
    private Long statusId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 整体健康状态(1优秀 2良好 3一般 4较差 5很差)
     */
    @ApiModelProperty("整体健康状态(1优秀 2良好 3一般 4较差 5很差)")
    private Integer overallHealth;

    /**
     * 身高(cm)
     */
    @ApiModelProperty("身高(cm)")
    private BigDecimal height;

    /**
     * 体重(kg)
     */
    @ApiModelProperty("体重(kg)")
    private BigDecimal weight;

    /**
     * BMI指数
     */
    @ApiModelProperty("BMI指数")
    private BigDecimal bmi;

    /**
     * 血型(A、B、AB、O)
     */
    @ApiModelProperty("血型(A、B、AB、O)")
    private String bloodType;

    /**
     * RH血型(0阴性 1阳性)
     */
    @ApiModelProperty("RH血型(0阴性 1阳性)")
    private Integer rhType;

    /**
     * 吸烟状况(0从不吸烟 1已戒烟 2偶尔吸烟 3经常吸烟)
     */
    @ApiModelProperty("吸烟状况(0从不吸烟 1已戒烟 2偶尔吸烟 3经常吸烟)")
    private Integer smokingStatus;

    /**
     * 饮酒状况(0从不饮酒 1已戒酒 2偶尔饮酒 3经常饮酒)
     */
    @ApiModelProperty("饮酒状况(0从不饮酒 1已戒酒 2偶尔饮酒 3经常饮酒)")
    private Integer drinkingStatus;

    /**
     * 运动频率(0从不运动 1偶尔运动 2每周1-2次 3每周3-4次 4每天运动)
     */
    @ApiModelProperty("运动频率(0从不运动 1偶尔运动 2每周1-2次 3每周3-4次 4每天运动)")
    private Integer exerciseFrequency;

    /**
     * 睡眠质量(1很好 2较好 3一般 4较差 5很差)
     */
    @ApiModelProperty("睡眠质量(1很好 2较好 3一般 4较差 5很差)")
    private Integer sleepQuality;

    /**
     * 精神状态(1很好 2较好 3一般 4较差 5很差)
     */
    @ApiModelProperty("精神状态(1很好 2较好 3一般 4较差 5很差)")
    private Integer mentalStatus;

    /**
     * 活动能力(1完全自理 2基本自理 3部分依赖 4完全依赖)
     */
    @ApiModelProperty("活动能力(1完全自理 2基本自理 3部分依赖 4完全依赖)")
    private Integer mobilityStatus;

    /**
     * 慢性疾病列表(逗号分隔)
     */
    @ApiModelProperty("慢性疾病列表(逗号分隔)")
    private String chronicDiseases;

    /**
     * 风险评估等级(1低风险 2中风险 3高风险)
     */
    @ApiModelProperty("风险评估等级(1低风险 2中风险 3高风险)")
    private Integer riskLevel;

    /**
     * 特殊注意事项
     */
    @ApiModelProperty("特殊注意事项")
    private String specialNotes;

    /**
     * 最后更新时间
     */
    @ApiModelProperty("最后更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
