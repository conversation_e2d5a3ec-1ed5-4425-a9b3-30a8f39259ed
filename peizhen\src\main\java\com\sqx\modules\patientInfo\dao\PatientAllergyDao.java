package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.patientInfo.entity.PatientAllergy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 患者过敏信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Mapper
public interface PatientAllergyDao extends BaseMapper<PatientAllergy> {

    /**
     * 分页查询患者过敏记录
     */
    IPage<PatientAllergy> getAllergyList(@Param("pages") Page<PatientAllergy> pages, 
                                        @Param("patientId") Long patientId, 
                                        @Param("allergyType") Integer allergyType);

    /**
     * 获取患者活跃过敏记录
     */
    List<PatientAllergy> getActiveAllergiesByPatient(@Param("patientId") Long patientId);

    /**
     * 获取患者药物过敏记录
     */
    List<PatientAllergy> getDrugAllergiesByPatient(@Param("patientId") Long patientId);

    /**
     * 根据过敏原名称搜索
     */
    List<PatientAllergy> searchByAllergenName(@Param("patientId") Long patientId, 
                                            @Param("allergenName") String allergenName);

    /**
     * 检查是否存在特定过敏原
     */
    int checkAllergenExists(@Param("patientId") Long patientId, 
                           @Param("allergenName") String allergenName, 
                           @Param("allergyType") Integer allergyType);
}
