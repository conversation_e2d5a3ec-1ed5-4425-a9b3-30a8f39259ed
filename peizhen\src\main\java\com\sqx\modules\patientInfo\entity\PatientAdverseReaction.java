package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者不良反应记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_adverse_reaction")
public class PatientAdverseReaction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 不良反应记录id
     */
    @TableId(value = "reaction_id", type = IdType.AUTO)
    @ApiModelProperty("不良反应记录id")
    private Long reactionId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 反应类型(1药物不良反应 2疫苗不良反应 3医疗器械不良反应 4其他)
     */
    @ApiModelProperty("反应类型(1药物不良反应 2疫苗不良反应 3医疗器械不良反应 4其他)")
    private Integer reactionType;

    /**
     * 引起反应的物质/药物名称
     */
    @ApiModelProperty("引起反应的物质/药物名称")
    @TableField(condition = SqlCondition.LIKE)
    private String causativeAgent;

    /**
     * 物质/药物编码
     */
    @ApiModelProperty("物质/药物编码")
    private String agentCode;

    /**
     * 用药剂量
     */
    @ApiModelProperty("用药剂量")
    private String dosage;

    /**
     * 用药途径(1口服 2注射 3外用 4吸入 5其他)
     */
    @ApiModelProperty("用药途径(1口服 2注射 3外用 4吸入 5其他)")
    private Integer administrationRoute;

    /**
     * 反应发生时间
     */
    @ApiModelProperty("反应发生时间")
    private String reactionDate;

    /**
     * 用药到反应发生的时间间隔(小时)
     */
    @ApiModelProperty("用药到反应发生的时间间隔(小时)")
    private Integer timeToReaction;

    /**
     * 反应严重程度(1轻微 2中度 3严重 4危及生命)
     */
    @ApiModelProperty("反应严重程度(1轻微 2中度 3严重 4危及生命)")
    private Integer severity;

    /**
     * 反应症状描述
     */
    @ApiModelProperty("反应症状描述")
    private String symptoms;

    /**
     * 反应持续时间(小时)
     */
    @ApiModelProperty("反应持续时间(小时)")
    private Integer duration;

    /**
     * 处理措施
     */
    @ApiModelProperty("处理措施")
    private String treatmentMeasures;

    /**
     * 处理结果(1完全恢复 2部分恢复 3无改善 4恶化 5死亡)
     */
    @ApiModelProperty("处理结果(1完全恢复 2部分恢复 3无改善 4恶化 5死亡)")
    private Integer treatmentOutcome;

    /**
     * 因果关系评估(1肯定 2很可能 3可能 4不太可能 5不相关)
     */
    @ApiModelProperty("因果关系评估(1肯定 2很可能 3可能 4不太可能 5不相关)")
    private Integer causalityAssessment;

    /**
     * 是否停药(0否 1是)
     */
    @ApiModelProperty("是否停药(0否 1是)")
    private Integer drugDiscontinued;

    /**
     * 是否再次用药(0否 1是)
     */
    @ApiModelProperty("是否再次用药(0否 1是)")
    private Integer rechallenge;

    /**
     * 再次用药结果(1无反应 2相同反应 3不同反应)
     */
    @ApiModelProperty("再次用药结果(1无反应 2相同反应 3不同反应)")
    private Integer rechallengeResult;

    /**
     * 报告医院
     */
    @ApiModelProperty("报告医院")
    private String reportingHospital;

    /**
     * 报告医生
     */
    @ApiModelProperty("报告医生")
    private String reportingDoctor;

    /**
     * 是否已上报药监部门(0否 1是)
     */
    @ApiModelProperty("是否已上报药监部门(0否 1是)")
    private Integer reportedToAuthority;

    /**
     * 上报编号
     */
    @ApiModelProperty("上报编号")
    private String reportNumber;

    /**
     * 相关检查报告路径
     */
    @ApiModelProperty("相关检查报告路径")
    private String reportPath;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String notes;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
