package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者疫苗接种记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_vaccination")
public class PatientVaccination implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 疫苗接种记录id
     */
    @TableId(value = "vaccination_id", type = IdType.AUTO)
    @ApiModelProperty("疫苗接种记录id")
    private Long vaccinationId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 疫苗名称
     */
    @ApiModelProperty("疫苗名称")
    @TableField(condition = SqlCondition.LIKE)
    private String vaccineName;

    /**
     * 疫苗编码
     */
    @ApiModelProperty("疫苗编码")
    private String vaccineCode;

    /**
     * 疫苗类型(1常规疫苗 2应急疫苗 3旅行疫苗 4职业疫苗)
     */
    @ApiModelProperty("疫苗类型(1常规疫苗 2应急疫苗 3旅行疫苗 4职业疫苗)")
    private Integer vaccineType;

    /**
     * 疫苗厂家
     */
    @ApiModelProperty("疫苗厂家")
    private String manufacturer;

    /**
     * 疫苗批号
     */
    @ApiModelProperty("疫苗批号")
    private String batchNumber;

    /**
     * 接种日期
     */
    @ApiModelProperty("接种日期")
    private String vaccinationDate;

    /**
     * 接种剂次(第几针)
     */
    @ApiModelProperty("接种剂次(第几针)")
    private Integer doseNumber;

    /**
     * 总剂次数
     */
    @ApiModelProperty("总剂次数")
    private Integer totalDoses;

    /**
     * 接种部位(1左上臂 2右上臂 3左大腿 4右大腿 5其他)
     */
    @ApiModelProperty("接种部位(1左上臂 2右上臂 3左大腿 4右大腿 5其他)")
    private Integer injectionSite;

    /**
     * 接种途径(1肌肉注射 2皮下注射 3口服 4鼻喷 5其他)
     */
    @ApiModelProperty("接种途径(1肌肉注射 2皮下注射 3口服 4鼻喷 5其他)")
    private Integer administrationRoute;

    /**
     * 接种剂量(ml)
     */
    @ApiModelProperty("接种剂量(ml)")
    private String dosage;

    /**
     * 接种机构
     */
    @ApiModelProperty("接种机构")
    private String vaccinationSite;

    /**
     * 接种医生
     */
    @ApiModelProperty("接种医生")
    private String vaccinatingDoctor;

    /**
     * 接种护士
     */
    @ApiModelProperty("接种护士")
    private String vaccinatingNurse;

    /**
     * 下次接种日期
     */
    @ApiModelProperty("下次接种日期")
    private String nextVaccinationDate;

    /**
     * 接种反应(0无反应 1轻微反应 2中度反应 3严重反应)
     */
    @ApiModelProperty("接种反应(0无反应 1轻微反应 2中度反应 3严重反应)")
    private Integer reaction;

    /**
     * 反应描述
     */
    @ApiModelProperty("反应描述")
    private String reactionDescription;

    /**
     * 反应处理
     */
    @ApiModelProperty("反应处理")
    private String reactionTreatment;

    /**
     * 禁忌症
     */
    @ApiModelProperty("禁忌症")
    private String contraindications;

    /**
     * 接种原因
     */
    @ApiModelProperty("接种原因")
    private String vaccinationReason;

    /**
     * 疫苗有效期
     */
    @ApiModelProperty("疫苗有效期")
    private String vaccineExpiryDate;

    /**
     * 接种证书编号
     */
    @ApiModelProperty("接种证书编号")
    private String certificateNumber;

    /**
     * 接种记录文件路径
     */
    @ApiModelProperty("接种记录文件路径")
    private String recordFilePath;

    /**
     * 是否完成全程接种(0否 1是)
     */
    @ApiModelProperty("是否完成全程接种(0否 1是)")
    private Integer isCompleted;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String notes;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
