-- ========================================
-- 患者医疗档案增强功能 - 数据验证和维护脚本
-- 创建时间: 2024-08-05
-- 描述: 验证数据完整性、创建触发器和存储过程
-- ========================================

-- 1. 数据完整性检查
-- 检查患者基础信息完整性
SELECT 
    COUNT(*) as total_patients,
    COUNT(real_name) as has_name,
    COUNT(phone) as has_phone,
    COUNT(id_number) as has_id_number,
    ROUND(COUNT(real_name) * 100.0 / COUNT(*), 2) as name_completeness,
    ROUND(COUNT(phone) * 100.0 / COUNT(*), 2) as phone_completeness,
    ROUND(COUNT(id_number) * 100.0 / COUNT(*), 2) as id_completeness
FROM patient_info 
WHERE is_delete = 0;

-- 2. 创建触发器 - 自动更新档案完整度
DELIMITER $$

CREATE TRIGGER `tr_update_profile_completeness_after_health_insert`
AFTER INSERT ON `patient_health_status`
FOR EACH ROW
BEGIN
    UPDATE patient_info 
    SET last_medical_update_time = NOW()
    WHERE patient_id = NEW.patient_id;
END$$

CREATE TRIGGER `tr_update_profile_completeness_after_health_update`
AFTER UPDATE ON `patient_health_status`
FOR EACH ROW
BEGIN
    UPDATE patient_info 
    SET last_medical_update_time = NOW()
    WHERE patient_id = NEW.patient_id;
END$$

CREATE TRIGGER `tr_update_profile_completeness_after_history_insert`
AFTER INSERT ON `patient_medical_history`
FOR EACH ROW
BEGIN
    UPDATE patient_info 
    SET last_medical_update_time = NOW()
    WHERE patient_id = NEW.patient_id;
END$$

CREATE TRIGGER `tr_update_profile_completeness_after_allergy_insert`
AFTER INSERT ON `patient_allergy`
FOR EACH ROW
BEGIN
    UPDATE patient_info 
    SET last_medical_update_time = NOW()
    WHERE patient_id = NEW.patient_id;
END$$

CREATE TRIGGER `tr_update_profile_completeness_after_medication_insert`
AFTER INSERT ON `patient_medication`
FOR EACH ROW
BEGIN
    UPDATE patient_info 
    SET last_medical_update_time = NOW()
    WHERE patient_id = NEW.patient_id;
END$$

DELIMITER ;

-- 3. 创建存储过程 - 批量更新档案完整度
DELIMITER $$

CREATE PROCEDURE `sp_update_all_profile_completeness`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE patient_id_var BIGINT;
    DECLARE completeness_score INT;
    
    DECLARE patient_cursor CURSOR FOR 
        SELECT patient_id FROM patient_info WHERE is_delete = 0;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN patient_cursor;
    
    patient_loop: LOOP
        FETCH patient_cursor INTO patient_id_var;
        IF done THEN
            LEAVE patient_loop;
        END IF;
        
        -- 计算完整度
        SET completeness_score = 0;
        
        -- 基础信息 (30%)
        SELECT 
            CASE 
                WHEN real_name IS NOT NULL AND real_name != '' THEN 10 ELSE 0 
            END +
            CASE 
                WHEN phone IS NOT NULL AND phone != '' THEN 10 ELSE 0 
            END +
            CASE 
                WHEN id_number IS NOT NULL AND id_number != '' THEN 10 ELSE 0 
            END
        INTO @basic_score
        FROM patient_info 
        WHERE patient_id = patient_id_var;
        
        SET completeness_score = completeness_score + @basic_score;
        
        -- 健康状态 (15%)
        IF EXISTS(SELECT 1 FROM patient_health_status WHERE patient_id = patient_id_var AND is_delete = 0) THEN
            SET completeness_score = completeness_score + 15;
        END IF;
        
        -- 病史记录 (15%)
        IF EXISTS(SELECT 1 FROM patient_medical_history WHERE patient_id = patient_id_var AND is_delete = 0) THEN
            SET completeness_score = completeness_score + 15;
        END IF;
        
        -- 过敏记录 (10%)
        IF EXISTS(SELECT 1 FROM patient_allergy WHERE patient_id = patient_id_var AND is_delete = 0) THEN
            SET completeness_score = completeness_score + 10;
        END IF;
        
        -- 用药记录 (10%)
        IF EXISTS(SELECT 1 FROM patient_medication WHERE patient_id = patient_id_var AND is_delete = 0) THEN
            SET completeness_score = completeness_score + 10;
        END IF;
        
        -- 生命体征 (10%)
        IF EXISTS(SELECT 1 FROM patient_vital_signs WHERE patient_id = patient_id_var AND is_delete = 0) THEN
            SET completeness_score = completeness_score + 10;
        END IF;
        
        -- 检查结果 (5%)
        IF EXISTS(SELECT 1 FROM patient_test_result WHERE patient_id = patient_id_var AND is_delete = 0) THEN
            SET completeness_score = completeness_score + 5;
        END IF;
        
        -- 疫苗接种 (5%)
        IF EXISTS(SELECT 1 FROM patient_vaccination WHERE patient_id = patient_id_var AND is_delete = 0) THEN
            SET completeness_score = completeness_score + 5;
        END IF;
        
        -- 更新完整度
        UPDATE patient_info 
        SET profile_completeness = completeness_score,
            last_medical_update_time = NOW()
        WHERE patient_id = patient_id_var;
        
    END LOOP;
    
    CLOSE patient_cursor;
    
    SELECT CONCAT('已更新 ', ROW_COUNT(), ' 个患者的档案完整度') AS result;
END$$

DELIMITER ;

-- 4. 创建存储过程 - 风险评估
DELIMITER $$

CREATE PROCEDURE `sp_assess_patient_risk`(IN p_patient_id BIGINT)
BEGIN
    DECLARE risk_score INT DEFAULT 0;
    DECLARE risk_level INT DEFAULT 1;
    
    -- 基于年龄的风险评分
    SELECT 
        CASE 
            WHEN is_under_age = 0 THEN 10  -- 成年人基础风险
            ELSE 5  -- 未成年人基础风险
        END
    INTO @age_risk
    FROM patient_info 
    WHERE patient_id = p_patient_id;
    
    SET risk_score = risk_score + IFNULL(@age_risk, 0);
    
    -- 基于病史的风险评分
    SELECT COUNT(*) * 5 INTO @history_risk
    FROM patient_medical_history 
    WHERE patient_id = p_patient_id AND is_active = 1 AND is_delete = 0;
    
    SET risk_score = risk_score + IFNULL(@history_risk, 0);
    
    -- 基于过敏的风险评分
    SELECT SUM(severity * 3) INTO @allergy_risk
    FROM patient_allergy 
    WHERE patient_id = p_patient_id AND is_active = 1 AND is_delete = 0;
    
    SET risk_score = risk_score + IFNULL(@allergy_risk, 0);
    
    -- 基于用药的风险评分
    SELECT COUNT(*) * 2 INTO @medication_risk
    FROM patient_medication 
    WHERE patient_id = p_patient_id AND medication_status = 1 AND is_delete = 0;
    
    SET risk_score = risk_score + IFNULL(@medication_risk, 0);
    
    -- 确定风险等级
    IF risk_score >= 70 THEN
        SET risk_level = 3; -- 高风险
    ELSEIF risk_score >= 40 THEN
        SET risk_level = 2; -- 中风险
    ELSE
        SET risk_level = 1; -- 低风险
    END IF;
    
    -- 更新患者风险等级
    UPDATE patient_info 
    SET risk_level = risk_level,
        last_medical_update_time = NOW()
    WHERE patient_id = p_patient_id;
    
    SELECT risk_score as calculated_risk_score, risk_level as assigned_risk_level;
END$$

DELIMITER ;

-- 5. 创建视图 - 患者医疗概览
CREATE OR REPLACE VIEW `v_patient_medical_overview` AS
SELECT 
    pi.patient_id,
    pi.real_name,
    pi.phone,
    pi.profile_completeness,
    pi.risk_level,
    pi.last_medical_update_time,
    phs.overall_health,
    phs.bmi,
    phs.blood_type,
    (SELECT COUNT(*) FROM patient_medical_history pmh WHERE pmh.patient_id = pi.patient_id AND pmh.is_active = 1 AND pmh.is_delete = 0) as active_conditions,
    (SELECT COUNT(*) FROM patient_allergy pa WHERE pa.patient_id = pi.patient_id AND pa.is_active = 1 AND pa.is_delete = 0) as active_allergies,
    (SELECT COUNT(*) FROM patient_medication pm WHERE pm.patient_id = pi.patient_id AND pm.medication_status = 1 AND pm.is_delete = 0) as current_medications,
    (SELECT measurement_date FROM patient_vital_signs pvs WHERE pvs.patient_id = pi.patient_id AND pvs.is_delete = 0 ORDER BY pvs.create_time DESC LIMIT 1) as last_vital_signs_date
FROM patient_info pi
LEFT JOIN patient_health_status phs ON pi.patient_id = phs.patient_id AND phs.is_delete = 0
WHERE pi.is_delete = 0;

-- 6. 数据清理和维护
-- 清理过期的软删除记录（可选，谨慎使用）
-- DELETE FROM patient_medical_history WHERE is_delete = 1 AND update_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);
-- DELETE FROM patient_allergy WHERE is_delete = 1 AND update_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);
-- DELETE FROM patient_medication WHERE is_delete = 1 AND update_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- 7. 性能监控查询
-- 查找需要优化的慢查询表
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS table_size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name LIKE 'patient_%'
ORDER BY table_size_mb DESC;

-- 验证脚本完成提示
SELECT '医疗档案系统验证和维护脚本执行完成！' AS validation_status;
