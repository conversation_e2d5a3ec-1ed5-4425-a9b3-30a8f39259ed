-- ========================================
-- 患者医疗档案增强功能数据库迁移脚本
-- 创建时间: 2024-08-04
-- 描述: 为PatientInfo添加全面的医疗信息管理功能
-- ========================================

-- 1. 首先备份现有的patient_info表结构
-- CREATE TABLE patient_info_backup AS SELECT * FROM patient_info;

-- 2. 为patient_info表添加医疗档案扩展字段
ALTER TABLE `patient_info` 
ADD COLUMN `birth_date` varchar(20) DEFAULT NULL COMMENT '出生日期',
ADD COLUMN `ethnicity` varchar(50) DEFAULT NULL COMMENT '民族',
ADD COLUMN `marital_status` int(1) DEFAULT NULL COMMENT '婚姻状况(1未婚 2已婚 3离异 4丧偶)',
ADD COLUMN `occupation` varchar(100) DEFAULT NULL COMMENT '职业',
ADD COLUMN `employer` varchar(200) DEFAULT NULL COMMENT '工作单位',
ADD COLUMN `current_address` varchar(500) DEFAULT NULL COMMENT '现住址',
ADD COLUMN `registered_address` varchar(500) DEFAULT NULL COMMENT '户籍地址',
ADD COLUMN `insurance_type` int(1) DEFAULT NULL COMMENT '医保类型(1城镇职工 2城镇居民 3新农合 4商业保险 5自费)',
ADD COLUMN `insurance_number` varchar(50) DEFAULT NULL COMMENT '医保卡号',
ADD COLUMN `primary_contact_name` varchar(100) DEFAULT NULL COMMENT '主要联系人姓名',
ADD COLUMN `primary_contact_relation` varchar(50) DEFAULT NULL COMMENT '主要联系人关系',
ADD COLUMN `primary_contact_phone` varchar(20) DEFAULT NULL COMMENT '主要联系人电话',
ADD COLUMN `secondary_contact_name` varchar(100) DEFAULT NULL COMMENT '次要联系人姓名',
ADD COLUMN `secondary_contact_relation` varchar(50) DEFAULT NULL COMMENT '次要联系人关系',
ADD COLUMN `secondary_contact_phone` varchar(20) DEFAULT NULL COMMENT '次要联系人电话',
ADD COLUMN `major_medical_history` text DEFAULT NULL COMMENT '既往重大疾病史摘要',
ADD COLUMN `drug_allergy_summary` text DEFAULT NULL COMMENT '药物过敏史摘要',
ADD COLUMN `current_medication_summary` text DEFAULT NULL COMMENT '当前主要用药摘要',
ADD COLUMN `special_medical_needs` text DEFAULT NULL COMMENT '特殊医疗需求',
ADD COLUMN `risk_level` int(1) DEFAULT 1 COMMENT '风险等级(1低风险 2中风险 3高风险)',
ADD COLUMN `profile_completeness` int(3) DEFAULT 0 COMMENT '医疗档案完整度(%)',
ADD COLUMN `last_medical_update_time` datetime DEFAULT NULL COMMENT '最后更新医疗信息时间',
ADD COLUMN `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 3. 创建患者健康状态表
DROP TABLE IF EXISTS `patient_health_status`;
CREATE TABLE `patient_health_status` (
    `status_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '健康状态id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `overall_health` int(1) DEFAULT NULL COMMENT '整体健康状态(1优秀 2良好 3一般 4较差 5很差)',
    `height` decimal(5,2) DEFAULT NULL COMMENT '身高(cm)',
    `weight` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
    `bmi` decimal(4,2) DEFAULT NULL COMMENT 'BMI指数',
    `blood_type` varchar(10) DEFAULT NULL COMMENT '血型(A、B、AB、O)',
    `rh_type` int(1) DEFAULT NULL COMMENT 'RH血型(0阴性 1阳性)',
    `smoking_status` int(1) DEFAULT 0 COMMENT '吸烟状况(0从不吸烟 1已戒烟 2偶尔吸烟 3经常吸烟)',
    `drinking_status` int(1) DEFAULT 0 COMMENT '饮酒状况(0从不饮酒 1已戒酒 2偶尔饮酒 3经常饮酒)',
    `exercise_frequency` int(1) DEFAULT 0 COMMENT '运动频率(0从不运动 1偶尔运动 2每周1-2次 3每周3-4次 4每天运动)',
    `sleep_quality` int(1) DEFAULT NULL COMMENT '睡眠质量(1很好 2较好 3一般 4较差 5很差)',
    `mental_status` int(1) DEFAULT NULL COMMENT '精神状态(1很好 2较好 3一般 4较差 5很差)',
    `mobility_status` int(1) DEFAULT 1 COMMENT '活动能力(1完全自理 2基本自理 3部分依赖 4完全依赖)',
    `chronic_diseases` text DEFAULT NULL COMMENT '慢性疾病列表(逗号分隔)',
    `risk_level` int(1) DEFAULT 1 COMMENT '风险评估等级(1低风险 2中风险 3高风险)',
    `special_notes` text DEFAULT NULL COMMENT '特殊注意事项',
    `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`status_id`),
    UNIQUE KEY `uk_patient_health` (`patient_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_risk_level` (`risk_level`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者健康状态表';

-- 4. 创建患者病史记录表
DROP TABLE IF EXISTS `patient_medical_history`;
CREATE TABLE `patient_medical_history` (
    `history_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '病史记录id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `history_type` int(1) NOT NULL COMMENT '病史类型(1既往病史 2手术史 3外伤史 4输血史 5家族史)',
    `disease_name` varchar(200) NOT NULL COMMENT '疾病/手术名称',
    `disease_code` varchar(50) DEFAULT NULL COMMENT '疾病编码(ICD-10)',
    `diagnosis_date` varchar(20) DEFAULT NULL COMMENT '诊断时间/手术时间',
    `hospital_name` varchar(200) DEFAULT NULL COMMENT '治疗医院',
    `doctor_name` varchar(100) DEFAULT NULL COMMENT '主治医生',
    `treatment_result` int(1) DEFAULT NULL COMMENT '治疗结果(1治愈 2好转 3未愈 4死亡 5未知)',
    `severity` int(1) DEFAULT NULL COMMENT '病情严重程度(1轻度 2中度 3重度)',
    `is_hereditary` int(1) DEFAULT 0 COMMENT '是否遗传性疾病(0否 1是)',
    `family_relation` varchar(50) DEFAULT NULL COMMENT '家族关系(父亲、母亲、兄弟、姐妹等)',
    `description` text DEFAULT NULL COMMENT '详细描述',
    `attachment_path` varchar(500) DEFAULT NULL COMMENT '相关文件附件路径',
    `is_active` int(1) DEFAULT 1 COMMENT '是否当前活跃病史(0否 1是)',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`history_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_history_type` (`history_type`),
    KEY `idx_disease_name` (`disease_name`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者病史记录表';

-- 5. 创建患者过敏信息表
DROP TABLE IF EXISTS `patient_allergy`;
CREATE TABLE `patient_allergy` (
    `allergy_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '过敏记录id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `allergy_type` int(1) NOT NULL COMMENT '过敏类型(1药物过敏 2食物过敏 3环境过敏 4接触性过敏 5其他)',
    `allergen_name` varchar(200) NOT NULL COMMENT '过敏原名称',
    `allergen_code` varchar(50) DEFAULT NULL COMMENT '过敏原编码(药物编码/食物编码等)',
    `severity` int(1) NOT NULL COMMENT '过敏严重程度(1轻微 2中度 3严重 4危及生命)',
    `symptoms` text DEFAULT NULL COMMENT '过敏反应症状',
    `first_discovery_date` varchar(20) DEFAULT NULL COMMENT '首次发现时间',
    `last_reaction_date` varchar(20) DEFAULT NULL COMMENT '最后一次反应时间',
    `reaction_duration` int(11) DEFAULT NULL COMMENT '反应持续时间(分钟)',
    `treatment_method` text DEFAULT NULL COMMENT '处理方式',
    `requires_emergency_treatment` int(1) DEFAULT 0 COMMENT '是否需要紧急处理(0否 1是)',
    `is_confirmed` int(1) DEFAULT 0 COMMENT '是否确认过敏(0疑似 1确认)',
    `confirmation_method` int(1) DEFAULT NULL COMMENT '确认方式(1临床观察 2皮肤试验 3血液检测 4激发试验)',
    `confirmation_hospital` varchar(200) DEFAULT NULL COMMENT '确认医院',
    `confirmation_doctor` varchar(100) DEFAULT NULL COMMENT '确认医生',
    `notes` text DEFAULT NULL COMMENT '备注说明',
    `report_path` varchar(500) DEFAULT NULL COMMENT '相关检测报告路径',
    `is_active` int(1) DEFAULT 1 COMMENT '是否当前活跃(0否 1是)',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`allergy_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_allergy_type` (`allergy_type`),
    KEY `idx_allergen_name` (`allergen_name`),
    KEY `idx_severity` (`severity`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者过敏信息表';

-- 6. 创建患者不良反应记录表
DROP TABLE IF EXISTS `patient_adverse_reaction`;
CREATE TABLE `patient_adverse_reaction` (
    `reaction_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '不良反应记录id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `reaction_type` int(1) NOT NULL COMMENT '反应类型(1药物不良反应 2疫苗不良反应 3医疗器械不良反应 4其他)',
    `causative_agent` varchar(200) NOT NULL COMMENT '引起反应的物质/药物名称',
    `agent_code` varchar(50) DEFAULT NULL COMMENT '物质/药物编码',
    `dosage` varchar(100) DEFAULT NULL COMMENT '用药剂量',
    `administration_route` int(1) DEFAULT NULL COMMENT '用药途径(1口服 2注射 3外用 4吸入 5其他)',
    `reaction_date` varchar(20) DEFAULT NULL COMMENT '反应发生时间',
    `time_to_reaction` int(11) DEFAULT NULL COMMENT '用药到反应发生的时间间隔(小时)',
    `severity` int(1) NOT NULL COMMENT '反应严重程度(1轻微 2中度 3严重 4危及生命)',
    `symptoms` text DEFAULT NULL COMMENT '反应症状描述',
    `duration` int(11) DEFAULT NULL COMMENT '反应持续时间(小时)',
    `treatment_measures` text DEFAULT NULL COMMENT '处理措施',
    `treatment_outcome` int(1) DEFAULT NULL COMMENT '处理结果(1完全恢复 2部分恢复 3无改善 4恶化 5死亡)',
    `causality_assessment` int(1) DEFAULT NULL COMMENT '因果关系评估(1肯定 2很可能 3可能 4不太可能 5不相关)',
    `drug_discontinued` int(1) DEFAULT NULL COMMENT '是否停药(0否 1是)',
    `rechallenge` int(1) DEFAULT NULL COMMENT '是否再次用药(0否 1是)',
    `rechallenge_result` int(1) DEFAULT NULL COMMENT '再次用药结果(1无反应 2相同反应 3不同反应)',
    `reporting_hospital` varchar(200) DEFAULT NULL COMMENT '报告医院',
    `reporting_doctor` varchar(100) DEFAULT NULL COMMENT '报告医生',
    `reported_to_authority` int(1) DEFAULT 0 COMMENT '是否已上报药监部门(0否 1是)',
    `report_number` varchar(100) DEFAULT NULL COMMENT '上报编号',
    `report_path` varchar(500) DEFAULT NULL COMMENT '相关检查报告路径',
    `notes` text DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`reaction_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_reaction_type` (`reaction_type`),
    KEY `idx_causative_agent` (`causative_agent`),
    KEY `idx_severity` (`severity`),
    KEY `idx_reaction_date` (`reaction_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者不良反应记录表';

-- 7. 创建患者用药记录表
DROP TABLE IF EXISTS `patient_medication`;
CREATE TABLE `patient_medication` (
    `medication_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用药记录id',
    `patient_id` bigint(20) NOT NULL COMMENT '患者id',
    `medication_name` varchar(200) NOT NULL COMMENT '药物名称(通用名)',
    `brand_name` varchar(200) DEFAULT NULL COMMENT '商品名',
    `medication_code` varchar(50) DEFAULT NULL COMMENT '药物编码',
    `medication_type` int(1) DEFAULT NULL COMMENT '药物分类(1处方药 2非处方药 3中药 4生物制品)',
    `dosage_form` int(1) DEFAULT NULL COMMENT '剂型(1片剂 2胶囊 3注射剂 4口服液 5外用药 6其他)',
    `strength` varchar(100) DEFAULT NULL COMMENT '规格强度',
    `single_dose` decimal(10,3) DEFAULT NULL COMMENT '单次剂量',
    `dose_unit` varchar(20) DEFAULT NULL COMMENT '剂量单位(mg、ml、片等)',
    `frequency` int(1) DEFAULT NULL COMMENT '用药频率(1每日一次 2每日两次 3每日三次 4每日四次 5按需服用 6其他)',
    `timing_of_administration` int(1) DEFAULT NULL COMMENT '用药时间(1餐前 2餐后 3餐中 4空腹 5睡前 6其他)',
    `route_of_administration` int(1) DEFAULT NULL COMMENT '用药途径(1口服 2注射 3外用 4吸入 5直肠给药 6其他)',
    `start_date` varchar(20) DEFAULT NULL COMMENT '开始用药时间',
    `end_date` varchar(20) DEFAULT NULL COMMENT '结束用药时间',
    `indication` text DEFAULT NULL COMMENT '用药目的/适应症',
    `prescribing_doctor` varchar(100) DEFAULT NULL COMMENT '处方医生',
    `prescribing_hospital` varchar(200) DEFAULT NULL COMMENT '处方医院',
    `prescription_date` varchar(20) DEFAULT NULL COMMENT '处方日期',
    `prescription_number` varchar(100) DEFAULT NULL COMMENT '处方编号',
    `pharmacy` varchar(200) DEFAULT NULL COMMENT '药房信息',
    `medication_status` int(1) DEFAULT 1 COMMENT '用药状态(1正在使用 2已停用 3暂停使用)',
    `discontinuation_reason` text DEFAULT NULL COMMENT '停药原因',
    `adherence` int(1) DEFAULT NULL COMMENT '依从性评估(1完全依从 2基本依从 3部分依从 4不依从)',
    `effectiveness` int(1) DEFAULT NULL COMMENT '疗效评估(1显效 2有效 3无效 4恶化)',
    `side_effects` text DEFAULT NULL COMMENT '副作用记录',
    `special_instructions` text DEFAULT NULL COMMENT '特殊注意事项',
    `prescription_image_path` varchar(500) DEFAULT NULL COMMENT '处方图片路径',
    `notes` text DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` int(1) DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
    `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`medication_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_medication_name` (`medication_name`),
    KEY `idx_medication_status` (`medication_status`),
    KEY `idx_start_date` (`start_date`),
    KEY `idx_prescribing_doctor` (`prescribing_doctor`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者用药记录表';
