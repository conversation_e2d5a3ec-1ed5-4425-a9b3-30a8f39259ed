<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<!-- 数据字典管理 -->
			<el-tab-pane label="医疗数据字典" name="dict">
				<div style="margin-bottom: 20px;">
					<div style="position: relative;display: inline-block;margin: 3px;">
						<span>字典类型：</span>
						<el-select style="width: 250px;" v-model="selectedDictType" placeholder="请选择字典类型" @change="loadDictData">
							<el-option v-for="item in dictTypes" :key="item.value" 
								:label="item.label" :value="item.value">
							</el-option>
						</el-select>&nbsp;&nbsp;
					</div>
					<el-button style="margin-left:15px;" size="mini" type="primary" icon="el-icon-refresh" @click="loadDictData">
						刷新
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="primary" icon="el-icon-plus" @click="addDictData()" 
						:disabled="!selectedDictType">
						添加字典项
					</el-button>
					<el-button style='margin-left:15px;' size="mini" type="success" icon="el-icon-download" @click="exportDictData()" 
						:disabled="!selectedDictType">
						导出配置
					</el-button>
				</div>
				
				<el-card v-if="selectedDictType" class="box-card">
					<div slot="header" class="clearfix">
						<span>{{ getDictTypeName(selectedDictType) }} - 配置项管理</span>
						<el-button style="float: right; padding: 3px 0" type="text" @click="batchEdit">批量编辑</el-button>
					</div>
					
					<el-table v-loading="dictLoading" :data="dictData" border>
						<el-table-column prop="dictSort" label="排序" width="80">
							<template slot-scope="scope">
								<el-input-number v-model="scope.row.dictSort" :min="1" :max="999" size="mini" 
									@change="updateDictSort(scope.row)"></el-input-number>
							</template>
						</el-table-column>
						<el-table-column prop="dictLabel" label="字典标签" width="150">
							<template slot-scope="scope">
								<el-input v-if="scope.row.editing" v-model="scope.row.dictLabel" size="mini" 
									@blur="saveDictItem(scope.row)" @keyup.enter.native="saveDictItem(scope.row)"></el-input>
								<span v-else @dblclick="editDictItem(scope.row)">{{ scope.row.dictLabel }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="dictValue" label="字典键值" width="120">
							<template slot-scope="scope">
								<el-input v-if="scope.row.editing" v-model="scope.row.dictValue" size="mini" 
									@blur="saveDictItem(scope.row)" @keyup.enter.native="saveDictItem(scope.row)"></el-input>
								<span v-else @dblclick="editDictItem(scope.row)">{{ scope.row.dictValue }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="cssClass" label="样式属性" width="120">
							<template slot-scope="scope">
								<el-input v-if="scope.row.editing" v-model="scope.row.cssClass" size="mini" 
									@blur="saveDictItem(scope.row)" @keyup.enter.native="saveDictItem(scope.row)"></el-input>
								<span v-else @dblclick="editDictItem(scope.row)">{{ scope.row.cssClass }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="listClass" label="表格回显样式" width="120">
							<template slot-scope="scope">
								<el-tag :type="scope.row.listClass" size="mini">{{ scope.row.listClass }}</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="isDefault" label="是否默认" width="100">
							<template slot-scope="scope">
								<el-switch v-model="scope.row.isDefault" active-value="Y" inactive-value="N" 
									@change="updateDictDefault(scope.row)"></el-switch>
							</template>
						</el-table-column>
						<el-table-column prop="status" label="状态" width="80">
							<template slot-scope="scope">
								<el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" 
									@change="updateDictStatus(scope.row)"></el-switch>
							</template>
						</el-table-column>
						<el-table-column prop="remark" label="备注" min-width="150">
							<template slot-scope="scope">
								<el-input v-if="scope.row.editing" v-model="scope.row.remark" size="mini" type="textarea" 
									@blur="saveDictItem(scope.row)"></el-input>
								<span v-else @dblclick="editDictItem(scope.row)">{{ scope.row.remark }}</span>
							</template>
						</el-table-column>
						<el-table-column label="操作" width="200" fixed="right">
							<template slot-scope="scope">
								<el-button v-if="!scope.row.editing" size="mini" type="primary" @click="editDictItem(scope.row)">编辑</el-button>
								<el-button v-if="scope.row.editing" size="mini" type="success" @click="saveDictItem(scope.row)">保存</el-button>
								<el-button v-if="scope.row.editing" size="mini" type="info" @click="cancelEdit(scope.row)">取消</el-button>
								<el-button v-if="!scope.row.editing" size="mini" type="danger" @click="deleteDictItem(scope.row)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-card>
				
				<el-empty v-else description="请选择要管理的字典类型"></el-empty>
			</el-tab-pane>
			
			<!-- 配置项预览 -->
			<el-tab-pane label="配置项预览" name="preview">
				<div style="margin-bottom: 20px;">
					<el-alert title="配置项预览" type="info" show-icon>
						<p>这里展示了所有医疗档案相关的配置项，可以预览配置效果</p>
					</el-alert>
				</div>
				
				<el-row :gutter="20">
					<el-col :span="8" v-for="dictType in dictTypes" :key="dictType.value">
						<el-card class="preview-card">
							<div slot="header" class="clearfix">
								<span>{{ dictType.label }}</span>
								<el-button style="float: right; padding: 3px 0" type="text" 
									@click="previewDictType(dictType.value)">查看详情</el-button>
							</div>
							
							<div class="preview-content">
								<el-tag v-for="item in getPreviewData(dictType.value)" :key="item.dictValue" 
									:type="item.listClass" size="small" style="margin: 2px;">
									{{ item.dictLabel }}
								</el-tag>
								<div v-if="!getPreviewData(dictType.value).length" class="no-data">
									暂无配置项
								</div>
							</div>
						</el-card>
					</el-col>
				</el-row>
			</el-tab-pane>
			
			<!-- 配置导入导出 -->
			<el-tab-pane label="配置导入导出" name="import">
				<div style="margin-bottom: 20px;">
					<el-alert title="配置导入导出" type="warning" show-icon>
						<p>可以导入导出医疗档案配置，便于在不同环境间同步配置</p>
					</el-alert>
				</div>
				
				<el-row :gutter="20">
					<el-col :span="12">
						<el-card>
							<div slot="header">
								<span>导出配置</span>
							</div>
							<div>
								<el-checkbox-group v-model="exportTypes">
									<el-checkbox v-for="item in dictTypes" :key="item.value" :label="item.value">
										{{ item.label }}
									</el-checkbox>
								</el-checkbox-group>
								<div style="margin-top: 20px;">
									<el-button type="primary" @click="exportAllConfig" :disabled="!exportTypes.length">
										导出选中配置
									</el-button>
									<el-button type="success" @click="exportAllConfig(true)">
										导出全部配置
									</el-button>
								</div>
							</div>
						</el-card>
					</el-col>
					<el-col :span="12">
						<el-card>
							<div slot="header">
								<span>导入配置</span>
							</div>
							<div>
								<el-upload class="upload-demo" drag action="#" :before-upload="handleImportFile" 
									:show-file-list="false" accept=".json">
									<i class="el-icon-upload"></i>
									<div class="el-upload__text">将配置文件拖到此处，或<em>点击上传</em></div>
									<div class="el-upload__tip" slot="tip">只能上传json文件</div>
								</el-upload>
								<div style="margin-top: 20px;">
									<el-button type="warning" @click="resetAllConfig">
										重置为默认配置
									</el-button>
								</div>
							</div>
						</el-card>
					</el-col>
				</el-row>
			</el-tab-pane>
		</el-tabs>
		
		<!-- 添加字典项对话框 -->
		<el-dialog title="添加字典项" :visible.sync="addDialogVisible" width="600px">
			<el-form :model="newDictItem" :rules="dictRules" ref="dictForm" label-width="120px">
				<el-form-item label="字典标签" prop="dictLabel">
					<el-input v-model="newDictItem.dictLabel" placeholder="请输入字典标签"></el-input>
				</el-form-item>
				<el-form-item label="字典键值" prop="dictValue">
					<el-input v-model="newDictItem.dictValue" placeholder="请输入字典键值"></el-input>
				</el-form-item>
				<el-form-item label="排序" prop="dictSort">
					<el-input-number v-model="newDictItem.dictSort" :min="1" :max="999"></el-input-number>
				</el-form-item>
				<el-form-item label="样式属性">
					<el-input v-model="newDictItem.cssClass" placeholder="请输入CSS样式类"></el-input>
				</el-form-item>
				<el-form-item label="表格样式">
					<el-select v-model="newDictItem.listClass" placeholder="请选择表格样式">
						<el-option label="默认" value="default"></el-option>
						<el-option label="主要" value="primary"></el-option>
						<el-option label="成功" value="success"></el-option>
						<el-option label="信息" value="info"></el-option>
						<el-option label="警告" value="warning"></el-option>
						<el-option label="危险" value="danger"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="是否默认">
					<el-switch v-model="newDictItem.isDefault" active-value="Y" inactive-value="N"></el-switch>
				</el-form-item>
				<el-form-item label="状态">
					<el-switch v-model="newDictItem.status" :active-value="1" :inactive-value="0"></el-switch>
				</el-form-item>
				<el-form-item label="备注">
					<el-input v-model="newDictItem.remark" type="textarea" placeholder="请输入备注"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="addDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveDictData">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			activeName: 'dict',
			selectedDictType: '',
			dictLoading: false,
			dictData: [],
			addDialogVisible: false,
			exportTypes: [],
			
			// 医疗档案相关的字典类型
			dictTypes: [
				{ value: 'patient_risk_level', label: '患者风险等级' },
				{ value: 'health_status_level', label: '健康状态评级' },
				{ value: 'medical_history_type', label: '病史类型' },
				{ value: 'allergy_type', label: '过敏类型' },
				{ value: 'allergy_severity', label: '过敏严重程度' },
				{ value: 'medication_type', label: '药物分类' },
				{ value: 'medication_status', label: '用药状态' },
				{ value: 'test_type', label: '检查类型' },
				{ value: 'test_result_status', label: '检查结果状态' },
				{ value: 'vaccine_type', label: '疫苗类型' },
				{ value: 'vaccination_reaction', label: '接种反应' },
				{ value: 'treatment_plan_type', label: '治疗方案类型' },
				{ value: 'treatment_plan_status', label: '治疗方案状态' },
				{ value: 'marital_status', label: '婚姻状况' },
				{ value: 'insurance_type', label: '医保类型' }
			],
			
			// 新增字典项
			newDictItem: {
				dictLabel: '',
				dictValue: '',
				dictSort: 1,
				cssClass: '',
				listClass: 'default',
				isDefault: 'N',
				status: 1,
				remark: ''
			},
			
			// 表单验证规则
			dictRules: {
				dictLabel: [
					{ required: true, message: '请输入字典标签', trigger: 'blur' }
				],
				dictValue: [
					{ required: true, message: '请输入字典键值', trigger: 'blur' }
				],
				dictSort: [
					{ required: true, message: '请输入排序', trigger: 'blur' }
				]
			},
			
			// 预览数据缓存
			previewDataCache: {}
		}
	},
	methods: {
		// 标签页切换
		handleClick(tab) {
			if (tab.name === 'preview') {
				this.loadAllPreviewData()
			}
		},
		
		// 获取字典类型名称
		getDictTypeName(type) {
			const item = this.dictTypes.find(t => t.value === type)
			return item ? item.label : type
		},

		// 加载字典数据
		loadDictData() {
			if (!this.selectedDictType) return

			this.dictLoading = true
			this.$http({
				url: this.$http.adornUrl('sys/dict/list'),
				method: 'get',
				params: this.$http.adornParams({
					type: this.selectedDictType
				})
			}).then(({data}) => {
				this.dictLoading = false
				if (data && data.code === 0) {
					// 适配现有的 sys_dict 表结构
					this.dictData = data.data.map(item => ({
						dictDataId: item.id,
						dictLabel: item.value || item.name,
						dictValue: item.code,
						dictSort: item.order_num || 0,
						cssClass: '',
						listClass: 'default',
						isDefault: 'N',
						status: 1,
						remark: item.remark || '',
						editing: false,
						originalData: { ...item }
					}))
				}
			}).catch(() => {
				this.dictLoading = false
			})
		},

		// 编辑字典项
		editDictItem(row) {
			row.editing = true
			row.originalData = { ...row }
		},

		// 取消编辑
		cancelEdit(row) {
			Object.assign(row, row.originalData)
			row.editing = false
		},

		// 保存字典项
		saveDictItem(row) {
			this.$http({
				url: this.$http.adornUrl('sys/dict/update'),
				method: 'post',
				data: this.$http.adornData({
					id: row.dictDataId,
					name: row.dictLabel,
					type: this.selectedDictType,
					code: row.dictValue,
					value: row.dictLabel,
					order_num: row.dictSort,
					remark: row.remark,
					parent_id: 0
				})
			}).then(({data}) => {
				if (data.code == 0) {
					this.$message.success('保存成功')
					row.editing = false
					this.loadDictData()
				} else {
					this.$message.error(data.msg)
				}
			})
		},

		// 更新排序
		updateDictSort(row) {
			this.saveDictItem(row)
		},

		// 更新默认状态
		updateDictDefault(row) {
			this.saveDictItem(row)
		},

		// 更新状态
		updateDictStatus(row) {
			this.saveDictItem(row)
		},

		// 删除字典项
		deleteDictItem(row) {
			this.$confirm(`确定删除字典项 "${row.dictLabel}"?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('sys/dict/delete'),
					method: 'post',
					data: this.$http.adornData({
						id: row.dictDataId
					})
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('删除成功')
						this.loadDictData()
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// 添加字典项
		addDictData() {
			this.newDictItem = {
				dictLabel: '',
				dictValue: '',
				dictSort: this.dictData.length + 1,
				cssClass: '',
				listClass: 'default',
				isDefault: 'N',
				status: 1,
				remark: ''
			}
			this.addDialogVisible = true
		},

		// 保存新字典项
		saveDictData() {
			this.$refs.dictForm.validate((valid) => {
				if (valid) {
					this.$http({
						url: this.$http.adornUrl('sys/dict/save'),
						method: 'post',
						data: this.$http.adornData({
							name: this.newDictItem.dictLabel,
							type: this.selectedDictType,
							code: this.newDictItem.dictValue,
							value: this.newDictItem.dictLabel,
							order_num: this.newDictItem.dictSort,
							remark: this.newDictItem.remark,
							parent_id: 0
						})
					}).then(({data}) => {
						if (data.code == 0) {
							this.$message.success('添加成功')
							this.addDialogVisible = false
							this.loadDictData()
						} else {
							this.$message.error(data.msg)
						}
					})
				}
			})
		},

		// 批量编辑
		batchEdit() {
			this.$confirm('确定进入批量编辑模式?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'info'
			}).then(() => {
				this.dictData.forEach(item => {
					item.editing = true
				})
				this.$message.info('已进入批量编辑模式，双击可编辑字段')
			}).catch(() => {})
		},

		// 导出字典数据
		exportDictData() {
			window.open(this.$http.adornUrl('sys/dict/exportDictData?' +
				this.$http.adornParams({
					dictType: this.selectedDictType
				}, true)))
		},

		// 加载所有预览数据
		loadAllPreviewData() {
			this.dictTypes.forEach(dictType => {
				if (!this.previewDataCache[dictType.value]) {
					this.$http({
						url: this.$http.adornUrl('sys/dict/selectDictList'),
						method: 'get',
						params: this.$http.adornParams({
							type: dictType.value
						})
					}).then(({data}) => {
						if (data && data.code === 0) {
							this.$set(this.previewDataCache, dictType.value, data.data)
						}
					})
				}
			})
		},

		// 获取预览数据
		getPreviewData(dictType) {
			return this.previewDataCache[dictType] || []
		},

		// 预览字典类型
		previewDictType(dictType) {
			this.selectedDictType = dictType
			this.activeName = 'dict'
			this.loadDictData()
		},

		// 导出全部配置
		exportAllConfig(exportAll = false) {
			const types = exportAll ? this.dictTypes.map(t => t.value) : this.exportTypes

			if (!exportAll && types.length === 0) {
				this.$message.warning('请选择要导出的配置类型')
				return
			}

			this.$http({
				url: this.$http.adornUrl('sys/dict/exportMedicalConfig'),
				method: 'post',
				data: this.$http.adornData({
					dictTypes: types
				})
			}).then(({data}) => {
				if (data.code == 0) {
					// 创建下载链接
					const blob = new Blob([JSON.stringify(data.data, null, 2)], {
						type: 'application/json'
					})
					const url = window.URL.createObjectURL(blob)
					const link = document.createElement('a')
					link.href = url
					link.download = `medical_dict_config_${new Date().getTime()}.json`
					link.click()
					window.URL.revokeObjectURL(url)

					this.$message.success('配置导出成功')
				} else {
					this.$message.error(data.msg)
				}
			})
		},

		// 处理导入文件
		handleImportFile(file) {
			const reader = new FileReader()
			reader.onload = (e) => {
				try {
					const config = JSON.parse(e.target.result)
					this.importConfig(config)
				} catch (error) {
					this.$message.error('文件格式错误，请上传有效的JSON文件')
				}
			}
			reader.readAsText(file)
			return false // 阻止自动上传
		},

		// 导入配置
		importConfig(config) {
			this.$confirm('确定导入配置? 这将覆盖现有的配置项', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('sys/dict/importMedicalConfig'),
					method: 'post',
					data: this.$http.adornData(config)
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('配置导入成功')
						this.loadDictData()
						this.previewDataCache = {} // 清空缓存
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		},

		// 重置为默认配置
		resetAllConfig() {
			this.$confirm('确定重置为默认配置? 这将删除所有自定义配置', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: this.$http.adornUrl('sys/dict/resetMedicalConfig'),
					method: 'post'
				}).then(({data}) => {
					if (data.code == 0) {
						this.$message.success('重置成功')
						this.loadDictData()
						this.previewDataCache = {} // 清空缓存
					} else {
						this.$message.error(data.msg)
					}
				})
			}).catch(() => {})
		}
	},
	mounted() {
		// 默认选择第一个字典类型
		if (this.dictTypes.length > 0) {
			this.selectedDictType = this.dictTypes[0].value
			this.loadDictData()
		}
	}
}
</script>

<style scoped>
.box-card {
	margin-top: 20px;
}

.preview-card {
	margin-bottom: 20px;
	height: 200px;
}

.preview-content {
	max-height: 120px;
	overflow-y: auto;
}

.no-data {
	color: #909399;
	text-align: center;
	padding: 20px;
}

.upload-demo {
	text-align: center;
}
</style>
