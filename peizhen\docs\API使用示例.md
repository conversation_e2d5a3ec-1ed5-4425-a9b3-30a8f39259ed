# 医疗档案API使用示例

## 概述

本文档提供了患者医疗档案增强功能的详细API使用示例，包括请求格式、响应格式和常见使用场景。

## 基础配置

### 请求头设置
```http
Content-Type: application/json
Authorization: Bearer {token}
```

### 基础URL
```
用户端: /app/patientMedical/
管理端: /admin/patientMedical/
```

## API使用示例

### 1. 健康状态管理

#### 保存健康状态
```http
POST /app/patientMedical/saveHealthStatus
Content-Type: application/json

{
  "patientId": 123,
  "overallHealth": 2,
  "height": 170.5,
  "weight": 65.0,
  "bmi": 22.37,
  "bloodType": "A",
  "rhType": 1,
  "smokingStatus": 0,
  "drinkingStatus": 1,
  "exerciseFrequency": 3,
  "sleepQuality": 2,
  "mentalStatus": 2,
  "mobilityStatus": 1,
  "chronicDiseases": "高血压",
  "riskLevel": 2,
  "specialNotes": "定期监测血压"
}
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "健康状态保存成功",
  "success": true
}
```

#### 获取健康状态
```http
GET /app/patientMedical/getHealthStatus?patientId=123
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "data": {
    "statusId": 1,
    "patientId": 123,
    "overallHealth": 2,
    "height": 170.5,
    "weight": 65.0,
    "bmi": 22.37,
    "bloodType": "A",
    "rhType": 1,
    "smokingStatus": 0,
    "drinkingStatus": 1,
    "exerciseFrequency": 3,
    "sleepQuality": 2,
    "mentalStatus": 2,
    "mobilityStatus": 1,
    "chronicDiseases": "高血压",
    "riskLevel": 2,
    "specialNotes": "定期监测血压",
    "createTime": "2024-08-04T10:30:00",
    "updateTime": "2024-08-04T10:30:00"
  }
}
```

### 2. 病史记录管理

#### 保存病史记录
```http
POST /app/patientMedical/saveMedicalHistory
Content-Type: application/json

{
  "patientId": 123,
  "historyType": 1,
  "diseaseName": "高血压",
  "diseaseCode": "I10",
  "diagnosisDate": "2020-01-15",
  "hospitalName": "北京协和医院",
  "doctorName": "张医生",
  "treatmentResult": 2,
  "severity": 2,
  "isHereditary": 0,
  "description": "原发性高血压，目前药物控制良好",
  "isActive": 1
}
```

#### 分页查询病史记录
```http
GET /app/patientMedical/getMedicalHistoryList?page=1&limit=10&patientId=123&historyType=1
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "data": {
    "records": [
      {
        "historyId": 1,
        "patientId": 123,
        "historyType": 1,
        "diseaseName": "高血压",
        "diseaseCode": "I10",
        "diagnosisDate": "2020-01-15",
        "hospitalName": "北京协和医院",
        "doctorName": "张医生",
        "treatmentResult": 2,
        "severity": 2,
        "isHereditary": 0,
        "description": "原发性高血压，目前药物控制良好",
        "isActive": 1,
        "createTime": "2024-08-04T10:30:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 3. 过敏信息管理

#### 保存过敏记录
```http
POST /app/patientMedical/saveAllergy
Content-Type: application/json

{
  "patientId": 123,
  "allergyType": 1,
  "allergenName": "青霉素",
  "allergenCode": "PENICILLIN",
  "severity": 3,
  "symptoms": "皮疹、呼吸困难、血压下降",
  "firstDiscoveryDate": "2018-05-20",
  "treatmentMethod": "立即停药，肾上腺素注射，抗过敏治疗",
  "requiresEmergencyTreatment": 1,
  "isConfirmed": 1,
  "confirmationMethod": 1,
  "confirmationHospital": "北京协和医院",
  "confirmationDoctor": "李医生",
  "isActive": 1
}
```

#### 检查过敏冲突
```http
POST /app/patientMedical/checkAllergyConflict
Content-Type: application/x-www-form-urlencoded

patientId=123&allergenName=青霉素&allergyType=1
```

#### 响应示例（存在冲突）
```json
{
  "code": 500,
  "msg": "患者对青霉素存在严重过敏反应，禁止使用",
  "success": false
}
```

### 4. 用药记录管理

#### 保存用药记录
```http
POST /app/patientMedical/saveMedication
Content-Type: application/json

{
  "patientId": 123,
  "medicationName": "阿司匹林",
  "brandName": "拜阿司匹灵",
  "medicationType": 1,
  "dosageForm": 1,
  "strength": "100mg",
  "singleDose": 100,
  "doseUnit": "mg",
  "frequency": 1,
  "timingOfAdministration": 2,
  "routeOfAdministration": 1,
  "startDate": "2024-01-01",
  "indication": "预防心血管疾病",
  "prescribingDoctor": "王医生",
  "prescribingHospital": "北京协和医院",
  "prescriptionDate": "2024-01-01",
  "medicationStatus": 1
}
```

#### 获取当前用药列表
```http
GET /app/patientMedical/getCurrentMedications?patientId=123
```

#### 停用药物
```http
POST /app/patientMedical/discontinueMedication
Content-Type: application/x-www-form-urlencoded

medicationId=1&reason=治疗完成
```

### 5. 生命体征管理

#### 保存生命体征记录
```http
POST /app/patientMedical/saveVitalSigns
Content-Type: application/json

{
  "patientId": 123,
  "measurementDate": "2024-08-04 10:30:00",
  "systolicPressure": 120,
  "diastolicPressure": 80,
  "heartRate": 72,
  "temperature": 36.5,
  "respiratoryRate": 18,
  "oxygenSaturation": 98.5,
  "weight": 65.0,
  "height": 170.5,
  "painScore": 0,
  "consciousnessLevel": 1,
  "measurementEnvironment": 2,
  "measurementDevice": "欧姆龙血压计",
  "measuredBy": "患者自测"
}
```

#### 获取最新生命体征
```http
GET /app/patientMedical/getLatestVitalSigns?patientId=123
```

### 6. 检查结果管理

#### 保存检查结果
```http
POST /app/patientMedical/saveTestResult
Content-Type: application/json

{
  "patientId": 123,
  "testType": 1,
  "testName": "血常规",
  "testCode": "CBC",
  "testDate": "2024-08-01",
  "testHospital": "北京协和医院",
  "testDepartment": "检验科",
  "testValue": "白细胞计数: 6.5×10^9/L, 红细胞计数: 4.5×10^12/L",
  "numericValue": 6.5,
  "unit": "×10^9/L",
  "referenceRange": "4.0-10.0",
  "resultStatus": 1,
  "reportNumber": "RPT20240801001",
  "reportDoctor": "检验师张三",
  "isUrgent": 0
}
```

#### 获取最近检查结果
```http
GET /app/patientMedical/getRecentTestResults?patientId=123&limit=5
```

### 7. 综合功能

#### 获取完整医疗档案
```http
GET /app/patientMedical/getCompleteMedicalProfile?patientId=123
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "data": {
    "patientId": 123,
    "realName": "张三",
    "phone": "138****5678",
    "profileCompleteness": 85,
    "riskLevel": 2,
    "lastMedicalUpdateTime": "2024-08-04T10:30:00",
    "healthStatus": {
      "overallHealth": 2,
      "height": 170.5,
      "weight": 65.0,
      "bloodType": "A"
    },
    "medicalHistoryList": [
      {
        "diseaseName": "高血压",
        "diagnosisDate": "2020-01-15",
        "isActive": 1
      }
    ],
    "allergyList": [
      {
        "allergenName": "青霉素",
        "severity": 3,
        "isActive": 1
      }
    ],
    "currentMedicationList": [
      {
        "medicationName": "阿司匹林",
        "singleDose": 100,
        "frequency": 1,
        "medicationStatus": 1
      }
    ],
    "latestVitalSigns": {
      "measurementDate": "2024-08-04 10:30:00",
      "systolicPressure": 120,
      "diastolicPressure": 80,
      "heartRate": 72
    }
  }
}
```

#### 医疗风险评估
```http
POST /app/patientMedical/assessMedicalRisk
Content-Type: application/x-www-form-urlencoded

patientId=123
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "data": {
    "riskScore": 45,
    "riskLevel": 2,
    "riskDescription": "中风险：患者存在一定医疗风险，需要定期监测和关注"
  }
}
```

#### 更新档案完整度
```http
POST /app/patientMedical/updateProfileCompleteness
Content-Type: application/x-www-form-urlencoded

patientId=123
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "completeness": 85
}
```

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `401`: 未授权访问
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "code": 400,
  "msg": "患者ID不能为空",
  "success": false
}
```

## 最佳实践

### 1. 数据验证
- 始终验证输入数据的格式和范围
- 使用提供的验证工具类进行数据校验
- 注意日期格式必须为 `yyyy-MM-dd`

### 2. 错误处理
- 检查API响应的 `success` 字段
- 根据错误信息提供用户友好的提示
- 实现重试机制处理网络异常

### 3. 性能优化
- 使用分页查询大量数据
- 避免频繁调用完整医疗档案接口
- 合理使用缓存机制

### 4. 安全考虑
- 确保用户只能访问自己的医疗数据
- 对敏感信息进行脱敏处理
- 使用HTTPS传输医疗数据

## 集成示例

### JavaScript/Vue.js示例
```javascript
// 保存健康状态
async function saveHealthStatus(healthData) {
  try {
    const response = await axios.post('/app/patientMedical/saveHealthStatus', healthData);
    if (response.data.success) {
      this.$message.success('健康状态保存成功');
    } else {
      this.$message.error(response.data.msg);
    }
  } catch (error) {
    this.$message.error('保存失败，请重试');
  }
}

// 获取完整医疗档案
async function getMedicalProfile(patientId) {
  try {
    const response = await axios.get(`/app/patientMedical/getCompleteMedicalProfile?patientId=${patientId}`);
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.msg);
    }
  } catch (error) {
    console.error('获取医疗档案失败:', error);
    throw error;
  }
}
```

### Java客户端示例
```java
// 使用RestTemplate调用API
@Service
public class MedicalProfileClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public Result saveHealthStatus(PatientHealthStatus healthStatus) {
        String url = "/app/patientMedical/saveHealthStatus";
        return restTemplate.postForObject(url, healthStatus, Result.class);
    }
    
    public PatientInfo getMedicalProfile(Long patientId) {
        String url = "/app/patientMedical/getCompleteMedicalProfile?patientId=" + patientId;
        Result result = restTemplate.getForObject(url, Result.class);
        return (PatientInfo) result.get("data");
    }
}
```

## 总结

本API提供了完整的患者医疗档案管理功能，支持健康状态、病史、过敏、用药、生命体征、检查结果、疫苗接种和治疗方案的全面管理。通过合理使用这些API，可以构建功能强大的医疗信息管理系统。
