# 医疗档案数据字典配置部署指南

## 🎯 概述

本指南说明如何为患者医疗档案管理系统配置数据字典，使所有选项都通过配置管理，而不是硬编码。

## 📋 现有表结构

您的系统使用的是 `sys_dict` 表，结构如下：

```sql
CREATE TABLE `sys_dict` (
    `id`        bigint(20) NOT NULL AUTO_INCREMENT,
    `name`      varchar(100) COMMENT '字典名称',
    `type`      varchar(100) COMMENT '字典类型',
    `code`      varchar(100) COMMENT '字典码',
    `value`     varchar(1000) COMMENT '字典值',
    `order_num` int(11) DEFAULT 0 COMMENT '排序',
    `remark`    varchar(255) COMMENT '备注',
    `parent_id` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
);
```

## 🚀 部署步骤

### 1. 执行数据字典配置SQL

运行以下SQL文件来添加医疗档案相关的数据字典：

```bash
# 执行数据字典配置
mysql -u your_username -p your_database < peizhen/db/medical_dict_config.sql
```

### 2. 验证数据字典配置

执行以下SQL验证配置是否成功：

```sql
-- 查看所有医疗相关的字典类型
SELECT DISTINCT type FROM sys_dict WHERE type LIKE '%patient%' OR type LIKE '%medical%' OR type LIKE '%health%';

-- 查看患者风险等级配置
SELECT * FROM sys_dict WHERE type = 'patient_risk_level' ORDER BY order_num;

-- 查看健康状态评级配置
SELECT * FROM sys_dict WHERE type = 'health_status_level' ORDER BY order_num;
```

### 3. 后端API适配

确保您的后端有以下API接口来支持数据字典查询：

```java
// 根据类型查询字典列表
@RequestMapping("/list")
public R list(@RequestParam Map<String, Object> params) {
    String type = (String) params.get("type");
    List<SysDictEntity> dictList = sysDictService.queryByType(type);
    return R.ok().put("data", dictList);
}
```

### 4. 前端页面部署

将以下Vue页面文件复制到您的前端项目：

```
peizhen--admin/src/views/carBrand/
├── patientMedicalProfile.vue      # 患者医疗档案主管理页面
├── medicationManagement.vue       # 用药与检查管理页面
├── vaccinationTreatment.vue       # 疫苗接种与治疗方案管理页面
└── medicalDictManagement.vue      # 数据字典配置管理页面
```

### 5. 路由配置

在您的路由文件中添加以下配置：

```javascript
{
  path: '/medical',
  component: Layout,
  redirect: '/medical/patient',
  name: 'Medical',
  meta: { title: '医疗档案管理', icon: 'medical' },
  children: [
    {
      path: 'patient',
      name: 'PatientMedical',
      component: () => import('@/views/carBrand/patientMedicalProfile.vue'),
      meta: { title: '患者档案管理', icon: 'patient' }
    },
    {
      path: 'medication',
      name: 'MedicationManagement',
      component: () => import('@/views/carBrand/medicationManagement.vue'),
      meta: { title: '用药检查管理', icon: 'medication' }
    },
    {
      path: 'vaccination',
      name: 'VaccinationTreatment',
      component: () => import('@/views/carBrand/vaccinationTreatment.vue'),
      meta: { title: '疫苗治疗管理', icon: 'vaccination' }
    },
    {
      path: 'dict',
      name: 'MedicalDict',
      component: () => import('@/views/carBrand/medicalDictManagement.vue'),
      meta: { title: '配置项管理', icon: 'dict' }
    }
  ]
}
```

## 📊 配置的数据字典类型

| 字典类型 | 说明 | 示例值 |
|---------|------|--------|
| `patient_risk_level` | 患者风险等级 | 低风险、中风险、高风险 |
| `health_status_level` | 健康状态评级 | 优秀、良好、一般、较差、差 |
| `medical_history_type` | 病史类型 | 既往史、现病史、家族史、过敏史、手术史 |
| `allergy_type` | 过敏类型 | 药物过敏、食物过敏、环境过敏、接触过敏 |
| `allergy_severity` | 过敏严重程度 | 轻微、中度、严重、危及生命 |
| `medication_type` | 药物分类 | 处方药、非处方药、中药、生物制剂、疫苗 |
| `medication_status` | 用药状态 | 正在使用、已停用、暂停使用 |
| `test_type` | 检查类型 | 血液检查、影像检查、心电检查、内镜检查 |
| `test_result_status` | 检查结果状态 | 正常、异常偏高、异常偏低、临界值、严重异常 |
| `vaccine_type` | 疫苗类型 | 新冠疫苗、流感疫苗、乙肝疫苗、HPV疫苗 |
| `vaccination_reaction` | 接种反应 | 无反应、轻微反应、中度反应、严重反应 |
| `treatment_plan_type` | 治疗方案类型 | 药物治疗、手术治疗、物理治疗、心理治疗 |
| `treatment_plan_status` | 治疗方案状态 | 计划中、进行中、已完成、暂停、终止 |
| `marital_status` | 婚姻状况 | 未婚、已婚、离异、丧偶 |
| `insurance_type` | 医保类型 | 城镇职工、城镇居民、新农合、商业保险、自费 |

## 🔧 配置管理

### 使用数据字典管理页面

1. **访问配置页面**: 进入"配置项管理"页面
2. **选择字典类型**: 从下拉列表选择要管理的字典类型
3. **编辑配置项**: 双击字段进行在线编辑
4. **添加新项**: 点击"添加字典项"按钮
5. **批量编辑**: 使用批量编辑模式
6. **导入导出**: 备份和迁移配置

### 配置项字段说明

- **字典标签**: 显示给用户看的文本
- **字典键值**: 程序中使用的值
- **排序**: 显示顺序
- **样式属性**: CSS样式类
- **表格样式**: 标签颜色类型
- **是否默认**: 是否为默认选项
- **状态**: 是否启用
- **备注**: 配置说明

## 🎨 前端适配说明

### 数据格式转换

前端页面会自动将 `sys_dict` 表的数据格式转换为统一的格式：

```javascript
const options = data.data.map(item => ({
    dictValue: item.code,        // 字典键值
    dictLabel: item.value || item.name  // 字典标签
}))
```

### API调用方式

```javascript
// 查询字典数据
this.$http({
    url: this.$http.adornUrl('sys/dict/list'),
    method: 'get',
    params: this.$http.adornParams({
        type: 'patient_risk_level'  // 字典类型
    })
})
```

## ✅ 验证配置

### 1. 检查数据字典数据

```sql
-- 检查配置的字典类型数量
SELECT type, COUNT(*) as count FROM sys_dict 
WHERE type IN (
    'patient_risk_level', 'health_status_level', 'medical_history_type',
    'allergy_type', 'allergy_severity', 'medication_type', 'medication_status',
    'test_type', 'test_result_status', 'vaccine_type', 'vaccination_reaction',
    'treatment_plan_type', 'treatment_plan_status', 'marital_status', 'insurance_type'
) 
GROUP BY type;
```

### 2. 测试前端页面

1. 访问患者档案管理页面
2. 检查所有下拉选项是否正确加载
3. 验证标签显示是否正常
4. 测试配置项管理功能

### 3. 功能验证清单

- [ ] 患者风险等级选项正常显示
- [ ] 健康状态评级选项正常显示
- [ ] 病史类型选项正常显示
- [ ] 过敏信息选项正常显示
- [ ] 用药相关选项正常显示
- [ ] 检查相关选项正常显示
- [ ] 疫苗接种选项正常显示
- [ ] 治疗方案选项正常显示
- [ ] 配置项管理功能正常
- [ ] 配置导入导出功能正常

## 🔄 后续维护

### 添加新的配置类型

1. 在 `sys_dict` 表中添加新的字典数据
2. 在前端页面的 `loadDictData()` 方法中添加新类型
3. 在数据字典管理页面的 `dictTypes` 数组中添加新类型

### 修改现有配置

1. 使用数据字典管理页面进行在线编辑
2. 或直接在数据库中修改 `sys_dict` 表数据
3. 前端页面会自动加载最新配置

## 🚨 注意事项

1. **备份数据**: 修改配置前请备份数据库
2. **测试环境**: 先在测试环境验证配置
3. **权限控制**: 配置管理功能需要管理员权限
4. **缓存清理**: 修改配置后可能需要清理前端缓存

通过这套完全配置化的数据字典系统，您可以灵活地管理所有医疗档案相关的选项，无需修改代码即可调整业务配置！
