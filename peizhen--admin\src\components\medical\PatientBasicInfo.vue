<template>
  <div class="patient-basic-info">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="search-item">
          <span>患者姓名：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入患者姓名"
            v-model="searchForm.realName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>所属用户昵称：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入所属用户昵称"
            v-model="searchForm.userName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>患者电话：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入患者电话"
            v-model="searchForm.phone"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>风险等级：</span>
          <el-select style="width: 200px;" v-model="searchForm.riskLevel" placeholder="请选择风险等级" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in riskLevelList" :key="item.code" :label="item.value" :value="item.code">
            </el-option>
          </el-select>
        </div>
      </div>
      
      <div class="action-buttons">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="handleSearch">
          查询
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd">
          添加患者
        </el-button>
        <el-button size="mini" type="success" icon="el-icon-download" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData.records" border stripe>
      <el-table-column fixed prop="patientId" label="患者ID" width="80"></el-table-column>
      <el-table-column prop="realName" label="患者姓名" width="120">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            style="color: #409EFF;background: #fff;border: none;padding: 0;" 
            type="primary"
            @click="handleViewProfile(scope.row)">
            {{ scope.row.realName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="所属用户" width="120">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            style="color: #008000;background: #fff;border: none;padding: 0;" 
            type="primary"
            @click="handleViewUser(scope.row.userId)">
            {{ scope.row.userName || '未绑定' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="sex" label="性别" width="60">
        <template slot-scope="scope">
          <span>{{ scope.row.sex == 1 ? '男' : '女' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="birthDate" label="出生日期" width="100"></el-table-column>
      <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
      <el-table-column prop="profileCompleteness" label="档案完整度" width="120">
        <template slot-scope="scope">
          <el-progress 
            :percentage="scope.row.profileCompleteness || 0"
            :color="getCompletenessColor(scope.row.profileCompleteness)">
          </el-progress>
        </template>
      </el-table-column>
      <el-table-column prop="riskLevel" label="风险等级" width="100">
        <template slot-scope="scope">
          <el-tag :type="getRiskLevelType(scope.row.riskLevel)">
            {{ getRiskLevelText(scope.row.riskLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="lastMedicalUpdateTime" label="最后更新" width="150">
        <template slot-scope="scope">
          <span>{{ formatDate(scope.row.lastMedicalUpdateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="350" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="success" @click="handleViewProfile(scope.row)">医疗档案</el-button>
          <el-button size="mini" type="warning" @click="handleAssessRisk(scope.row)">风险评估</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination 
        @size-change="handleSizeChange" 
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 30, 40]" 
        :page-size="pagination.limit" 
        :current-page="pagination.page"
        layout="total,sizes, prev, pager, next,jumper" 
        :total="tableData.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PatientBasicInfo',
  props: {
    riskLevelList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        realName: '',
        userName: '',
        phone: '',
        riskLevel: ''
      },
      pagination: {
        page: 1,
        limit: 10
      },
      tableData: {
        records: [],
        total: 0
      }
    }
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const response = await this.$http({
          url: this.$http.adornUrl('admin/patientInfo/getPatientList'),
          method: 'get',
          params: this.$http.adornParams({
            page: this.pagination.page,
            limit: this.pagination.limit,
            ...this.searchForm
          })
        })
        
        if (response.data && response.data.code === 0) {
          this.tableData = response.data.data
        }
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        realName: '',
        userName: '',
        phone: '',
        riskLevel: ''
      }
      this.pagination.page = 1
      this.loadData()
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.limit = val
      this.loadData()
    },

    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },

    // 操作处理
    handleAdd() {
      this.$emit('add-patient')
    },

    handleEdit(row) {
      this.$emit('edit-patient', row)
    },

    handleDelete(row) {
      this.$emit('delete-patient', row)
    },

    handleViewProfile(row) {
      this.$emit('view-profile', row)
    },

    handleViewUser(userId) {
      this.$emit('view-user', userId)
    },

    handleAssessRisk(row) {
      this.$emit('assess-risk', row)
    },

    handleExport() {
      this.$emit('export-data', this.searchForm)
    },

    // 工具方法
    getCompletenessColor(percentage) {
      if (percentage >= 80) return '#67c23a'
      if (percentage >= 60) return '#e6a23c'
      return '#f56c6c'
    },

    getRiskLevelType(level) {
      switch(level) {
        case 1: return 'success'
        case 2: return 'warning'
        case 3: return 'danger'
        default: return 'info'
      }
    },

    getRiskLevelText(level) {
      if (!level) return '未评估'
      const riskItem = this.riskLevelList.find(item => item.code == level)
      return riskItem ? riskItem.value : '未评估'
    },

    formatDate(dateStr) {
      if (!dateStr) return '-'
      return new Date(dateStr).toLocaleString('zh-CN')
    }
  },

  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.patient-basic-info {
  padding: 20px;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-item span {
  white-space: nowrap;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.pagination-section {
  text-align: center;
  margin-top: 20px;
}
</style>
