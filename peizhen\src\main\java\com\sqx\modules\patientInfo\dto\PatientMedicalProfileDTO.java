package com.sqx.modules.patientInfo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 患者医疗档案数据传输对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@ApiModel("患者医疗档案DTO")
public class PatientMedicalProfileDTO {

    @ApiModelProperty("患者ID")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @ApiModelProperty("健康状态信息")
    @Valid
    private HealthStatusDTO healthStatus;

    @ApiModelProperty("病史记录列表")
    @Valid
    private List<MedicalHistoryDTO> medicalHistoryList;

    @ApiModelProperty("过敏信息列表")
    @Valid
    private List<AllergyDTO> allergyList;

    @ApiModelProperty("用药记录列表")
    @Valid
    private List<MedicationDTO> medicationList;

    @ApiModelProperty("生命体征记录")
    @Valid
    private VitalSignsDTO vitalSigns;

    @ApiModelProperty("检查结果列表")
    @Valid
    private List<TestResultDTO> testResultList;

    @ApiModelProperty("疫苗接种记录列表")
    @Valid
    private List<VaccinationDTO> vaccinationList;

    @ApiModelProperty("治疗方案列表")
    @Valid
    private List<TreatmentPlanDTO> treatmentPlanList;

    /**
     * 健康状态DTO
     */
    @Data
    @ApiModel("健康状态DTO")
    public static class HealthStatusDTO {
        
        @ApiModelProperty("整体健康状态(1优秀 2良好 3一般 4较差 5很差)")
        @Min(value = 1, message = "健康状态值必须在1-5之间")
        @Max(value = 5, message = "健康状态值必须在1-5之间")
        private Integer overallHealth;

        @ApiModelProperty("身高(cm)")
        @DecimalMin(value = "50.0", message = "身高不能小于50cm")
        @DecimalMax(value = "250.0", message = "身高不能大于250cm")
        private BigDecimal height;

        @ApiModelProperty("体重(kg)")
        @DecimalMin(value = "10.0", message = "体重不能小于10kg")
        @DecimalMax(value = "300.0", message = "体重不能大于300kg")
        private BigDecimal weight;

        @ApiModelProperty("血型(A、B、AB、O)")
        @Pattern(regexp = "^(A|B|AB|O)$", message = "血型必须是A、B、AB、O中的一种")
        private String bloodType;

        @ApiModelProperty("RH血型(0阴性 1阳性)")
        @Min(value = 0, message = "RH血型值必须是0或1")
        @Max(value = 1, message = "RH血型值必须是0或1")
        private Integer rhType;

        @ApiModelProperty("吸烟状况(0从不吸烟 1已戒烟 2偶尔吸烟 3经常吸烟)")
        @Min(value = 0, message = "吸烟状况值必须在0-3之间")
        @Max(value = 3, message = "吸烟状况值必须在0-3之间")
        private Integer smokingStatus;

        @ApiModelProperty("饮酒状况(0从不饮酒 1已戒酒 2偶尔饮酒 3经常饮酒)")
        @Min(value = 0, message = "饮酒状况值必须在0-3之间")
        @Max(value = 3, message = "饮酒状况值必须在0-3之间")
        private Integer drinkingStatus;

        @ApiModelProperty("运动频率(0从不运动 1偶尔运动 2每周1-2次 3每周3-4次 4每天运动)")
        @Min(value = 0, message = "运动频率值必须在0-4之间")
        @Max(value = 4, message = "运动频率值必须在0-4之间")
        private Integer exerciseFrequency;

        @ApiModelProperty("睡眠质量(1很好 2较好 3一般 4较差 5很差)")
        @Min(value = 1, message = "睡眠质量值必须在1-5之间")
        @Max(value = 5, message = "睡眠质量值必须在1-5之间")
        private Integer sleepQuality;

        @ApiModelProperty("精神状态(1很好 2较好 3一般 4较差 5很差)")
        @Min(value = 1, message = "精神状态值必须在1-5之间")
        @Max(value = 5, message = "精神状态值必须在1-5之间")
        private Integer mentalStatus;

        @ApiModelProperty("活动能力(1完全自理 2基本自理 3部分依赖 4完全依赖)")
        @Min(value = 1, message = "活动能力值必须在1-4之间")
        @Max(value = 4, message = "活动能力值必须在1-4之间")
        private Integer mobilityStatus;

        @ApiModelProperty("慢性疾病列表")
        @Size(max = 1000, message = "慢性疾病描述不能超过1000字符")
        private String chronicDiseases;

        @ApiModelProperty("特殊注意事项")
        @Size(max = 2000, message = "特殊注意事项不能超过2000字符")
        private String specialNotes;
    }

    /**
     * 病史记录DTO
     */
    @Data
    @ApiModel("病史记录DTO")
    public static class MedicalHistoryDTO {
        
        @ApiModelProperty("病史类型(1既往病史 2手术史 3外伤史 4输血史 5家族史)")
        @NotNull(message = "病史类型不能为空")
        @Min(value = 1, message = "病史类型值必须在1-5之间")
        @Max(value = 5, message = "病史类型值必须在1-5之间")
        private Integer historyType;

        @ApiModelProperty("疾病/手术名称")
        @NotBlank(message = "疾病/手术名称不能为空")
        @Size(max = 200, message = "疾病/手术名称不能超过200字符")
        private String diseaseName;

        @ApiModelProperty("疾病编码(ICD-10)")
        @Size(max = 50, message = "疾病编码不能超过50字符")
        private String diseaseCode;

        @ApiModelProperty("诊断时间/手术时间")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String diagnosisDate;

        @ApiModelProperty("治疗医院")
        @Size(max = 200, message = "治疗医院名称不能超过200字符")
        private String hospitalName;

        @ApiModelProperty("主治医生")
        @Size(max = 100, message = "主治医生姓名不能超过100字符")
        private String doctorName;

        @ApiModelProperty("治疗结果(1治愈 2好转 3未愈 4死亡 5未知)")
        @Min(value = 1, message = "治疗结果值必须在1-5之间")
        @Max(value = 5, message = "治疗结果值必须在1-5之间")
        private Integer treatmentResult;

        @ApiModelProperty("病情严重程度(1轻度 2中度 3重度)")
        @Min(value = 1, message = "严重程度值必须在1-3之间")
        @Max(value = 3, message = "严重程度值必须在1-3之间")
        private Integer severity;

        @ApiModelProperty("是否遗传性疾病(0否 1是)")
        @Min(value = 0, message = "遗传性疾病标识必须是0或1")
        @Max(value = 1, message = "遗传性疾病标识必须是0或1")
        private Integer isHereditary;

        @ApiModelProperty("家族关系")
        @Size(max = 50, message = "家族关系不能超过50字符")
        private String familyRelation;

        @ApiModelProperty("详细描述")
        @Size(max = 2000, message = "详细描述不能超过2000字符")
        private String description;

        @ApiModelProperty("是否当前活跃病史(0否 1是)")
        @Min(value = 0, message = "活跃状态标识必须是0或1")
        @Max(value = 1, message = "活跃状态标识必须是0或1")
        private Integer isActive;
    }

    /**
     * 过敏信息DTO
     */
    @Data
    @ApiModel("过敏信息DTO")
    public static class AllergyDTO {
        
        @ApiModelProperty("过敏类型(1药物过敏 2食物过敏 3环境过敏 4接触性过敏 5其他)")
        @NotNull(message = "过敏类型不能为空")
        @Min(value = 1, message = "过敏类型值必须在1-5之间")
        @Max(value = 5, message = "过敏类型值必须在1-5之间")
        private Integer allergyType;

        @ApiModelProperty("过敏原名称")
        @NotBlank(message = "过敏原名称不能为空")
        @Size(max = 200, message = "过敏原名称不能超过200字符")
        private String allergenName;

        @ApiModelProperty("过敏严重程度(1轻微 2中度 3严重 4危及生命)")
        @NotNull(message = "过敏严重程度不能为空")
        @Min(value = 1, message = "严重程度值必须在1-4之间")
        @Max(value = 4, message = "严重程度值必须在1-4之间")
        private Integer severity;

        @ApiModelProperty("过敏反应症状")
        @Size(max = 1000, message = "过敏反应症状描述不能超过1000字符")
        private String symptoms;

        @ApiModelProperty("首次发现时间")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String firstDiscoveryDate;

        @ApiModelProperty("是否确认过敏(0疑似 1确认)")
        @Min(value = 0, message = "确认状态必须是0或1")
        @Max(value = 1, message = "确认状态必须是0或1")
        private Integer isConfirmed;

        @ApiModelProperty("确认方式(1临床观察 2皮肤试验 3血液检测 4激发试验)")
        @Min(value = 1, message = "确认方式值必须在1-4之间")
        @Max(value = 4, message = "确认方式值必须在1-4之间")
        private Integer confirmationMethod;

        @ApiModelProperty("是否当前活跃(0否 1是)")
        @Min(value = 0, message = "活跃状态必须是0或1")
        @Max(value = 1, message = "活跃状态必须是0或1")
        private Integer isActive;
    }

    /**
     * 用药记录DTO
     */
    @Data
    @ApiModel("用药记录DTO")
    public static class MedicationDTO {
        
        @ApiModelProperty("药物名称(通用名)")
        @NotBlank(message = "药物名称不能为空")
        @Size(max = 200, message = "药物名称不能超过200字符")
        private String medicationName;

        @ApiModelProperty("商品名")
        @Size(max = 200, message = "商品名不能超过200字符")
        private String brandName;

        @ApiModelProperty("药物分类(1处方药 2非处方药 3中药 4生物制品)")
        @Min(value = 1, message = "药物分类值必须在1-4之间")
        @Max(value = 4, message = "药物分类值必须在1-4之间")
        private Integer medicationType;

        @ApiModelProperty("单次剂量")
        @DecimalMin(value = "0.001", message = "单次剂量必须大于0")
        private BigDecimal singleDose;

        @ApiModelProperty("剂量单位")
        @Size(max = 20, message = "剂量单位不能超过20字符")
        private String doseUnit;

        @ApiModelProperty("用药频率(1每日一次 2每日两次 3每日三次 4每日四次 5按需服用 6其他)")
        @Min(value = 1, message = "用药频率值必须在1-6之间")
        @Max(value = 6, message = "用药频率值必须在1-6之间")
        private Integer frequency;

        @ApiModelProperty("开始用药时间")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String startDate;

        @ApiModelProperty("结束用药时间")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String endDate;

        @ApiModelProperty("用药目的/适应症")
        @Size(max = 500, message = "用药目的不能超过500字符")
        private String indication;

        @ApiModelProperty("处方医生")
        @Size(max = 100, message = "处方医生姓名不能超过100字符")
        private String prescribingDoctor;

        @ApiModelProperty("用药状态(1正在使用 2已停用 3暂停使用)")
        @Min(value = 1, message = "用药状态值必须在1-3之间")
        @Max(value = 3, message = "用药状态值必须在1-3之间")
        private Integer medicationStatus;
    }

    /**
     * 生命体征DTO
     */
    @Data
    @ApiModel("生命体征DTO")
    public static class VitalSignsDTO {

        @ApiModelProperty("测量时间")
        @NotBlank(message = "测量时间不能为空")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "时间格式必须为yyyy-MM-dd HH:mm:ss")
        private String measurementDate;

        @ApiModelProperty("收缩压(mmHg)")
        @Min(value = 50, message = "收缩压不能小于50mmHg")
        @Max(value = 300, message = "收缩压不能大于300mmHg")
        private Integer systolicPressure;

        @ApiModelProperty("舒张压(mmHg)")
        @Min(value = 30, message = "舒张压不能小于30mmHg")
        @Max(value = 200, message = "舒张压不能大于200mmHg")
        private Integer diastolicPressure;

        @ApiModelProperty("心率(次/分)")
        @Min(value = 30, message = "心率不能小于30次/分")
        @Max(value = 250, message = "心率不能大于250次/分")
        private Integer heartRate;

        @ApiModelProperty("体温(℃)")
        @DecimalMin(value = "30.0", message = "体温不能小于30℃")
        @DecimalMax(value = "45.0", message = "体温不能大于45℃")
        private BigDecimal temperature;

        @ApiModelProperty("呼吸频率(次/分)")
        @Min(value = 5, message = "呼吸频率不能小于5次/分")
        @Max(value = 60, message = "呼吸频率不能大于60次/分")
        private Integer respiratoryRate;

        @ApiModelProperty("血氧饱和度(%)")
        @DecimalMin(value = "50.0", message = "血氧饱和度不能小于50%")
        @DecimalMax(value = "100.0", message = "血氧饱和度不能大于100%")
        private BigDecimal oxygenSaturation;

        @ApiModelProperty("疼痛评分(0-10分)")
        @Min(value = 0, message = "疼痛评分不能小于0分")
        @Max(value = 10, message = "疼痛评分不能大于10分")
        private Integer painScore;

        @ApiModelProperty("测量环境(1医院 2家庭 3体检中心 4其他)")
        @Min(value = 1, message = "测量环境值必须在1-4之间")
        @Max(value = 4, message = "测量环境值必须在1-4之间")
        private Integer measurementEnvironment;
    }

    /**
     * 检查结果DTO
     */
    @Data
    @ApiModel("检查结果DTO")
    public static class TestResultDTO {

        @ApiModelProperty("检查类型(1血液检查 2尿液检查 3影像检查 4心电图 5病理检查 6其他)")
        @NotNull(message = "检查类型不能为空")
        @Min(value = 1, message = "检查类型值必须在1-6之间")
        @Max(value = 6, message = "检查类型值必须在1-6之间")
        private Integer testType;

        @ApiModelProperty("检查项目名称")
        @NotBlank(message = "检查项目名称不能为空")
        @Size(max = 200, message = "检查项目名称不能超过200字符")
        private String testName;

        @ApiModelProperty("检查日期")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String testDate;

        @ApiModelProperty("检查医院")
        @Size(max = 200, message = "检查医院名称不能超过200字符")
        private String testHospital;

        @ApiModelProperty("检查结果值")
        @Size(max = 2000, message = "检查结果值不能超过2000字符")
        private String testValue;

        @ApiModelProperty("数值结果")
        private BigDecimal numericValue;

        @ApiModelProperty("结果单位")
        @Size(max = 50, message = "结果单位不能超过50字符")
        private String unit;

        @ApiModelProperty("参考范围")
        @Size(max = 200, message = "参考范围不能超过200字符")
        private String referenceRange;

        @ApiModelProperty("结果状态(1正常 2异常偏高 3异常偏低 4临界值 5无法判断)")
        @Min(value = 1, message = "结果状态值必须在1-5之间")
        @Max(value = 5, message = "结果状态值必须在1-5之间")
        private Integer resultStatus;

        @ApiModelProperty("是否紧急结果(0否 1是)")
        @Min(value = 0, message = "紧急结果标识必须是0或1")
        @Max(value = 1, message = "紧急结果标识必须是0或1")
        private Integer isUrgent;
    }

    /**
     * 疫苗接种DTO
     */
    @Data
    @ApiModel("疫苗接种DTO")
    public static class VaccinationDTO {

        @ApiModelProperty("疫苗名称")
        @NotBlank(message = "疫苗名称不能为空")
        @Size(max = 200, message = "疫苗名称不能超过200字符")
        private String vaccineName;

        @ApiModelProperty("疫苗类型(1常规疫苗 2应急疫苗 3旅行疫苗 4职业疫苗)")
        @Min(value = 1, message = "疫苗类型值必须在1-4之间")
        @Max(value = 4, message = "疫苗类型值必须在1-4之间")
        private Integer vaccineType;

        @ApiModelProperty("接种日期")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String vaccinationDate;

        @ApiModelProperty("接种剂次(第几针)")
        @Min(value = 1, message = "接种剂次必须大于0")
        @Max(value = 10, message = "接种剂次不能超过10")
        private Integer doseNumber;

        @ApiModelProperty("总剂次数")
        @Min(value = 1, message = "总剂次数必须大于0")
        @Max(value = 10, message = "总剂次数不能超过10")
        private Integer totalDoses;

        @ApiModelProperty("接种机构")
        @Size(max = 200, message = "接种机构名称不能超过200字符")
        private String vaccinationSite;

        @ApiModelProperty("接种反应(0无反应 1轻微反应 2中度反应 3严重反应)")
        @Min(value = 0, message = "接种反应值必须在0-3之间")
        @Max(value = 3, message = "接种反应值必须在0-3之间")
        private Integer reaction;

        @ApiModelProperty("是否完成全程接种(0否 1是)")
        @Min(value = 0, message = "完成状态必须是0或1")
        @Max(value = 1, message = "完成状态必须是0或1")
        private Integer isCompleted;
    }

    /**
     * 治疗方案DTO
     */
    @Data
    @ApiModel("治疗方案DTO")
    public static class TreatmentPlanDTO {

        @ApiModelProperty("方案名称")
        @NotBlank(message = "方案名称不能为空")
        @Size(max = 200, message = "方案名称不能超过200字符")
        private String planName;

        @ApiModelProperty("主要诊断")
        @Size(max = 500, message = "主要诊断不能超过500字符")
        private String primaryDiagnosis;

        @ApiModelProperty("治疗目标")
        @Size(max = 1000, message = "治疗目标不能超过1000字符")
        private String treatmentGoals;

        @ApiModelProperty("治疗方案类型(1药物治疗 2手术治疗 3物理治疗 4心理治疗 5综合治疗)")
        @Min(value = 1, message = "治疗方案类型值必须在1-5之间")
        @Max(value = 5, message = "治疗方案类型值必须在1-5之间")
        private Integer planType;

        @ApiModelProperty("制定日期")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String planDate;

        @ApiModelProperty("计划开始日期")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String startDate;

        @ApiModelProperty("计划结束日期")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为yyyy-MM-dd")
        private String endDate;

        @ApiModelProperty("制定医生")
        @Size(max = 100, message = "制定医生姓名不能超过100字符")
        private String planningDoctor;

        @ApiModelProperty("治疗医院")
        @Size(max = 200, message = "治疗医院名称不能超过200字符")
        private String treatmentHospital;

        @ApiModelProperty("方案状态(1计划中 2进行中 3已完成 4已暂停 5已取消)")
        @Min(value = 1, message = "方案状态值必须在1-5之间")
        @Max(value = 5, message = "方案状态值必须在1-5之间")
        private Integer planStatus;

        @ApiModelProperty("治疗进度(%)")
        @Min(value = 0, message = "治疗进度不能小于0%")
        @Max(value = 100, message = "治疗进度不能大于100%")
        private Integer progress;
    }
}
