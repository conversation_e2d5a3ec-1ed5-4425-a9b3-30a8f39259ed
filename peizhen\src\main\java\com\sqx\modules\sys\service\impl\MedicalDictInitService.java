package com.sqx.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sqx.modules.sys.entity.SysDictEntity;
import com.sqx.modules.sys.service.SysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 医疗档案数据字典初始化服务
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class MedicalDictInitService {

    @Autowired
    private SysDictService sysDictService;

    /**
     * 初始化医疗档案相关数据字典
     */
    @Transactional
    public void initMedicalDict() {
        // 清理已存在的医疗相关字典数据
        cleanExistingMedicalDict();
        
        // 初始化各类医疗字典
        initPatientRiskLevel();
        initHealthStatusLevel();
        initMedicalHistoryType();
        initAllergyType();
        initAllergySeverity();
        initMedicationType();
        initMedicationStatus();
        initTestType();
        initTestResultStatus();
        initVaccineType();
        initVaccinationReaction();
        initTreatmentPlanType();
        initTreatmentPlanStatus();
        initMaritalStatus();
        initInsuranceType();
    }

    /**
     * 清理已存在的医疗相关字典数据
     */
    private void cleanExistingMedicalDict() {
        String[] medicalTypes = {
            "patient_risk_level", "health_status_level", "medical_history_type",
            "allergy_type", "allergy_severity", "medication_type", "medication_status",
            "test_type", "test_result_status", "vaccine_type", "vaccination_reaction",
            "treatment_plan_type", "treatment_plan_status", "marital_status", "insurance_type"
        };
        
        for (String type : medicalTypes) {
            // 删除该类型的所有字典项
            sysDictService.remove(new QueryWrapper<SysDictEntity>().eq("type", type));
        }
    }

    /**
     * 创建字典项的通用方法
     */
    private Long createDictItem(String name, String type, String code, String value, Integer orderNum, String remark, Long parentId) {
        SysDictEntity dict = new SysDictEntity();
        dict.setName(name);
        dict.setType(type);
        dict.setCode(code);
        dict.setValue(value);
        dict.setOrderNum(orderNum);
        dict.setRemark(remark);
        dict.setParentId(parentId);
        
        sysDictService.save(dict);
        return dict.getId();
    }

    /**
     * 创建字典类型及其子项的通用方法
     */
    private void createDictTypeWithItems(String typeName, String type, Map<String, String> items, String typeRemark) {
        // 创建父级字典类型
        Long parentId = createDictItem(typeName, type, null, null, 1, typeRemark, 0L);
        
        // 创建子级字典项
        int order = 1;
        for (Map.Entry<String, String> entry : items.entrySet()) {
            createDictItem(entry.getValue(), type, entry.getKey(), entry.getValue(), order++, entry.getValue(), parentId);
        }
    }

    /**
     * 1. 患者风险等级
     */
    private void initPatientRiskLevel() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "低风险");
        items.put("2", "中风险");
        items.put("3", "高风险");
        
        createDictTypeWithItems("患者风险等级", "patient_risk_level", items, "患者医疗风险等级分类");
    }

    /**
     * 2. 健康状态评级
     */
    private void initHealthStatusLevel() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "优秀");
        items.put("2", "良好");
        items.put("3", "一般");
        items.put("4", "较差");
        items.put("5", "差");
        
        createDictTypeWithItems("健康状态评级", "health_status_level", items, "患者整体健康状况评级");
    }

    /**
     * 3. 病史类型
     */
    private void initMedicalHistoryType() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "既往史");
        items.put("2", "现病史");
        items.put("3", "家族史");
        items.put("4", "过敏史");
        items.put("5", "手术史");
        
        createDictTypeWithItems("病史类型", "medical_history_type", items, "医疗病史记录类型分类");
    }

    /**
     * 4. 过敏类型
     */
    private void initAllergyType() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "药物过敏");
        items.put("2", "食物过敏");
        items.put("3", "环境过敏");
        items.put("4", "接触过敏");
        items.put("5", "其他过敏");
        
        createDictTypeWithItems("过敏类型", "allergy_type", items, "过敏反应类型分类");
    }

    /**
     * 5. 过敏严重程度
     */
    private void initAllergySeverity() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "轻微");
        items.put("2", "中度");
        items.put("3", "严重");
        items.put("4", "危及生命");
        
        createDictTypeWithItems("过敏严重程度", "allergy_severity", items, "过敏反应严重程度分级");
    }

    /**
     * 6. 药物分类
     */
    private void initMedicationType() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "处方药");
        items.put("2", "非处方药");
        items.put("3", "中药");
        items.put("4", "生物制剂");
        items.put("5", "疫苗");
        
        createDictTypeWithItems("药物分类", "medication_type", items, "药物类型分类");
    }

    /**
     * 7. 用药状态
     */
    private void initMedicationStatus() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "正在使用");
        items.put("2", "已停用");
        items.put("3", "暂停使用");
        
        createDictTypeWithItems("用药状态", "medication_status", items, "患者用药状态");
    }

    /**
     * 8. 检查类型
     */
    private void initTestType() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "血液检查");
        items.put("2", "影像检查");
        items.put("3", "心电检查");
        items.put("4", "内镜检查");
        items.put("5", "病理检查");
        
        createDictTypeWithItems("检查类型", "test_type", items, "医疗检查项目类型");
    }

    /**
     * 9. 检查结果状态
     */
    private void initTestResultStatus() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "正常");
        items.put("2", "异常偏高");
        items.put("3", "异常偏低");
        items.put("4", "临界值");
        items.put("5", "严重异常");
        
        createDictTypeWithItems("检查结果状态", "test_result_status", items, "检查结果状态分类");
    }

    /**
     * 10. 疫苗类型
     */
    private void initVaccineType() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "新冠疫苗");
        items.put("2", "流感疫苗");
        items.put("3", "乙肝疫苗");
        items.put("4", "HPV疫苗");
        items.put("5", "其他疫苗");
        
        createDictTypeWithItems("疫苗类型", "vaccine_type", items, "疫苗种类分类");
    }

    /**
     * 11. 接种反应
     */
    private void initVaccinationReaction() {
        Map<String, String> items = new HashMap<>();
        items.put("0", "无反应");
        items.put("1", "轻微反应");
        items.put("2", "中度反应");
        items.put("3", "严重反应");
        
        createDictTypeWithItems("接种反应", "vaccination_reaction", items, "疫苗接种反应分类");
    }

    /**
     * 12. 治疗方案类型
     */
    private void initTreatmentPlanType() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "药物治疗");
        items.put("2", "手术治疗");
        items.put("3", "物理治疗");
        items.put("4", "心理治疗");
        items.put("5", "综合治疗");
        
        createDictTypeWithItems("治疗方案类型", "treatment_plan_type", items, "治疗方案类型分类");
    }

    /**
     * 13. 治疗方案状态
     */
    private void initTreatmentPlanStatus() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "计划中");
        items.put("2", "进行中");
        items.put("3", "已完成");
        items.put("4", "暂停");
        items.put("5", "终止");
        
        createDictTypeWithItems("治疗方案状态", "treatment_plan_status", items, "治疗方案执行状态");
    }

    /**
     * 14. 婚姻状况
     */
    private void initMaritalStatus() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "未婚");
        items.put("2", "已婚");
        items.put("3", "离异");
        items.put("4", "丧偶");
        
        createDictTypeWithItems("婚姻状况", "marital_status", items, "患者婚姻状况");
    }

    /**
     * 15. 医保类型
     */
    private void initInsuranceType() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "城镇职工");
        items.put("2", "城镇居民");
        items.put("3", "新农合");
        items.put("4", "商业保险");
        items.put("5", "自费");
        
        createDictTypeWithItems("医保类型", "insurance_type", items, "医疗保险类型");
    }
}
