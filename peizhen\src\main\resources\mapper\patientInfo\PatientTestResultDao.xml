<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientTestResultDao">

    <!-- 分页查询患者检查结果 -->
    <select id="getTestResultList" resultType="com.sqx.modules.patientInfo.entity.PatientTestResult">
        SELECT * FROM patient_test_result 
        WHERE patient_id = #{patientId} AND is_delete = 0
        <if test="testType != null">
            AND test_type = #{testType}
        </if>
        <if test="startDate != null and startDate != ''">
            AND test_date >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND test_date &lt;= #{endDate}
        </if>
        ORDER BY test_date DESC, create_time DESC
    </select>

    <!-- 获取患者最近检查结果 -->
    <select id="getRecentByPatient" resultType="com.sqx.modules.patientInfo.entity.PatientTestResult">
        SELECT * FROM patient_test_result 
        WHERE patient_id = #{patientId} AND is_delete = 0
        ORDER BY test_date DESC, create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取异常检查结果 -->
    <select id="getAbnormalResults" resultType="com.sqx.modules.patientInfo.entity.PatientTestResult">
        SELECT * FROM patient_test_result 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND result_status IN (2, 3, 4)  -- 异常偏高、异常偏低、临界值
        ORDER BY test_date DESC, create_time DESC
    </select>

    <!-- 根据检查项目名称搜索 -->
    <select id="searchByTestName" resultType="com.sqx.modules.patientInfo.entity.PatientTestResult">
        SELECT * FROM patient_test_result 
        WHERE patient_id = #{patientId} AND is_delete = 0
        AND test_name LIKE CONCAT('%', #{testName}, '%')
        ORDER BY test_date DESC, create_time DESC
    </select>

    <!-- 获取紧急检查结果 -->
    <select id="getUrgentResults" resultType="com.sqx.modules.patientInfo.entity.PatientTestResult">
        SELECT * FROM patient_test_result 
        WHERE patient_id = #{patientId} AND is_urgent = 1 AND is_delete = 0
        ORDER BY test_date DESC, create_time DESC
    </select>

</mapper>
