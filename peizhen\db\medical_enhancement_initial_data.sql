-- ========================================
-- 患者医疗档案增强功能 - 初始数据脚本
-- 创建时间: 2024-08-05
-- 描述: 为医疗档案系统插入初始数据和数据字典
-- ========================================

-- 1. 数据字典类型定义
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_time`) VALUES
('患者风险等级', 'patient_risk_level', 1, NOW()),
('健康状态评级', 'health_status_level', 1, NOW()),
('病史类型', 'medical_history_type', 1, NOW()),
('过敏类型', 'allergy_type', 1, NOW()),
('过敏严重程度', 'allergy_severity', 1, NOW()),
('药物分类', 'medication_type', 1, NOW()),
('用药状态', 'medication_status', 1, NOW()),
('检查类型', 'test_type', 1, NOW()),
('检查结果状态', 'test_result_status', 1, NOW()),
('疫苗类型', 'vaccine_type', 1, NOW()),
('接种反应', 'vaccination_reaction', 1, NOW()),
('治疗方案类型', 'treatment_plan_type', 1, NOW()),
('治疗方案状态', 'treatment_plan_status', 1, NOW()),
('婚姻状况', 'marital_status', 1, NOW()),
('医保类型', 'insurance_type', 1, NOW());

-- 2. 患者风险等级数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '低风险', '1', 'patient_risk_level', '', 'success', 'Y', 1, NOW()),
(2, '中风险', '2', 'patient_risk_level', '', 'warning', 'N', 1, NOW()),
(3, '高风险', '3', 'patient_risk_level', '', 'danger', 'N', 1, NOW());

-- 3. 健康状态评级数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '优秀', '1', 'health_status_level', '', 'success', 'N', 1, NOW()),
(2, '良好', '2', 'health_status_level', '', 'primary', 'Y', 1, NOW()),
(3, '一般', '3', 'health_status_level', '', 'info', 'N', 1, NOW()),
(4, '较差', '4', 'health_status_level', '', 'warning', 'N', 1, NOW()),
(5, '很差', '5', 'health_status_level', '', 'danger', 'N', 1, NOW());

-- 4. 病史类型数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '既往病史', '1', 'medical_history_type', '', 'primary', 'Y', 1, NOW()),
(2, '手术史', '2', 'medical_history_type', '', 'info', 'N', 1, NOW()),
(3, '外伤史', '3', 'medical_history_type', '', 'warning', 'N', 1, NOW()),
(4, '输血史', '4', 'medical_history_type', '', 'danger', 'N', 1, NOW()),
(5, '家族史', '5', 'medical_history_type', '', 'success', 'N', 1, NOW());

-- 5. 过敏类型数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '药物过敏', '1', 'allergy_type', '', 'danger', 'Y', 1, NOW()),
(2, '食物过敏', '2', 'allergy_type', '', 'warning', 'N', 1, NOW()),
(3, '环境过敏', '3', 'allergy_type', '', 'info', 'N', 1, NOW()),
(4, '接触性过敏', '4', 'allergy_type', '', 'primary', 'N', 1, NOW()),
(5, '其他过敏', '5', 'allergy_type', '', 'default', 'N', 1, NOW());

-- 6. 过敏严重程度数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '轻微', '1', 'allergy_severity', '', 'success', 'Y', 1, NOW()),
(2, '中度', '2', 'allergy_severity', '', 'warning', 'N', 1, NOW()),
(3, '严重', '3', 'allergy_severity', '', 'danger', 'N', 1, NOW()),
(4, '危及生命', '4', 'allergy_severity', '', 'danger', 'N', 1, NOW());

-- 7. 药物分类数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '处方药', '1', 'medication_type', '', 'primary', 'Y', 1, NOW()),
(2, '非处方药', '2', 'medication_type', '', 'info', 'N', 1, NOW()),
(3, '中药', '3', 'medication_type', '', 'success', 'N', 1, NOW()),
(4, '生物制品', '4', 'medication_type', '', 'warning', 'N', 1, NOW());

-- 8. 用药状态数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '正在使用', '1', 'medication_status', '', 'success', 'Y', 1, NOW()),
(2, '已停用', '2', 'medication_status', '', 'danger', 'N', 1, NOW()),
(3, '暂停使用', '3', 'medication_status', '', 'warning', 'N', 1, NOW());

-- 9. 检查类型数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '血液检查', '1', 'test_type', '', 'danger', 'Y', 1, NOW()),
(2, '尿液检查', '2', 'test_type', '', 'warning', 'N', 1, NOW()),
(3, '影像检查', '3', 'test_type', '', 'primary', 'N', 1, NOW()),
(4, '心电图', '4', 'test_type', '', 'info', 'N', 1, NOW()),
(5, '病理检查', '5', 'test_type', '', 'success', 'N', 1, NOW()),
(6, '其他检查', '6', 'test_type', '', 'default', 'N', 1, NOW());

-- 10. 检查结果状态数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '正常', '1', 'test_result_status', '', 'success', 'Y', 1, NOW()),
(2, '异常偏高', '2', 'test_result_status', '', 'warning', 'N', 1, NOW()),
(3, '异常偏低', '3', 'test_result_status', '', 'info', 'N', 1, NOW()),
(4, '临界值', '4', 'test_result_status', '', 'primary', 'N', 1, NOW()),
(5, '无法判断', '5', 'test_result_status', '', 'default', 'N', 1, NOW());

-- 11. 疫苗类型数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '常规疫苗', '1', 'vaccine_type', '', 'primary', 'Y', 1, NOW()),
(2, '应急疫苗', '2', 'vaccine_type', '', 'danger', 'N', 1, NOW()),
(3, '旅行疫苗', '3', 'vaccine_type', '', 'info', 'N', 1, NOW()),
(4, '职业疫苗', '4', 'vaccine_type', '', 'warning', 'N', 1, NOW());

-- 12. 接种反应数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(0, '无反应', '0', 'vaccination_reaction', '', 'success', 'Y', 1, NOW()),
(1, '轻微反应', '1', 'vaccination_reaction', '', 'info', 'N', 1, NOW()),
(2, '中度反应', '2', 'vaccination_reaction', '', 'warning', 'N', 1, NOW()),
(3, '严重反应', '3', 'vaccination_reaction', '', 'danger', 'N', 1, NOW());

-- 13. 治疗方案类型数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '药物治疗', '1', 'treatment_plan_type', '', 'primary', 'Y', 1, NOW()),
(2, '手术治疗', '2', 'treatment_plan_type', '', 'danger', 'N', 1, NOW()),
(3, '物理治疗', '3', 'treatment_plan_type', '', 'info', 'N', 1, NOW()),
(4, '心理治疗', '4', 'treatment_plan_type', '', 'success', 'N', 1, NOW()),
(5, '综合治疗', '5', 'treatment_plan_type', '', 'warning', 'N', 1, NOW());

-- 14. 治疗方案状态数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '计划中', '1', 'treatment_plan_status', '', 'info', 'Y', 1, NOW()),
(2, '进行中', '2', 'treatment_plan_status', '', 'primary', 'N', 1, NOW()),
(3, '已完成', '3', 'treatment_plan_status', '', 'success', 'N', 1, NOW()),
(4, '已暂停', '4', 'treatment_plan_status', '', 'warning', 'N', 1, NOW()),
(5, '已取消', '5', 'treatment_plan_status', '', 'danger', 'N', 1, NOW());

-- 15. 婚姻状况数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '未婚', '1', 'marital_status', '', 'primary', 'Y', 1, NOW()),
(2, '已婚', '2', 'marital_status', '', 'success', 'N', 1, NOW()),
(3, '离异', '3', 'marital_status', '', 'warning', 'N', 1, NOW()),
(4, '丧偶', '4', 'marital_status', '', 'info', 'N', 1, NOW());

-- 16. 医保类型数据字典
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_time`) VALUES
(1, '城镇职工', '1', 'insurance_type', '', 'primary', 'Y', 1, NOW()),
(2, '城镇居民', '2', 'insurance_type', '', 'info', 'N', 1, NOW()),
(3, '新农合', '3', 'insurance_type', '', 'success', 'N', 1, NOW()),
(4, '商业保险', '4', 'insurance_type', '', 'warning', 'N', 1, NOW()),
(5, '自费', '5', 'insurance_type', '', 'danger', 'N', 1, NOW());

-- 初始数据插入完成提示
SELECT '医疗档案系统初始数据插入完成！' AS data_status;
