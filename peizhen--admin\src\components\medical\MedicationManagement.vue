<template>
  <div class="medication-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="search-item">
          <span>患者姓名：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入患者姓名"
            v-model="searchForm.patientName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>药物名称：</span>
          <el-input 
            style="width: 200px;" 
            @keydown.enter.native="handleSearch" 
            placeholder="请输入药物名称"
            v-model="searchForm.medicationName"
            clearable>
          </el-input>
        </div>
        <div class="search-item">
          <span>药物类型：</span>
          <el-select style="width: 200px;" v-model="searchForm.medicationType" placeholder="请选择药物类型" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in medicationTypeOptions" :key="item.code" :label="item.value" :value="item.code">
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span>用药状态：</span>
          <el-select style="width: 200px;" v-model="searchForm.medicationStatus" placeholder="请选择用药状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in medicationStatusOptions" :key="item.code" :label="item.value" :value="item.code">
            </el-option>
          </el-select>
        </div>
      </div>
      
      <div class="action-buttons">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="handleSearch">
          查询
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd">
          添加用药记录
        </el-button>
        <el-button size="mini" type="success" icon="el-icon-download" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 子标签页 -->
    <el-tabs v-model="activeSubTab" type="card">
      <el-tab-pane label="用药记录" name="medication">
        <el-table v-loading="loading" :data="tableData.records" border stripe>
          <el-table-column prop="patientName" label="患者姓名" width="120">
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                style="color: #409EFF;background: #fff;border: none;padding: 0;" 
                type="primary"
                @click="handleViewPatient(scope.row)">
                {{ scope.row.patientName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="medicationName" label="药物名称" width="150">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.medicationName" placement="top">
                <span class="text-ellipsis">{{ scope.row.medicationName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="brandName" label="商品名" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.brandName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="medicationType" label="药物类型" width="100">
            <template slot-scope="scope">
              <el-tag :type="getMedicationTypeColor(scope.row.medicationType)">
                {{ getMedicationTypeText(scope.row.medicationType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dosageForm" label="剂型" width="100">
            <template slot-scope="scope">
              <span>{{ getDosageFormText(scope.row.dosageForm) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="singleDose" label="单次剂量" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.singleDose }}{{ scope.row.doseUnit || '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="frequency" label="用药频率" width="100">
            <template slot-scope="scope">
              <span>{{ getFrequencyText(scope.row.frequency) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="medicationStatus" label="用药状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getMedicationStatusColor(scope.row.medicationStatus)">
                {{ getMedicationStatusText(scope.row.medicationStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
          <el-table-column prop="endDate" label="结束日期" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.endDate || '持续用药' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="prescribingDoctor" label="处方医生" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.prescribingDoctor || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" type="info" @click="handleViewDetail(scope.row)">详情</el-button>
              <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="检查记录" name="test">
        <test-result-table 
          :loading="testLoading"
          :table-data="testTableData"
          @edit="handleEditTest"
          @delete="handleDeleteTest"
          @view-detail="handleViewTestDetail"
          @view-patient="handleViewPatient">
        </test-result-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination 
        @size-change="handleSizeChange" 
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 30, 40]" 
        :page-size="pagination.limit" 
        :current-page="pagination.page"
        layout="total,sizes, prev, pager, next,jumper" 
        :total="tableData.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import TestResultTable from './TestResultTable.vue'

export default {
  name: 'MedicationManagement',
  components: {
    TestResultTable
  },
  props: {
    medicationTypeOptions: {
      type: Array,
      default: () => []
    },
    medicationStatusOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      testLoading: false,
      activeSubTab: 'medication',
      searchForm: {
        patientName: '',
        medicationName: '',
        medicationType: '',
        medicationStatus: ''
      },
      pagination: {
        page: 1,
        limit: 10
      },
      tableData: {
        records: [],
        total: 0
      },
      testTableData: {
        records: [],
        total: 0
      }
    }
  },
  watch: {
    activeSubTab(newTab) {
      if (newTab === 'medication') {
        this.loadMedicationData()
      } else if (newTab === 'test') {
        this.loadTestData()
      }
    }
  },
  methods: {
    // 加载用药数据
    async loadMedicationData() {
      this.loading = true
      try {
        const response = await this.$http({
          url: this.$http.adornUrl('admin/patientMedical/getMedicationList'),
          method: 'get',
          params: this.$http.adornParams({
            page: this.pagination.page,
            limit: this.pagination.limit,
            ...this.searchForm
          })
        })
        
        if (response.data && response.data.code === 0) {
          this.tableData = response.data.data
        }
      } catch (error) {
        this.$message.error('加载用药数据失败')
      } finally {
        this.loading = false
      }
    },

    // 加载检查数据
    async loadTestData() {
      this.testLoading = true
      try {
        const response = await this.$http({
          url: this.$http.adornUrl('admin/patientMedical/getTestResultList'),
          method: 'get',
          params: this.$http.adornParams({
            page: this.pagination.page,
            limit: this.pagination.limit,
            patientName: this.searchForm.patientName
          })
        })
        
        if (response.data && response.data.code === 0) {
          this.testTableData = response.data.data
        }
      } catch (error) {
        this.$message.error('加载检查数据失败')
      } finally {
        this.testLoading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      if (this.activeSubTab === 'medication') {
        this.loadMedicationData()
      } else {
        this.loadTestData()
      }
    },

    // 重置
    handleReset() {
      this.searchForm = {
        patientName: '',
        medicationName: '',
        medicationType: '',
        medicationStatus: ''
      }
      this.pagination.page = 1
      if (this.activeSubTab === 'medication') {
        this.loadMedicationData()
      } else {
        this.loadTestData()
      }
    },

    // 分页处理
    handleSizeChange(val) {
      this.pagination.limit = val
      if (this.activeSubTab === 'medication') {
        this.loadMedicationData()
      } else {
        this.loadTestData()
      }
    },

    handleCurrentChange(val) {
      this.pagination.page = val
      if (this.activeSubTab === 'medication') {
        this.loadMedicationData()
      } else {
        this.loadTestData()
      }
    },

    // 操作处理
    handleAdd() {
      this.$emit('add-medication')
    },

    handleEdit(row) {
      this.$emit('edit-medication', row)
    },

    handleDelete(row) {
      this.$emit('delete-medication', row)
    },

    handleViewDetail(row) {
      this.$emit('view-medication-detail', row)
    },

    handleViewPatient(row) {
      this.$emit('view-patient', row)
    },

    handleExport() {
      this.$emit('export-medication-data', this.searchForm)
    },

    // 检查记录操作
    handleEditTest(row) {
      this.$emit('edit-test-result', row)
    },

    handleDeleteTest(row) {
      this.$emit('delete-test-result', row)
    },

    handleViewTestDetail(row) {
      this.$emit('view-test-detail', row)
    },

    // 工具方法
    getMedicationTypeText(type) {
      if (!type) return '-'
      const item = this.medicationTypeOptions.find(opt => opt.code == type)
      return item ? item.value : '-'
    },

    getMedicationTypeColor(type) {
      switch(type) {
        case 1: return 'danger'   // 处方药
        case 2: return 'warning'  // 非处方药
        case 3: return 'success'  // 中药
        case 4: return 'primary'  // 生物制品
        default: return 'info'
      }
    },

    getMedicationStatusText(status) {
      if (!status) return '-'
      const item = this.medicationStatusOptions.find(opt => opt.code == status)
      return item ? item.value : '-'
    },

    getMedicationStatusColor(status) {
      switch(status) {
        case 1: return 'success'  // 正在使用
        case 2: return 'info'     // 已停用
        case 3: return 'warning'  // 暂停使用
        default: return 'info'
      }
    },

    getDosageFormText(form) {
      const formMap = {
        1: '片剂',
        2: '胶囊',
        3: '注射剂',
        4: '口服液',
        5: '外用药',
        6: '其他'
      }
      return formMap[form] || '-'
    },

    getFrequencyText(frequency) {
      const frequencyMap = {
        1: '每日一次',
        2: '每日两次',
        3: '每日三次',
        4: '每日四次',
        5: '按需服用',
        6: '其他'
      }
      return frequencyMap[frequency] || '-'
    }
  },

  mounted() {
    this.loadMedicationData()
  }
}
</script>

<style scoped>
.medication-management {
  padding: 20px;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-item span {
  white-space: nowrap;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.pagination-section {
  text-align: center;
  margin-top: 20px;
}

.text-ellipsis {
  display: inline-block;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
