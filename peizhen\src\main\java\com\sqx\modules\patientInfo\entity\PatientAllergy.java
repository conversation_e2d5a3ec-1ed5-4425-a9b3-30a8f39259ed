package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者过敏信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@TableName("patient_allergy")
public class PatientAllergy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 过敏记录id
     */
    @TableId(value = "allergy_id", type = IdType.AUTO)
    @ApiModelProperty("过敏记录id")
    private Long allergyId;

    /**
     * 患者id
     */
    @ApiModelProperty("患者id")
    private Long patientId;

    /**
     * 过敏类型(1药物过敏 2食物过敏 3环境过敏 4接触性过敏 5其他)
     */
    @ApiModelProperty("过敏类型(1药物过敏 2食物过敏 3环境过敏 4接触性过敏 5其他)")
    private Integer allergyType;

    /**
     * 过敏原名称
     */
    @ApiModelProperty("过敏原名称")
    @TableField(condition = SqlCondition.LIKE)
    private String allergenName;

    /**
     * 过敏原编码(药物编码/食物编码等)
     */
    @ApiModelProperty("过敏原编码(药物编码/食物编码等)")
    private String allergenCode;

    /**
     * 过敏严重程度(1轻微 2中度 3严重 4危及生命)
     */
    @ApiModelProperty("过敏严重程度(1轻微 2中度 3严重 4危及生命)")
    private Integer severity;

    /**
     * 过敏反应症状
     */
    @ApiModelProperty("过敏反应症状")
    private String symptoms;

    /**
     * 首次发现时间
     */
    @ApiModelProperty("首次发现时间")
    private String firstDiscoveryDate;

    /**
     * 最后一次反应时间
     */
    @ApiModelProperty("最后一次反应时间")
    private String lastReactionDate;

    /**
     * 反应持续时间(分钟)
     */
    @ApiModelProperty("反应持续时间(分钟)")
    private Integer reactionDuration;

    /**
     * 处理方式
     */
    @ApiModelProperty("处理方式")
    private String treatmentMethod;

    /**
     * 是否需要紧急处理(0否 1是)
     */
    @ApiModelProperty("是否需要紧急处理(0否 1是)")
    private Integer requiresEmergencyTreatment;

    /**
     * 是否确认过敏(0疑似 1确认)
     */
    @ApiModelProperty("是否确认过敏(0疑似 1确认)")
    private Integer isConfirmed;

    /**
     * 确认方式(1临床观察 2皮肤试验 3血液检测 4激发试验)
     */
    @ApiModelProperty("确认方式(1临床观察 2皮肤试验 3血液检测 4激发试验)")
    private Integer confirmationMethod;

    /**
     * 确认医院
     */
    @ApiModelProperty("确认医院")
    private String confirmationHospital;

    /**
     * 确认医生
     */
    @ApiModelProperty("确认医生")
    private String confirmationDoctor;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String notes;

    /**
     * 相关检测报告路径
     */
    @ApiModelProperty("相关检测报告路径")
    private String reportPath;

    /**
     * 是否当前活跃(0否 1是)
     */
    @ApiModelProperty("是否当前活跃(0否 1是)")
    private Integer isActive;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(0否 1是)
     */
    @ApiModelProperty("是否删除(0否 1是)")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;
}
