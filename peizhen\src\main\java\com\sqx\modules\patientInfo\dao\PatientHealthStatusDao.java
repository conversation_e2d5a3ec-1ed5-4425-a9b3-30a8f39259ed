package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sqx.modules.patientInfo.entity.PatientHealthStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 患者健康状态 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Mapper
public interface PatientHealthStatusDao extends BaseMapper<PatientHealthStatus> {

    /**
     * 根据患者ID获取健康状态
     */
    PatientHealthStatus getByPatientId(@Param("patientId") Long patientId);

    /**
     * 更新或插入健康状态
     */
    int saveOrUpdate(PatientHealthStatus healthStatus);
}
